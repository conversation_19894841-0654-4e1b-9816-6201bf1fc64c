import logging

from celery import shared_task

from b2b.models import B2bApplicant
from b2b.services.pipedrive import PipedriveIntegration
from orders.models import Order

logger = logging.getLogger('cstm')


@shared_task
def send_b2b_applicant_to_pipedrive(applicant_pk: int) -> None:
    b2b_applicant = B2bApplicant.objects.get(pk=applicant_pk)
    PipedriveIntegration(applicant=b2b_applicant).add_applicant_to_pipedrive()


@shared_task
def update_pipedrive_account_after_order_payment(order_id: int) -> None:
    order = Order.objects.get(pk=order_id)
    PipedriveIntegration().update_pipedrive_after_payment(order)


@shared_task
def send_previous_orders_to_pipedrive(pipedrive_id: int, email: str) -> None:
    PipedriveIntegration().send_previous_orders_to_pipedrive(pipedrive_id, email)
