{% load static %}
<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>CSTM B2B product order</title>
        <link href={% static 'product_order_table_style.css' %} rel='stylesheet'>
    </head>
    <body>
      {% if table_data %}
        <h1>B2B product order</h1>
        <div id='download-button'>
            {% if cart_id %}
                <a href='{% url 'admin:product-order-table-download' cart_id %}' target='_blank' class='no-style-link'>Download table</a>
            {% else %}
                <a href='{% url 'admin:product-order-table-download' %}' target='_blank' class='no-style-link'>Download table</a>
            {% endif %}
        </div>
        <table>
            <thead>
                <tr>
                    <th>Gallery ID</th>
                    <th>Preview</th>
                    <th>Type</th>
                    <th>Category</th>
                    <th>Region</th>
                    <th>Regular price</th>
                    <th>Cogs (PLN)</th>
                    <th>Weight brutto</th>
                    <th>N.o. packs</th>
                </tr>
            </thead>
            <tbody>
                {% for furniture in table_data %}
                    <tr>
                        <td>
                            <a href="{{furniture.configurator_url}}" target='_blank'>
                                {{furniture.id}}
                            </a>
                        </td>
                        <td>
                            <img
                            src="{{ furniture.img }}"
                            alt="preview image"
                            />
                        </td>
                        <td>
                            {{furniture.type}}
                        </td>
                        <td>
                            {{furniture.category}}
                        </td>
                        <td>
                            {{furniture.region}}
                        </td>
                        <td>
                            {{furniture.price}}
                        </td>
                        <td>{{furniture.cogs}} zł</td>
                        <td>{{furniture.weight_brutto}} kg</td>
                        <td>{{furniture.packs_count}}</td>
                    </tr>
                {% endfor %}
            </tbody>
        </table>
      {% else %}
        <h1>Cart data not found</h1>
      {% endif %}
    </body>
</html>
