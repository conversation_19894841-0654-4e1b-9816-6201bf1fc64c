from django.db import models

from b2b.enums import DepartmentChoices
from custom.enums import LanguageEnum
from custom.models import Countries


class B2bApplicant(models.Model):
    marketing_permission = models.BooleanField()
    language = models.CharField(
        max_length=2,
        choices=LanguageEnum.choices,
        default=LanguageEnum.EN,
    )
    job_other = models.CharField(max_length=255, blank=True, null=True)
    why_tylko = models.TextField(
        blank=True,
        default='',
        help_text='Why do you want to join the Tylko Pro program?',
    )
    first_name = models.CharField(max_length=255)
    last_name = models.CharField(max_length=255)
    email = models.EmailField()
    departament = models.CharField(max_length=255, choices=DepartmentChoices.choices)
    company_name = models.CharField(max_length=255)
    country = models.CharField(
        max_length=200,
        choices=sorted(Countries.as_choices()),
        blank=True,
        null=True,
    )
    vat_number = models.Char<PERSON>ield(max_length=255)
    website = models.Char<PERSON>ield(max_length=255)
    free_sample_code = models.CharField(max_length=25, blank=True, null=True)
    promo_code = models.CharField(max_length=25, blank=True, null=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f'B2BApplicant: {self.first_name} {self.last_name} ({self.company_name})'
