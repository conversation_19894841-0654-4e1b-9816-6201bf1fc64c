from django.http import Http404
from django.urls import reverse

import pytest

from rest_framework import status
from rest_framework.test import (
    APIRequestFactory,
    force_authenticate,
)

from b2b.enums import DepartmentChoices
from b2b.models import B2bApplicant
from b2b.views import (
    B2bApplicantAPIView,
    B2BCartMixin,
)
from carts.choices import CartStatusChoices
from custom.enums import (
    LanguageEnum,
    ShelfType,
)


@pytest.mark.django_db
class TestB2bApplicantAPIView:
    url = reverse('b2b-form')
    view = B2bApplicantAPIView

    def test_b2b_applicant_create(
        self,
        api_client,
        user,
        item_discount_factory,
        user_factory,
        voucher_settings,
    ):
        item_discount_factory(shelf_type=ShelfType.TYPE03.value, value=20)
        item_discount_factory(furniture_type='sample_box', value=0)
        user_factory(username='admin')
        payload = {
            'marketing_permission': True,
            'job_other': 'Some job',
            'first_name': '<PERSON>',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'departament': DepartmentChoices.OTHER,
            'company_name': 'Example Corp.',
            'country': 'united_kingdom',
            'vat_number': '*********',
            'website': 'https://www.example.com',
        }

        assert B2bApplicant.objects.count() == 0

        request_factory = APIRequestFactory()
        request = request_factory.post(self.url, data=payload, format='json')
        request.user = user
        request.LANGUAGE_CODE = LanguageEnum.FR
        force_authenticate(request, user=user)
        response = self.view.as_view()(request)

        assert response.status_code == status.HTTP_201_CREATED
        assert B2bApplicant.objects.count() == 1
        applicant = B2bApplicant.objects.first()
        assert applicant.language == LanguageEnum.FR

    def test_b2b_applicant_create_without_marketing_permission(self, api_client, user):
        payload = {
            'marketing_permission': False,
            'job_other': 'Some job',
            'first_name': 'John',
            'last_name': 'Doe',
            'email': '<EMAIL>',
            'departament': DepartmentChoices.OTHER,
            'company_name': 'Example Corp.',
            'country': 'united_kingdom',
            'vat_number': '*********',
            'website': 'https://www.example.com',
        }

        response = api_client.post(self.url, data=payload, format='json')
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'marketing_permission' in response.data


@pytest.mark.django_db
class TestB2BCartMixin:
    def test_get_cart_with_latest_item_with_existing_cart(
        self,
        cart_factory,
    ):
        expected_cart = cart_factory(status=CartStatusChoices.ACTIVE)
        cart = B2BCartMixin()._get_cart_with_latest_item(None, expected_cart.id)

        assert expected_cart == cart

    def test_get_cart_with_latest_item_raise_404_when_cart_does_not_exist(
        self,
    ):
        with pytest.raises(Http404):
            B2BCartMixin()._get_cart_with_latest_item(None, 123123)

    def test_get_cart_with_latest_item_get_cart_with_latest_items(
        self, cart_factory, cart_item_factory, user, rf
    ):
        cart_factory(owner=user, status=CartStatusChoices.ACTIVE)
        cart_2 = cart_factory(owner=user, status=CartStatusChoices.ACTIVE)
        cart_3 = cart_factory(owner=user, status=CartStatusChoices.ACTIVE)

        cart_item_factory(cart=cart_3)
        cart_item_factory(cart=cart_2)

        request = rf.get(reverse('admin:product-order-table'))
        request.user = user

        assert cart_2 == B2BCartMixin()._get_cart_with_latest_item(request)
