import factory

from factory.django import DjangoModelFactory

from b2b.enums import DepartmentChoices
from b2b.models import B2bApplicant
from custom.enums import LanguageEnum
from custom.models import Countries


class B2bApplicantFactory(DjangoModelFactory):
    class Meta:
        model = B2bApplicant

    marketing_permission = True
    language = factory.Iterator(LanguageEnum.choices, getter=lambda choice: choice[0])
    job_other = factory.Faker('job')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    email = factory.Faker('email')
    departament = factory.Iterator(
        DepartmentChoices.choices,
        getter=lambda choice: choice[0],
    )
    company_name = factory.Faker('company')
    country = factory.Iterator(Countries.as_choices(), getter=lambda choice: choice[0])
    website = factory.Faker('url')
