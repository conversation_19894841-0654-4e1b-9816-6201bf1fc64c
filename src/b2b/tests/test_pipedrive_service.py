from datetime import datetime

import pytest
import requests_mock

from b2b.serializers import PipedrivePersonSerializer
from b2b.services.pipedrive import PipedriveIntegration
from orders.enums import OrderStatus


@pytest.mark.django_db
class TestPipedriveIntegration:
    @pytest.fixture
    def pipedrive_integration(self, b2b_applicant):
        return PipedriveIntegration(applicant=b2b_applicant)

    def test_create_organization(self, pipedrive_integration):
        with requests_mock.Mocker() as m:
            m.post(requests_mock.ANY, json={'data': {'id': 1}})

            organization_id = pipedrive_integration._create_organization()
        assert organization_id == 1

    def test_get_organisation_id_if_it_exists(self, pipedrive_integration):
        with requests_mock.Mocker() as m:
            m.get(requests_mock.ANY, json={'data': {'items': [{'item': {'id': 1}}]}})

            org_id = pipedrive_integration._get_organisation_id_if_it_exists()
        assert org_id == 1

    def test_get_organisation_id_if_it_exists_no_results(
        self,
        pipedrive_integration,
    ):
        with requests_mock.Mocker() as m:
            m.get(requests_mock.ANY, json={'data': {'items': []}})

            org_id = pipedrive_integration._get_organisation_id_if_it_exists()
        assert org_id is None

    def test_create_person(self, pipedrive_integration):
        with requests_mock.Mocker() as m:
            m.post(requests_mock.ANY, json={})
            pipedrive_integration._create_person(1)

            data = PipedrivePersonSerializer(
                instance=pipedrive_integration.applicant
            ).data
            data['org_id'] = 1
            assert m.called_once
            assert (
                m.last_request.url
                == f'{pipedrive_integration.base_url}/persons?api_token={pipedrive_integration.api_key}'
            )
            assert m.last_request.json() == data

    def test_send_previous_orders_to_pipedrive(
        self,
        order_factory,
    ):
        email = '<EMAIL>'
        order_factory(
            email=email,
            status=OrderStatus.DELIVERED,
            paid_at=datetime(2021, 12, 14),
        )
        with (requests_mock.Mocker() as m):
            m.post(requests_mock.ANY, json={})
            pipedrive_integration = PipedriveIntegration()
            pipedrive_integration.send_previous_orders_to_pipedrive(
                pipedrive_id=1,
                email=email,
            )
            assert m.called_once
            assert (
                m.last_request.url
                == f'{pipedrive_integration.base_url}/activities?api_token={pipedrive_integration.api_key}'
            )
