from django.contrib.admin import AdminSite
from django.urls import path

from b2b.views import (
    B2BProductOrder,
    B2BProductOrderDownload,
)


class B2bAdminPage(AdminSite):
    def get_b2b_admin_urls(self):
        return [
            path(
                'product-order-table/',
                B2BProductOrder.as_view(),
                name='product-order-table',
            ),
            path(
                'product-order-table/<int:cart_id>/',
                B2BProductOrder.as_view(),
                name='product-order-table',
            ),
            path(
                'product-order-table/download/',
                B2BProductOrderDownload.as_view(),
                name='product-order-table-download',
            ),
            path(
                'product-order-table/download/<int:cart_id>/',
                B2BProductOrderDownload.as_view(),
                name='product-order-table-download',
            ),
        ]


urlpatterns = B2bAdminPage().get_b2b_admin_urls()
