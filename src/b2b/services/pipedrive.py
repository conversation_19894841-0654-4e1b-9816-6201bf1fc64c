import logging

from decimal import Decimal
from typing import Optional
from urllib.parse import urlencode

from django.conf import settings
from django.db.models import QuerySet

import requests

from b2b.constants import PURCHASE_VALUE_API_KEY
from b2b.models import B2bApplicant
from b2b.serializers import (
    PipedriveOrganisationSerializer,
    PipedrivePersonSerializer,
)
from orders.enums import OrderStatus
from orders.models import Order

logger = logging.getLogger('cstm')


class PipedriveIntegration:
    api_key = settings.PIPEDRIVE_API_KEY
    base_url = 'https://tylkosa.pipedrive.com/api/v1'
    headers = {'Content-Type': 'application/json'}

    def __init__(self, applicant: Optional[B2bApplicant] = None) -> None:
        self.applicant = applicant

    def _post_request(self, endpoint: str, data: dict) -> dict:
        response = requests.post(
            url=f'{self.base_url}/{endpoint}?api_token={self.api_key}',
            json=data,
            headers=self.headers,
        )
        response.raise_for_status()
        return response.json()

    def _get_request(self, endpoint: str, params: Optional[dict]) -> dict:
        url = f'{self.base_url}/{endpoint}?api_token={self.api_key}'
        if params:
            url += f'&{urlencode(params)}'
        response = requests.get(url=url)
        response.raise_for_status()
        return response.json()

    def _put_request(self, endpoint: str, data: dict, pk: int) -> dict:
        response = requests.put(
            url=f'{self.base_url}/{endpoint}/{pk}?api_token={self.api_key}',
            json=data,
            headers=self.headers,
        )
        response.raise_for_status()
        return response.json()

    def _create_person(self, organization_id: int) -> None:
        data = self._get_person_data(organization_id)
        self._post_request('persons', data)

    def _get_person_data(self, organization_id: int) -> dict:
        data = PipedrivePersonSerializer(instance=self.applicant).data
        data['org_id'] = organization_id
        return data

    def _get_or_create_organization(self) -> int:
        return self._get_organisation_id_if_it_exists() or self._create_organization()

    def _create_organization(self) -> int:
        data = self._get_organisation_data()
        response = self._post_request('organizations', data)
        return response['data']['id']

    def _get_organisation_id_if_it_exists(self) -> Optional[str]:
        response = self._get_request(
            endpoint='organizations/search',
            params={
                'fields': 'name',
                'exact_match': 'true',
                'term': self.applicant.company_name,
            },
        )
        results = response['data']['items']
        if not results:
            return
        return results[0]['item']['id']

    def _get_organisation_data(self) -> dict:
        return PipedriveOrganisationSerializer(instance=self.applicant).data

    def add_applicant_to_pipedrive(self) -> None:
        organization_id = self._get_or_create_organization()
        self._create_person(organization_id)

    def _search_person(self, email_address: str) -> Optional[dict]:
        response = self._get_request(
            endpoint='persons/search',
            params={
                'exact_match': 'true',
                'term': email_address,
            },
        )
        if results := response['data']['items']:
            return results[0]['item']

    def _send_activity(
        self,
        order: Order,
        person_id: int,
        activity_type: str,
        done: int = 0,
    ) -> None:
        data = {
            'person_id': person_id,
            'due_date': order.paid_at.date().strftime('%Y-%m-%d'),
            'due_time': order.paid_at.time().strftime('%H:%M'),
            'type': activity_type,
            'subject': 'Purchase done',
            'note': f'<a href="{settings.SITE_URL}/cs/search?q_orderid_direct='
            f'{order.pk}">Link to cs panel (click)</a>Order Price: {order.total_price}',
            'done': done,
        }
        self._post_request('activities', data)

    def send_purchase_value(
        self,
        order: Order,
        person_id: int,
        purchase_value: str,
    ) -> None:
        data = {PURCHASE_VALUE_API_KEY: Decimal(purchase_value) + order.total_price}
        self._put_request('persons', data=data, pk=person_id)

    def update_pipedrive_after_payment(self, order: Order) -> None:
        """
        Handles the Pipedrive notification after an order has been paid.

        This method performs two main tasks:
        1. Sends a purchase activity to Pipedrive, indicating that a purchase has been
            made.
        2. Updates the total purchase value for the person associated with the order
            in Pipedrive.

        Args:
            order (Order): The order object that contains details of the purchase,
            including the email of the buyer, the date and time of purchase, and the
            total price.

        Returns:
            None

        Note:
            This method assumes that the person associated with the order email already
            exists in Pipedrive. If the person does not exist, the method will not
            send the purchase activity or update the purchase value.
        """
        if person := self._search_person(order.email):
            person_id = person['id']
            purchase_value = (
                self._get_request(f'persons/{person_id}', None)['data'][
                    PURCHASE_VALUE_API_KEY
                ]
                or 0
            )
        else:
            return

        if order.contains_only_samples:
            self._send_activity(order, person_id, 'samples_purchase_done', 0)
        else:
            self._send_activity(order, person_id, 'purchase_', 0)
        self.send_purchase_value(order, person_id, purchase_value)

    def send_previous_orders_to_pipedrive(self, pipedrive_id: int, email: str) -> None:
        """Method for sending data about user's previous orders to Pipedrive.
        Called once per user.
        Initiated by a webhook from Pipedrive (after adding a new user to Pipedrive)."""
        previous_orders = self._get_previous_orders(email)
        for order in previous_orders:
            if order.contains_only_samples:
                self._send_activity(order, pipedrive_id, 'samples_purchase_done', 1)
            else:
                self._send_activity(order, pipedrive_id, 'purchase_', 1)

    @staticmethod
    def _get_previous_orders(email: str) -> QuerySet[Order]:
        return Order.objects.filter(
            email=email,
            status__in={
                OrderStatus.IN_PRODUCTION,
                OrderStatus.SHIPPED,
                OrderStatus.DELIVERED,
                OrderStatus.TO_BE_SHIPPED,
            },
        )
