from carts.models import Cart
from gallery.data_from_ps import get_serialized_data_from_ps
from gallery.models.furniture_abstract import FurnitureAbstract
from gallery.models.models import (
    Jetty,
    Watty,
)


def generate_b2b_table(cart: Cart) -> list:
    region = cart.get_region()

    furniture_items = [
        item.cart_item
        for item in cart.items.exclude(content_type__model='samplebox').all()
    ]
    table_data = []
    for furniture in furniture_items:
        serialized_product = get_serialized_data_from_ps(furniture, cart=cart)
        packs = serialized_product.get('item', {}).get('packs', [])

        table_item = {
            'id': furniture.pk,
            'configurator_url': _get_configurator_url_for_furniture(furniture),
            'img': furniture.preview.url,
            'category': furniture.furniture_category,
            'type': furniture.product_line,
            'region': region.name,
            'price': furniture.get_regionalized_price_display(
                region.cached_region_data
            ),
            'cogs': _get_cogs(serialized_product),
            'weight_brutto': _get_weight_brutto(packs),
            'packs_count': len(packs),
        }
        table_data.append(table_item)

    return table_data


def _get_configurator_url_for_furniture(furniture: FurnitureAbstract) -> str:
    if isinstance(furniture, Jetty):
        return f'/furniture/{furniture.pk},j/'
    elif isinstance(furniture, Watty):
        return f'/furniture/{furniture.pk},w/'
    return ''


def _get_cogs(serialized_product: dict) -> int:
    cogs_values = (
        serialized_product.get('margins', {}).get('cogs_dict_elements', {}).values()
    )
    # NOTE: key 'other' has sometimes negative value,
    # and sum of all positive-values + 'other' key value equals 0
    return round(sum(value if value > 0 else 0 for value in cogs_values))


def _get_weight_brutto(packs: list) -> int:
    return round(sum(pack.get('weight', 0.0) for pack in packs))
