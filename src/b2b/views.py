from io import BytesIO

from django.http import (
    HttpRequest,
    HttpResponse,
)
from django.shortcuts import get_object_or_404
from django.views.generic import TemplateView

from djangorestframework_camel_case.parser import CamelCaseJ<PERSON>NParser
from djangorestframework_camel_case.render import Came<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from openpyxl import Workbook
from rest_framework import (
    generics,
    status,
)
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework.views import APIView

from b2b.serializers import B2bApplicantSerializer
from b2b.services.order_cost_table import generate_b2b_table
from b2b.tasks import (
    send_b2b_applicant_to_pipedrive,
    send_previous_orders_to_pipedrive,
)
from carts.choices import CartStatusChoices
from carts.models import Cart


class B2bApplicantAPIView(generics.CreateAPIView):
    permission_classes = (AllowAny,)
    serializer_class = B2bApplicantSerializer
    renderer_classes = [CamelCaseJ<PERSON><PERSON>enderer]
    parser_classes = [CamelCaseJSONParser]

    def perform_create(self, serializer: B2bApplicantSerializer) -> None:
        super().perform_create(serializer)
        send_b2b_applicant_to_pipedrive.delay(serializer.instance.pk)


class PreviousPurchaseWebhookAPIView(APIView):
    permission_classes = (AllowAny,)

    def post(self, request, *args, **kwargs):
        pipedrive_id = request.data['current']['id']
        email = request.data['current']['email'][0].get('value')
        if not email:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        send_previous_orders_to_pipedrive.delay(pipedrive_id, email)
        return Response(status=status.HTTP_200_OK)


class B2BCartMixin:
    @staticmethod
    def _get_cart_with_latest_item(request: HttpRequest, cart_id: int = None) -> Cart:
        if cart_id:
            return get_object_or_404(Cart, id=cart_id)
        return (
            request.user.carts.prefetch_related('items')
            .filter(status=CartStatusChoices.ACTIVE)
            .order_by('-items__created_at')
            .first()
        )


class B2BProductOrder(B2BCartMixin, TemplateView):
    """View responsible for rendering b2b order cost table for bussiness team."""

    template_name = 'b2b_product_order.html'

    def get(self, request, cart_id=None, *args, **kwargs):
        if not request.user or not request.user.is_staff:
            return HttpResponse(
                content="You're not logged in", status=status.HTTP_403_FORBIDDEN
            )
        cart = self._get_cart_with_latest_item(request, cart_id)
        if not cart:
            return self.render_to_response(context={})
        table_data = generate_b2b_table(cart=cart)
        context = {
            'cart_id': cart_id,
            'table_data': table_data,
        }
        return self.render_to_response(context=context)


class B2BProductOrderDownload(B2BCartMixin, APIView):
    """View responsible for downloading order cost table as CSV."""

    def get(self, request, cart_id=None, *args, **kwargs):
        if not request.user or not request.user.is_staff:
            return HttpResponse(
                content="You're not logged in", status=status.HTTP_403_FORBIDDEN
            )

        cart = self._get_cart_with_latest_item(request, cart_id)
        table_data = generate_b2b_table(cart=cart)

        workbook = Workbook()
        sheet = workbook.active

        fieldnames = [
            'id',
            'configurator_url',
            'img',
            'category',
            'type',
            'region',
            'price',
            'cogs',
            'weight_brutto',
            'packs_count',
        ]
        sheet.append(fieldnames)

        for cart_item in table_data:
            sheet.append(cart_item[key] for key in fieldnames)

        stream = BytesIO()
        workbook.save(stream)

        return HttpResponse(
            content=stream.getvalue(),
            content_type='application/ms-excel',
            headers={
                'Content-Disposition': 'attachment; filename="order_cost_table.xlsx"'
            },
        )
