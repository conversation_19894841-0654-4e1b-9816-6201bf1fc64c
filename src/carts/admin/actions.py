from django.contrib import admin

from carts.services.cart_service import CartService
from custom.utils.decorators import (
    CSVObject,
    csv_export,
)


@csv_export
@admin.action(description='Export selected carts to csv')
def export_carts_as_csv(modeladmin, request, queryset):
    header_row = [
        'ID',
        'Owner',
        'Email',
        'First name',
        'Order source',
        'Promo code text',
        'Total price',
        'Created at',
        'First item added at',
        'Updated at',
        'Language',
        'Preview URLs',
    ]

    rows = []
    for cart in queryset:
        rows.append(
            [
                cart.id,
                cart.owner.username,
                cart.owner.email,
                cart.promo_text,
                cart.total_price,
                cart.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                cart.first_item_added_at.strftime('%Y-%m-%d %H:%M:%S'),
                cart.updated_at.strftime('%Y-%m-%d %H:%M:%S'),
                cart.owner.profile.language,
                [item.sellable_item.preview.url for item in cart.items.all()],
            ]
        )
    return CSVObject(
        file_name='carts_export.csv',
        header_row=header_row,
        rows=rows,
    )


@admin.action(description='Recalculate Cart with current pricing')
def recalculate_cart(modeladmin, request, queryset):
    for cart in queryset:
        CartService.recalculate_cart(cart)
