# Generated by Django 4.1.13 on 2025-01-14 13:09

import django.core.validators

from django.db import (
    migrations,
    models,
)

from custom.enums import ShelfType
from gallery.enums import FurnitureCategory


def enable_assembly_service_for_tone_wardrobes(apps, schema_editor):
    CartItem = apps.get_model('carts', 'CartItem')
    Watty = apps.get_model('gallery', 'Watty')

    tone_wardrobe_ids = Watty.objects.filter(
        shelf_type=ShelfType.TYPE03, shelf_category=FurnitureCategory.WARDROBE
    ).values_list('id', flat=True)
    CartItem.objects.filter(
        content_type__app_label='gallery',
        content_type__model='watty',
        object_id__in=tone_wardrobe_ids,
    ).update(
        with_assembly=True,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('carts', '0010_alter_cartitem_content_type'),
        ('gallery', '0110_samplebox_cost'),
    ]

    operations = [
        migrations.AddField(
            model_name='cartitem',
            name='updated_at',
            field=models.DateTimeField(auto_now=True, help_text='Update date'),
        ),
        migrations.AddField(
            model_name='cartitem',
            name='with_assembly',
            field=models.BooleanField(
                default=False,
                help_text='Did client choose the assembly service or is it required',
                verbose_name='assembly_enabled',
            ),
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True, help_text='Creation date'),
        ),
        migrations.AlterField(
            model_name='cartitem',
            name='quantity',
            field=models.PositiveSmallIntegerField(
                default=1,
                validators=[
                    django.core.validators.MinValueValidator(1),
                    django.core.validators.MaxValueValidator(100),
                ],
            ),
        ),
        migrations.RunPython(
            enable_assembly_service_for_tone_wardrobes,
            reverse_code=migrations.RunPython.noop,
        ),
    ]
