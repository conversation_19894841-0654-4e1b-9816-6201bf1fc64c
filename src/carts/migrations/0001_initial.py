# Generated by Django 4.1.9 on 2024-02-19 07:19

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import regions.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('regions', '0009_change_pl_region_currency_rate'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('vouchers', '0034_influencers_barter_deal_vouher_origin'),
    ]

    operations = [
        migrations.CreateModel(
            name='Cart',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'total_price',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'total_price_net',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'region_total_price',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'region_total_price_net',
                    models.DecimalField(
                        blank=True, decimal_places=2, max_digits=12, null=True
                    ),
                ),
                (
                    'promo_amount',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                (
                    'promo_amount_net',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                (
                    'region_promo_amount',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                (
                    'region_promo_amount_net',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                (
                    'fast_track_price',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                (
                    'region_fast_track_price',
                    models.DecimalField(
                        blank=True, decimal_places=2, default=0, max_digits=12
                    ),
                ),
                ('assembly', models.BooleanField(default=False)),
                ('is_fast_track', models.BooleanField(default=False)),
                ('region_vat', models.BooleanField(default=False)),
                (
                    'vat_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'Normal VAT'),
                            (1, 'VAT 0% - WDT'),
                            (2, 'Switzerland VAT'),
                            (3, 'Germany 19% VAT'),
                            (4, 'Export 0% VAT'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (1, 'Draft'),
                            (2, 'Ordered'),
                            (3, 'Active'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'cart_source',
                    models.IntegerField(
                        choices=[
                            (0, 'web desktop'),
                            (1, 'web mobile'),
                            (2, 'iphone'),
                            (3, 'ipad'),
                            (4, 'android'),
                            (5, 'android tablet'),
                            (8, 'new ios app'),
                            (6, 'api'),
                            (15, 'unknown source'),
                            (16, 'internal order/complaint'),
                            (99, 'new row configurator'),
                        ],
                        default=15,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('first_item_added_at', models.DateTimeField(blank=True, null=True)),
                ('items_changed_at', models.DateTimeField(blank=True, null=True)),
                (
                    'owner',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='regions.region',
                    ),
                ),
                (
                    'used_promo',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.PROTECT,
                        to='vouchers.voucher',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=(regions.mixins.RegionalizedMixin, models.Model),
        ),
        migrations.CreateModel(
            name='CartItem',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('price', models.DecimalField(decimal_places=2, max_digits=12)),
                ('price_net', models.DecimalField(decimal_places=2, max_digits=12)),
                ('region_price', models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    'region_price_net',
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    'assembly_price',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'region_assembly_price',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'delivery_price',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'region_delivery_price',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'vat_amount',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'region_vat_amount',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                (
                    'recycle_tax_value',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
                ('free_assembly_service', models.BooleanField(null=True)),
                (
                    'deleted',
                    models.DateTimeField(db_index=True, editable=False, null=True),
                ),
                (
                    'deleted_by_cascade',
                    models.BooleanField(default=False, editable=False),
                ),
                ('object_id', models.PositiveIntegerField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'cart',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='items',
                        to='carts.cart',
                    ),
                ),
                (
                    'content_type',
                    models.ForeignKey(
                        limit_choices_to=models.Q(
                            models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                            models.Q(('app_label', 'gallery'), ('model', 'samplebox')),
                            models.Q(('app_label', 'gallery'), ('model', 'watty')),
                            _connector='OR',
                        ),
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.contenttype',
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='regions.region',
                    ),
                ),
            ],
            options={
                'abstract': False,
            },
            bases=(regions.mixins.RegionalizedMixin, models.Model),
        ),
    ]
