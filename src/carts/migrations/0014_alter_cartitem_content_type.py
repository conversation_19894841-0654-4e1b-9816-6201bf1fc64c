# Generated by Django 4.1.13 on 2025-05-18 18:20

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('carts', '0013_cartitem_region_delivery_promo_value'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cartitem',
            name='content_type',
            field=models.ForeignKey(
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                    models.Q(('app_label', 'gallery'), ('model', 'samplebox')),
                    models.Q(('app_label', 'gallery'), ('model', 'watty')),
                    models.Q(('app_label', 'gallery'), ('model', 'sotty')),
                    models.Q(('app_label', 'services'), ('model', 'additionalservice')),
                    _connector='OR',
                ),
                on_delete=django.db.models.deletion.CASCADE,
                to='contenttypes.contenttype',
            ),
        ),
    ]
