# Generated by Django 4.1.9 on 2024-02-29 15:23

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0046_non_nullable_first_name'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('carts', '0004_cart_promo_text_cart_vat'),
    ]

    operations = [
        migrations.AddField(
            model_name='cart',
            name='deleted',
            field=models.DateTimeField(db_index=True, editable=False, null=True),
        ),
        migrations.AddField(
            model_name='cart',
            name='deleted_by_cascade',
            field=models.BooleanField(default=False, editable=False),
        ),
        migrations.AddField(
            model_name='cart',
            name='is_sku',
            field=models.BooleanField(help_text='stock keeping unit', null=True),
        ),
        migrations.AddField(
            model_name='cart',
            name='order',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='orders.order',
            ),
        ),
        migrations.AddField(
            model_name='cart',
            name='price_updated_at',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='cart',
            name='owner',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='carts',
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
