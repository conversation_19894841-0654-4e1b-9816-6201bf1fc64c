from django.db.models import Q

import pytest

from custom.enums import ShelfType
from gallery.enums import FurnitureCategory


@pytest.mark.django_db
class TestCart:
    def test_clear_promo(self, cart_factory, voucher):
        cart = cart_factory(
            used_promo=voucher,
            promo_amount=100,
            promo_amount_net=90,
            region_promo_amount=200,
            region_promo_amount_net=180,
        )

        cart.clear_promo()
        cart.refresh_from_db()

        assert cart.used_promo is None
        assert cart.promo_amount == 0
        assert cart.promo_amount_net == 0
        assert cart.region_promo_amount == 0
        assert cart.region_promo_amount_net == 0


@pytest.mark.django_db
class TestCartItem:
    def test_get_without_assembly_service_required(
        self, cart_factory, cart_item_factory, watty_factory, jetty_factory
    ):
        tone_wardrobe = watty_factory(
            shelf_type=ShelfType.TYPE03, shelf_category=FurnitureCategory.WARDROBE
        )
        edge_wardrobe = watty_factory(
            shelf_type=ShelfType.TYPE13, shelf_category=FurnitureCategory.WARDROBE
        )
        tone_sideboard = watty_factory(
            shelf_type=ShelfType.TYPE03, shelf_category=FurnitureCategory.SIDEBOARD
        )
        jetty = jetty_factory()
        cart = cart_factory(items=[])
        _ = [
            cart_item_factory(cart=cart, cart_item=furniture)
            for furniture in (tone_wardrobe, edge_wardrobe, tone_sideboard, jetty)
        ]

        cart_items = cart.items.get_without_assembly_service_required()

        assert cart_items.count() == 3
        assert (
            cart_items.exclude(
                object_id=tone_wardrobe.id,
                content_type__model=tone_wardrobe.furniture_type,
            )
            .filter(
                Q(
                    object_id=edge_wardrobe.id,
                    content_type__model=edge_wardrobe.furniture_type,
                )
                | Q(
                    object_id=tone_sideboard.id,
                    content_type__model=tone_sideboard.furniture_type,
                )
                | Q(object_id=jetty.id, content_type__model=jetty.furniture_type)
            )
            .distinct()
            .count()
            == 3
        )
