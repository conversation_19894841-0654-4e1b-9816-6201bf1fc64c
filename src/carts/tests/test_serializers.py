import pytest

from carts.serializers import MailingCartSerializer


@pytest.mark.django_db
def test_mailing_cart_serializer_items_count_field(
    cart_factory,
    cart_item_factory,
    country_factory,
):
    cart = cart_factory(items=[])
    region = country_factory(germany=True).region

    cart_item_factory(cart=cart, quantity=3, is_watty=True)
    cart_item_factory(cart=cart, quantity=2, is_sample_box=True)

    expected_count = 5

    serializer = MailingCartSerializer(cart, context={'region': region})

    assert serializer.data['items_count'] == expected_count
    assert cart.aggregate_items_count == expected_count
