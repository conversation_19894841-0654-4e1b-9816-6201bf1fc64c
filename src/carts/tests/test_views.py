from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from unittest import mock
from unittest.mock import (
    MagicMock,
    patch,
)

from django.contrib.auth.models import User
from django.test import override_settings
from django.urls import reverse

import pytest

from rest_framework import status

from carts.choices import CartStatusChoices
from carts.models import Cart
from carts.services.cart_service import CartService
from carts.tests.factories import NO_ORDER
from events.models import Event
from gallery.constants import (
    NEAR,
    SOTTY_ASSEMBLY_PRICE,
    SOTTY_DELIVERY_PRICE_MAPPER,
    TWO_MODULES,
)
from gallery.enums import SellableItemContentTypes
from orders.models import Order
from regions.mixins import RegionCalculationsObject
from regions.utils import is_t03_available
from services.constants import OLD_SOFA_COLLECTION_PRICE_MAPPER


@pytest.mark.django_db
class TestCartViewSet:
    def test_owner_response_status(
        self,
        cart_factory,
        region_de,
        api_client,
    ):
        cart = cart_factory(region=region_de)

        url = reverse('cart-v2-detail', args=[cart.id])
        api_client.force_authenticate(user=cart.owner)
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    def test_non_owner_response_status(
        self,
        cart_factory,
        user_factory,
        region_de,
        api_client,
    ):
        cart = cart_factory(region=region_de)
        non_owner_user = user_factory()

        url = reverse('cart-v2-detail', args=[cart.id])
        api_client.force_authenticate(user=non_owner_user)
        response = api_client.get(url)

        assert cart.owner != non_owner_user
        assert response.status_code == status.HTTP_404_NOT_FOUND

    @pytest.mark.parametrize('is_anonymous', [True, False])
    def test_empty_cart_expected_response(
        self,
        is_anonymous,
        api_client,
        region_de,
        promotion_config_factory,
        user_profile_factory,
        mocker,
    ):
        promotion_config = promotion_config_factory(cart_ribbon_enabled=True)
        promotion_config.enabled_regions.add(region_de)
        promotion_config.save()

        url = reverse('cart-v2-empty-response')
        user = None

        if is_anonymous:
            mocker.patch(
                'user_profile.middleware.RegionSelector.get_region',
                return_value=region_de,
            )
        else:
            user_profile = user_profile_factory(region=region_de)
            api_client.force_authenticate(user_profile.user)
            user = user_profile.user

        response = api_client.get(url)
        assert response.status_code == status.HTTP_200_OK

        data = response.json()
        assert data['cartItems'] == []
        assert data['region_name'] == region_de.name
        assert data['cartItemsCount'] == 0
        assert data['cartUsedPromo'] is False
        assert data['cart_used_assembly'] is False
        assert data['promoCodeName'] == ''
        assert data['hasAssemblyPossible'] is False
        assert data['order_pricing']['total_price'] == 0.0
        assert data['order_pricing']['total_price_netto'] == 0.0
        assert data['order_pricing']['total_price_before_discount'] == 0.0
        assert data['order_pricing']['assembly_price'] == 0.0
        assert data['order_pricing']['assembly_promo_price'] == 0.0
        assert data['order_pricing']['discount_value'] == 0.0
        assert data['order_pricing']['recycle_tax_value'] == 0.0
        assert data['order_pricing']['order_revenue_brutto'] == 0.0
        assert data['order_pricing']['tax'] == 0.0
        assert data['order_pricing']['order_total_price_netto'] == 0.0
        assert data['order_pricing']['order_promo_amount_netto'] == 0.0
        assert data['order_pricing']['vat_percentage_value'] == 23
        assert data['order_pricing']['delivery_price'] == 0.0
        assert data['order_pricing']['delivery_promo_price'] == 0.0
        assert data['has_lighting'] is False
        assert data['library_items'] == 0
        assert data['signedIn'] is False
        assert data['saleEnabled'] is True
        assert data['shelfItemsCount'] == 0
        assert data['hasT03'] is False
        assert data['hasT03Samples'] is False
        assert data['cartRibbonEnabled'] is True
        assert data['deliveryTime'] == ''
        assert data['t03Available'] is False if is_anonymous else is_t03_available(user)

    def test_cart_details(
        self,
        api_client,
        user_factory,
        cart_factory,
        cart_item_factory,
        sample_box_factory,
    ):
        user = user_factory()
        sample_box = sample_box_factory()
        cart = cart_factory(owner=user, items=[])
        cart_item_factory(cart=cart, cart_item=sample_box)
        api_client.force_authenticate(user)
        url = reverse('cart-v2-detail', kwargs={'pk': cart.id})

        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        response_cart_item = response_json['cartItems'][0]
        assert response_cart_item['content_type'] == 'samplebox'
        assert response_cart_item['variant_type'] == sample_box.box_variant.variant_type
        assert response_cart_item['color'] == sample_box.box_variant.color

    @pytest.mark.parametrize(
        ['sellable_item_fixture_name', 'expected_shelf_items_count'],
        [
            ['jetty', 1],
            ['watty', 1],
            ['sotty', 1],
            ['sample_box', 0],
        ],
    )
    def test_cart_shelf_items_count(
        self,
        sellable_item_fixture_name,
        expected_shelf_items_count,
        api_client,
        user_factory,
        cart_factory,
        cart_item_factory,
        request,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)
        user = user_factory()
        cart = cart_factory(owner=user)
        cart_item_factory(
            cart=cart, cart_item=sellable_item, free_assembly_service=False
        )
        api_client.force_authenticate(user)

        url = reverse('cart-v2-detail', kwargs={'pk': cart.id})
        response = api_client.get(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json['shelfItemsCount'] == expected_shelf_items_count

    def test_create_cart(self, api_client):
        users_count_before = User.objects.count()

        url = reverse('cart-v2-create-cart')
        response = api_client.post(url)
        assert response.status_code == status.HTTP_201_CREATED

        created_cart_id = response.json()['id']

        assert User.objects.count() == users_count_before + 1
        assert Cart.objects.filter(id=created_cart_id).exists()

    @pytest.mark.parametrize(
        ['sellable_item_fixture', 'content_type'],
        [
            ['jetty', SellableItemContentTypes.JETTY],
            ['sotty', SellableItemContentTypes.SOTTY],
            ['watty', SellableItemContentTypes.WATTY],
        ],
    )
    def test_add_to_cart(
        self,
        sellable_item_fixture,
        content_type,
        cart,
        api_client,
        request,
    ):
        cart_items_count_before = cart.items.count()
        sellable_item = request.getfixturevalue(sellable_item_fixture)
        data = {'sellable_item_id': sellable_item.id, 'content_type': content_type}
        api_client.force_authenticate(user=cart.owner)
        url = reverse('cart-v2-add-to-cart', args=[cart.id])

        response = api_client.post(url, data=data)

        assert response.status_code == status.HTTP_201_CREATED
        cart.refresh_from_db()
        assert cart.items.count() == cart_items_count_before + 1
        assert cart.items.first().sellable_item != sellable_item

    @pytest.mark.parametrize(
        ['sellable_item_fixture', 'content_type'],
        [
            ['jetty', SellableItemContentTypes.JETTY],
            ['sotty', SellableItemContentTypes.SOTTY],
            ['watty', SellableItemContentTypes.WATTY],
        ],
    )
    def test_add_to_cart_emits_cart_update_event(
        self,
        sellable_item_fixture,
        content_type,
        cart,
        api_client,
        request,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture)
        events_count_before = Event.objects.filter(event_name='CartUpdateEvent').count()

        url = reverse('cart-v2-add-to-cart', args=[cart.id])
        data = {'sellable_item_id': sellable_item.id, 'content_type': content_type}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_201_CREATED

        events_count_after = Event.objects.filter(event_name='CartUpdateEvent').count()
        assert events_count_after == events_count_before + 1

    @pytest.mark.parametrize(
        ['sellable_item_fixture', 'content_type'],
        [
            ['jetty', SellableItemContentTypes.JETTY],
            ['sotty', SellableItemContentTypes.SOTTY],
            ['watty', SellableItemContentTypes.WATTY],
        ],
    )
    def test_add_to_cart_increases_datadog_metrics(
        self,
        sellable_item_fixture,
        content_type,
        cart,
        api_client,
        request,
        mocker,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture)
        metrics_client_mock = MagicMock()
        mocker.patch('carts.views.metrics_client', return_value=metrics_client_mock)

        url = reverse('cart-v2-add-to-cart', args=[cart.id])
        data = {'sellable_item_id': sellable_item.id, 'content_type': content_type}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_201_CREATED

        metrics_client_mock.increment.assert_called_once_with(
            'api.save_to_cart',
            1,
            tags=[
                f'shelf_type:{sellable_item.furniture_type}',
                f'user_lang:{cart.owner.profile.language}',
            ],
        )

    @pytest.mark.parametrize(
        ['sellable_item_fixture', 'content_type', 'state_added'],
        [
            ['jetty', SellableItemContentTypes.JETTY, True],
            ['sotty', SellableItemContentTypes.SOTTY, False],
            ['watty', SellableItemContentTypes.WATTY, False],
        ],
    )
    def test_add_to_cart_adds_only_jetty_states_to_redis(
        self,
        sellable_item_fixture,
        content_type,
        state_added,
        cart,
        api_client,
        request,
        mocker,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture)
        add_states_to_redis_mock = mocker.patch('carts.views.add_jetty_state_to_redis')

        url = reverse('cart-v2-add-to-cart', args=[cart.id])
        data = {'sellable_item_id': sellable_item.id, 'content_type': content_type}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_201_CREATED

        assert add_states_to_redis_mock.called is state_added

    def test_delete_from_cart(self, cart, api_client):
        cart_item_to_delete = cart.items.first()
        cart_items_count_before = cart.items.count()

        url = reverse('cart-v2-delete-from-cart', args=[cart.id])
        data = {
            'sellable_item_id': cart_item_to_delete.object_id,
            'content_type': cart_item_to_delete.content_type.model,
        }
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_204_NO_CONTENT

        cart.refresh_from_db()
        assert cart.items.count() == cart_items_count_before - 1
        assert cart_item_to_delete not in cart.items.all()

    def test_delete_from_cart_emits_cart_update_event(
        self,
        cart,
        api_client,
    ):
        assert cart.items.count() == 2

        cart_item_to_delete = cart.items.first()
        events_count_before = Event.objects.filter(event_name='CartUpdateEvent').count()

        url = reverse('cart-v2-delete-from-cart', args=[cart.id])
        data = {
            'sellable_item_id': cart_item_to_delete.object_id,
            'content_type': cart_item_to_delete.content_type.model,
        }
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_204_NO_CONTENT

        events_count_after = Event.objects.filter(event_name='CartUpdateEvent').count()
        assert events_count_after == events_count_before + 1

    def test_delete_last_item_from_cart_emits_cart_empty_event(
        self,
        cart_factory,
        cart_item_factory,
        api_client,
    ):
        cart = cart_factory(items=[])
        cart_item_factory(cart=cart)
        assert cart.items.count() == 1

        cart_item_to_delete = cart.items.first()
        events_count_before = Event.objects.filter(event_name='CartEmptyEvent').count()

        url = reverse('cart-v2-delete-from-cart', args=[cart.id])
        data = {
            'sellable_item_id': cart_item_to_delete.object_id,
            'content_type': cart_item_to_delete.content_type.model,
        }
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_204_NO_CONTENT

        events_count_after = Event.objects.filter(event_name='CartEmptyEvent').count()
        assert events_count_after == events_count_before + 1

    def test_add_assembly(self, cart_factory, region_factory, api_client, settings):
        region = region_factory(germany=True)
        cart = cart_factory(assembly=False, region=region)
        assert region.name in settings.ASSEMBLY_REGION_KEYS

        url = reverse('cart-v2-change-assembly', args=[cart.id])
        data = {'assembly': True}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_200_OK

        cart.refresh_from_db()
        assert cart.assembly is True

    def test_add_assembly_in_no_assembly_region(
        self,
        cart_factory,
        region_factory,
        api_client,
        settings,
    ):
        region = region_factory()
        cart = cart_factory(assembly=False, region=region)
        assert region.name not in settings.ASSEMBLY_REGION_KEYS

        url = reverse('cart-v2-change-assembly', args=[cart.id])
        data = {'assembly': True}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {
            'detail': 'Assembly is not available for this region or it is a fast track',
        }

        cart.refresh_from_db()
        assert cart.assembly is False

    def test_remove_assembly(self, cart_factory, region_factory, api_client, settings):
        region = region_factory(germany=True)
        cart = cart_factory(assembly=True, region=region)
        assert region.name in settings.ASSEMBLY_REGION_KEYS

        url = reverse('cart-v2-change-assembly', args=[cart.id])
        data = {'assembly': False}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_200_OK

        cart.refresh_from_db()
        assert cart.assembly is False

    def test_sync_with_order_when_cart_has_no_order(
        self,
        cart_factory,
        api_client,
    ):
        cart = cart_factory(order=NO_ORDER)
        order_count = Order.objects.count()

        url = reverse('cart-v2-sync-with-order', args=[cart.id])
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url)
        assert response.status_code == status.HTTP_200_OK

        assert Order.objects.count() == order_count + 1

        cart.refresh_from_db()
        created_order = Order.objects.last()

        assert cart.order == created_order
        assert response.json() == {'order_id': created_order.id}

    def test_sync_with_order_when_cart_has_order(
        self,
        cart,
        api_client,
    ):
        order = cart.order
        assert order is not None

        order_count = Order.objects.count()

        url = reverse('cart-v2-sync-with-order', args=[cart.id])
        api_client.force_authenticate(cart.owner)
        response = api_client.post(url)
        assert response.status_code == status.HTTP_200_OK

        assert Order.objects.count() == order_count
        assert cart.order == order
        assert response.json() == {'order_id': order.id}

    @pytest.mark.parametrize(
        ['sellable_item_fixture', 'delivery_price'],
        [
            ['jetty', Decimal('0.0')],
            ['sotty', Decimal('199.00')],
            ['watty', Decimal('0.0')],
        ],
    )
    def test_get_response_with_delivery_price(
        self,
        sellable_item_fixture,
        delivery_price,
        region_uk,
        cart_factory,
        api_client,
        request,
        user,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture)
        cart = cart_factory(owner=user, items=[], region=region_uk)
        CartService(cart=cart).add_to_cart(sellable_item)

        api_client.force_authenticate(user)
        url = reverse('cart-v2-detail', args=[cart.id])
        response = api_client.get(url)

        region_delivery_price = RegionCalculationsObject(
            region=region_uk
        ).calculate_regionalized(delivery_price)
        assert response.status_code == status.HTTP_200_OK
        assert response.data['order_pricing']['delivery_price'] == region_delivery_price
        assert (
            response.data['order_pricing']['delivery_price_in_euro'] == delivery_price
        )
        assert response.data['cartItems'][0]['deliveryPrice'] == delivery_price
        assert (
            response.data['cartItems'][0]['regionDeliveryPrice']
            == region_delivery_price
        )


@pytest.mark.django_db
class TestChangeOldSofaCollectionAPIView:
    """
    Plan.
    1. Can add, all good.
    2. Can remove, all good.
    3. Can't add if not available in region, all good.
    4. Can't add if does not have sofa in cart
    5. Can't remove if does not have old sofa collection in cart
    """

    def test_add_old_sofa_removal(
        self,
        cart_factory,
        region_de,
        api_client,
        settings,
        cart_item_factory,
    ):
        cart = cart_factory(region=region_de)
        cart_item_factory(cart=cart, is_sotty=True)
        assert region_de.name in settings.OLD_SOFA_COLLECTION_REGIONS

        url = reverse('cart-v2-change-old-sofa-collection', args=[cart.id])
        data = {'old_sofa_collection': True}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_200_OK

        assert cart.has_old_sofa_collection is True

    def test_remove_old_sofa_removal(
        self,
        cart_factory,
        region_de,
        api_client,
        settings,
        cart_item_factory,
    ):
        cart = cart_factory(region=region_de)
        cart_item_factory(cart=cart, is_sotty=True)
        cart_item_factory(cart=cart, is_old_sofa_collection=True)
        assert region_de.name in settings.OLD_SOFA_COLLECTION_REGIONS

        url = reverse('cart-v2-change-old-sofa-collection', args=[cart.id])
        data = {'old_sofa_collection': False}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_200_OK

        assert cart.has_old_sofa_collection is False

    def test_cant_add_old_sofa_removal_if_not_available_in_region(
        self,
        cart_factory,
        region_de,
        api_client,
        settings,
        cart_item_factory,
    ):
        cart = cart_factory(region=region_de)
        cart_item_factory(cart=cart, is_sotty=True)
        with override_settings(OLD_SOFA_COLLECTION_REGIONS={}):
            assert region_de.name not in settings.OLD_SOFA_COLLECTION_REGIONS
            url = reverse('cart-v2-change-old-sofa-collection', args=[cart.id])
            data = {'old_sofa_collection': True}
            api_client.force_authenticate(user=cart.owner)
            response = api_client.post(url, data=data)
            assert response.status_code == status.HTTP_400_BAD_REQUEST
            assert response.json() == {
                'detail': 'Service is not available for this region.'
            }

    def test_cant_add_old_sofa_removal_if_no_sofa_in_cart(
        self,
        cart_factory,
        region_de,
        api_client,
        settings,
    ):
        cart = cart_factory(region=region_de)
        assert cart.items.filter(content_type__model='sotty').exists() is False
        assert region_de.name in settings.OLD_SOFA_COLLECTION_REGIONS

        url = reverse('cart-v2-change-old-sofa-collection', args=[cart.id])
        data = {'old_sofa_collection': True}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {'detail': 'Cart does not have sofa'}

    def test_cant_remove_old_sofa_removal_if_no_old_sofa_removal_in_cart(
        self,
        cart_factory,
        region_de,
        api_client,
        settings,
        cart_item_factory,
    ):
        cart = cart_factory(region=region_de)
        cart_item_factory(cart=cart, is_sotty=True)
        assert cart.has_old_sofa_collection is False
        assert region_de.name in settings.OLD_SOFA_COLLECTION_REGIONS

        url = reverse('cart-v2-change-old-sofa-collection', args=[cart.id])
        data = {'old_sofa_collection': False}
        api_client.force_authenticate(user=cart.owner)
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert response.json() == {
            'detail': "Can't remove service if no service in cart."
        }


@pytest.mark.django_db
class TestCartItemQuantityChangeAPIView:
    def test_should_increase_cart_item_quantity(
        self, api_client, user, cart_factory, cart_item_factory
    ):
        cart = cart_factory(owner=user, items=[])
        item = cart_item_factory(cart=cart)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-increase', kwargs={'item_id': item.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json == {'id': item.id, 'new_quantity': 2}

    def test_should_decrease_cart_item_quantity(
        self, api_client, user, cart_factory, cart_item_factory
    ):
        cart = cart_factory(owner=user, items=[])
        item = cart_item_factory(cart=cart, quantity=2)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-decrease', kwargs={'item_id': item.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json == {'id': item.id, 'new_quantity': 1}

    def test_cant_decrease_quantity_below_1(
        self, api_client, user, cart_factory, cart_item_factory
    ):
        cart = cart_factory(owner=user, items=[])
        item = cart_item_factory(cart=cart)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-decrease', kwargs={'item_id': item.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_json = response.json()
        assert response_json == {'detail': 'Quantity cannot be decreased below 1'}

    def test_cant_increase_cart_size_over_100(
        self, api_client, user, cart_factory, cart_item_factory
    ):
        cart = cart_factory(owner=user, items=[])
        item = cart_item_factory(cart=cart)
        cart_item_factory(cart=cart, quantity=99)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-increase', kwargs={'item_id': item.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_json = response.json()
        assert response_json == {
            'detail': (
                'The maximum cart size has been exceeded. '
                'The total number of products in the cart '
                'must be less than or equal to 100.'
            )
        }

    @pytest.mark.parametrize(
        'kwargs', [{}, {'status': CartStatusChoices.ORDERED}]  # different owner
    )
    def test_should_not_find_cart_item_quantity(
        self, api_client, user, cart_factory, cart_item_factory, kwargs
    ):
        cart = cart_factory(items=[], **kwargs)
        item = cart_item_factory(cart=cart)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-increase', kwargs={'item_id': item.id})

        response = api_client.post(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_json = response.json()
        assert response_json == {'detail': 'Not found.'}


@pytest.mark.skip
@pytest.mark.django_db
class TestCartItemEnableAssemblyAPIView:
    @pytest.mark.parametrize('assembly_enabled', (True, False))
    def test_should_enable_assembly_service(
        self, api_client, user, cart_item_factory, assembly_enabled
    ):
        item = cart_item_factory(cart__owner=user, with_assembly=assembly_enabled)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-assembly', kwargs={'item_id': item.id})

        response = api_client.patch(
            url, data={'assembly_enabled': not assembly_enabled}
        )

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json == {
            'id': item.id,
            'assembly_enabled': not assembly_enabled,
        }

    def test_cant_enable_assembly_servie_when_required(
        self, api_client, user, watty_factory, cart_item_factory
    ):
        item = cart_item_factory(
            cart__owner=user, with_assembly=True, is_tone_wardrobe=True
        )
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-assembly', kwargs={'item_id': item.id})

        response = api_client.patch(url, data={'assembly_enabled': False})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_json = response.json()
        assert (
            response_json['assembly_enabled'][0]
            == f'Cart Item [id={item.id}] requires assembly service'
        )

    @pytest.mark.parametrize(
        'kwargs', [{}, {'cart__status': CartStatusChoices.ORDERED}]  # different owner
    )
    def test_should_not_find_cart_item(
        self, api_client, user, cart_item_factory, kwargs
    ):
        item = cart_item_factory(with_assembly=True, **kwargs)
        api_client.force_authenticate(user=user)
        url = reverse('cart-item-assembly', kwargs={'item_id': item.id})

        response = api_client.patch(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_json = response.json()
        assert response_json == {'detail': 'Not found.'}


@pytest.mark.django_db
class TestMailingCartStatusAPIView:
    @patch('gallery.models.Jetty.get_compartment_max_load', return_value=100)
    def test_get_cart_by_cart_id(self, _, braze_client, cart_factory, country_factory):
        the_greatest_country_in_the_world = country_factory(germany=True)
        cart = cart_factory(region=the_greatest_country_in_the_world.region)
        url = reverse('mailing-cart-status-detail', kwargs={'pk': cart.id})

        response = braze_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    def test_get_cart_by_invalid_cart_id(self, braze_client):
        url = reverse(
            'mailing-cart-status-detail', kwargs={'pk': 9999}  # Nonexistent cart
        )

        response = braze_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    @patch('gallery.models.Jetty.get_compartment_max_load', return_value=100)
    def test_get_cart_by_user_id(
        self, _, braze_client, user_factory, cart_factory, country_factory
    ):
        the_greatest_country_in_the_world = country_factory(germany=True)
        user = user_factory()
        braze_client.force_authenticate(user)
        cart_factory(owner=user, region=the_greatest_country_in_the_world.region)
        url = reverse('mailing-cart-status-by-user', kwargs={'user_id': user.id})

        response = braze_client.get(url)

        assert response.status_code == status.HTTP_200_OK

    def test_get_cart_by_invalid_user_id(self, braze_client):
        url = reverse('mailing-cart-status-by-user', kwargs={'user_id': 9999})

        response = braze_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND

    def test_user_has_no_cart(self, braze_client, user_factory):
        user = user_factory()
        braze_client.force_authenticate(user)
        url = reverse('mailing-cart-status-by-user', kwargs={'user_id': user.id})

        response = braze_client.get(url)

        assert response.status_code == status.HTTP_404_NOT_FOUND
        response_json = response.json()
        assert response_json == {'detail': 'Cart not found for this user'}


@pytest.fixture
def region_pl_with_strong_currency(
    region_factory, currency_factory, currency_rate_factory
):
    """Poland, but with PLN being half of euro price"""
    strong_zloty = currency_factory(code='PLN', symbol='zł', name='Złoty')
    currency_rate_factory(currency=strong_zloty, rate=0.5)
    return region_factory(
        name='poland',
        currency=strong_zloty,
        default_for_language='pl',
    )


@pytest.mark.django_db
class TestCartPricingSerializerInView:
    """
    Testing only delivery and assembly related fields, with neutral regions and not
    Scenarios for order_pricing :
    1. No promo, no delivery cost, no old_sof_collection, regular assembly price
    2. With sofa, no promo, with delivery cost, with old_sofa_collection, assembly with delivery
    3. Sofa with discount on delivery, discount on assembly
    4. Sofa and shelf, no promo, with old sofa, with delivery cost, assembly with delivery
    5. Sofa and shelf, with promo, with old sofa, with delivery cost, assembly with delivery
    6. Sofa and Tone wardrobe, no promo, with old sofa, with delivery cost, assembly with delivery without wardrobe cost
    7. Shelf, with promo, 0 delivery cost, assembly without promo

    Scenarios for cartItems, check delivery promo price and delivery promo
    1. Sofa, no promo
    2. Sofa, with promo
    3. Shelf, no promo
    4. Shelf, with promo
    5. Sofa and shelf, no promo
    5. Sofa and shelf, with promo
    """

    JETTY_ASSEMBLY_PRICE = 100
    # germany has 30% discount on assembly
    JETTY_ASSEMBLY_PRICE_GERMANY = 70

    def _setup_cart_and_get_response(
        self,
        cart_factory,
        cart_item_factory,
        region,
        api_client,
        items_config,
        delivery_voucher=None,
    ):
        """Helper method to set up cart and get API response"""
        items = []
        for config in items_config:
            items.append(cart_item_factory(region=region, **config))

        cart = cart_factory(
            items=items,
            owner__profile__region=region,
            region=region,
        )

        with mock.patch(
            'gallery.models.models.Jetty.get_assembly_price',
            return_value=self.JETTY_ASSEMBLY_PRICE,
        ):
            if delivery_voucher:
                CartService(cart).add_voucher(delivery_voucher)
            else:
                CartService.recalculate_cart(cart)

        url = reverse('cart-v2-detail', args=[cart.id])
        api_client.force_authenticate(user=cart.owner)

        return cart, url, api_client

    def _get_response_with_jetty_mock(self, cart, url, api_client):
        """Get response with mocked Jetty assembly price"""
        with mock.patch(
            'gallery.models.models.Jetty.get_assembly_price',
            return_value=self.JETTY_ASSEMBLY_PRICE,
        ):
            response = api_client.get(url)
        return response

    def _assert_common_response(self, response):
        """Assert common response expectations"""
        assert response.status_code == status.HTTP_200_OK
        return response.json().get('order_pricing')

    def _assert_cart_items(self, response):
        """Assert common cart items expectations and return cart items"""
        assert response.status_code == status.HTTP_200_OK
        return response.json().get('cartItems')

    def _calculate_promo_price(self, full_price):
        """Calculate promotional price (50% discount)"""
        return Decimal(full_price * Decimal('0.50')).quantize(
            Decimal('1'), rounding=ROUND_HALF_UP
        )

    def _calculate_price_for_region(self, base_price, region_rate) -> Decimal:
        return (base_price * region_rate).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    @pytest.mark.parametrize(
        ('region_fixture', 'rate', 'assembly_price'),
        (
            ('region_de', Decimal('1'), JETTY_ASSEMBLY_PRICE_GERMANY),
            ('region_pl_with_strong_currency', Decimal('0.5'), JETTY_ASSEMBLY_PRICE),
        ),
    )
    def test_no_promo_no_sofa(
        self,
        cart_factory,
        cart_item_factory,
        api_client,
        request,
        region_fixture,
        rate,
        assembly_price,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_jetty': True}],
        )
        response = self._get_response_with_jetty_mock(cart, url, api_client)
        pricing_data = self._assert_common_response(response)

        # Assertions
        assert pricing_data['old_sofa_collection_price'] == 0
        assert pricing_data['delivery_price'] == 0
        assert pricing_data['delivery_promo'] is False
        assert pricing_data['delivery_promo_price'] == 0
        assert pricing_data['assembly_promo'] is False
        assert pricing_data['assembly_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )
        assert pricing_data['assembly_promo_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_sofa_no_promo(
        self,
        cart_factory,
        cart_item_factory,
        api_client,
        request,
        region_fixture,
        rate,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}],
        )
        response = api_client.get(url)
        pricing_data = self._assert_common_response(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]

        # Assertions
        assert pricing_data['delivery_promo'] is False
        expected_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )
        assert pricing_data['delivery_price'] == expected_delivery_price
        assert pricing_data['delivery_promo_price'] == expected_delivery_price
        assert pricing_data['assembly_promo'] is False
        expected_assembly_price = (
            self._calculate_price_for_region(SOTTY_ASSEMBLY_PRICE, rate)
            + expected_delivery_price
        )
        assert pricing_data['assembly_price'] == expected_assembly_price
        assert pricing_data['assembly_promo_price'] == expected_assembly_price
        assert pricing_data[
            'old_sofa_collection_price'
        ] == self._calculate_price_for_region(
            OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES], rate
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate', 'assembly_price'),
        (
            ('region_de', Decimal('1'), JETTY_ASSEMBLY_PRICE_GERMANY),
            ('region_pl_with_strong_currency', Decimal('0.5'), JETTY_ASSEMBLY_PRICE),
        ),
    )
    def test_sofa_and_shelf_without_promo(
        self,
        cart_factory,
        cart_item_factory,
        api_client,
        request,
        region_fixture,
        rate,
        assembly_price,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}, {'is_jetty': True}],
        )
        response = self._get_response_with_jetty_mock(cart, url, api_client)
        pricing_data = self._assert_common_response(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]

        # Assertions
        expected_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )
        assert pricing_data['delivery_price'] == expected_delivery_price
        assert pricing_data['delivery_promo'] is False
        assert pricing_data['delivery_promo_price'] == expected_delivery_price
        assert pricing_data['assembly_promo'] is False
        expected_assembly_price = (
            self._calculate_price_for_region(
                assembly_price + SOTTY_ASSEMBLY_PRICE, rate
            )
            + expected_delivery_price
        )
        assert pricing_data['assembly_price'] == expected_assembly_price
        assert pricing_data['assembly_promo_price'] == expected_assembly_price
        assert pricing_data[
            'old_sofa_collection_price'
        ] == self._calculate_price_for_region(
            OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES], rate
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_sofa_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}],
            delivery_voucher=delivery_voucher,
        )
        response = api_client.get(url)
        pricing_data = self._assert_common_response(response)

        # Expected values
        full_sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        promo_sotty_delivery_price = self._calculate_promo_price(
            full_sotty_delivery_price
        )

        # Assertions
        assert pricing_data[
            'old_sofa_collection_price'
        ] == self._calculate_price_for_region(
            OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES], rate
        )
        expected_delivery_price = self._calculate_price_for_region(
            full_sotty_delivery_price, rate
        )
        expected_delivery_promo_price = self._calculate_price_for_region(
            promo_sotty_delivery_price, rate
        )
        assert pricing_data['delivery_price'] == expected_delivery_price
        assert pricing_data['delivery_promo'] is True
        assert pricing_data['delivery_promo_price'] == expected_delivery_promo_price
        assert pricing_data['assembly_promo'] is True
        assert (
            pricing_data['assembly_price']
            == self._calculate_price_for_region(SOTTY_ASSEMBLY_PRICE, rate)
            + expected_delivery_price
        )
        assert (
            pricing_data['assembly_promo_price']
            == self._calculate_price_for_region(SOTTY_ASSEMBLY_PRICE, rate)
            + expected_delivery_promo_price
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate', 'assembly_price'),
        (
            ('region_de', Decimal('1'), JETTY_ASSEMBLY_PRICE_GERMANY),
            ('region_pl_with_strong_currency', Decimal('0.5'), JETTY_ASSEMBLY_PRICE),
        ),
    )
    def test_sofa_and_shelf_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
        assembly_price,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}, {'is_jetty': True}],
            delivery_voucher=delivery_voucher,
        )

        response = api_client.get(url)
        pricing_data = self._assert_common_response(response)

        # Expected values
        full_sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        promo_sotty_delivery_price = self._calculate_promo_price(
            full_sotty_delivery_price
        )

        # Assertions
        assert pricing_data[
            'old_sofa_collection_price'
        ] == self._calculate_price_for_region(
            OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES], rate
        )
        expected_delivery_price = self._calculate_price_for_region(
            full_sotty_delivery_price, rate
        )
        expected_delivery_promo_price = self._calculate_price_for_region(
            promo_sotty_delivery_price, rate
        )
        assert pricing_data['delivery_price'] == expected_delivery_price
        assert pricing_data['delivery_promo'] is True
        assert pricing_data['delivery_promo_price'] == expected_delivery_promo_price
        assert pricing_data['assembly_promo'] is True
        assert (
            pricing_data['assembly_price']
            == self._calculate_price_for_region(
                assembly_price + SOTTY_ASSEMBLY_PRICE, rate
            )
            + expected_delivery_price
        )
        assert (
            pricing_data['assembly_promo_price']
            == self._calculate_price_for_region(
                assembly_price + SOTTY_ASSEMBLY_PRICE, rate
            )
            + expected_delivery_promo_price
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_sofa_and_tone_wardrobe_no_promo(
        self, cart_factory, cart_item_factory, api_client, request, region_fixture, rate
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}, {'is_tone_wardrobe': True}],
        )
        response = api_client.get(url)
        pricing_data = self._assert_common_response(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]

        # Assertions
        assert pricing_data[
            'old_sofa_collection_price'
        ] == self._calculate_price_for_region(
            OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES], rate
        )
        expected_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )
        assert pricing_data['delivery_price'] == expected_delivery_price
        assert pricing_data['delivery_promo'] is False
        assert pricing_data['delivery_promo_price'] == expected_delivery_price
        # Tone wardrobe assembly is free
        assert pricing_data['assembly_promo'] is False
        assert (
            pricing_data['assembly_price']
            == self._calculate_price_for_region(SOTTY_ASSEMBLY_PRICE, rate)
            + expected_delivery_price
        )
        assert (
            pricing_data['assembly_promo_price']
            == self._calculate_price_for_region(SOTTY_ASSEMBLY_PRICE, rate)
            + expected_delivery_price
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate', 'assembly_price'),
        (
            ('region_de', Decimal('1'), JETTY_ASSEMBLY_PRICE_GERMANY),
            ('region_pl_with_strong_currency', Decimal('0.5'), JETTY_ASSEMBLY_PRICE),
        ),
    )
    def test_shelf_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
        assembly_price,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_jetty': True}],
            delivery_voucher=delivery_voucher,
        )

        response = api_client.get(url)
        pricing_data = self._assert_common_response(response)

        # Assertions
        assert pricing_data['old_sofa_collection_price'] == 0
        assert pricing_data['delivery_price'] == 0
        assert pricing_data['delivery_promo'] is False
        assert pricing_data['delivery_promo_price'] == 0
        assert pricing_data['assembly_promo'] is False
        assert pricing_data['assembly_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )
        assert pricing_data['assembly_promo_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate', 'assembly_price'),
        (
            ('region_de', Decimal('1'), JETTY_ASSEMBLY_PRICE_GERMANY),
            ('region_pl_with_strong_currency', Decimal('0.5'), JETTY_ASSEMBLY_PRICE),
        ),
    )
    def test_shelf_without_promo(
        self,
        cart_factory,
        cart_item_factory,
        api_client,
        request,
        region_fixture,
        rate,
        assembly_price,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_jetty': True}],
        )
        response = self._get_response_with_jetty_mock(cart, url, api_client)
        pricing_data = self._assert_common_response(response)

        # Assertions
        assert pricing_data['old_sofa_collection_price'] == 0
        assert pricing_data['delivery_price'] == 0
        assert pricing_data['delivery_promo'] is False
        assert pricing_data['delivery_promo_price'] == 0
        assert pricing_data['assembly_promo'] is False
        assert pricing_data['assembly_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )
        assert pricing_data['assembly_promo_price'] == (
            self._calculate_price_for_region(assembly_price, rate)
        )

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_sofa_no_promo(
        self, cart_factory, cart_item_factory, api_client, request, region_fixture, rate
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}],
        )
        response = api_client.get(url)
        cart_items = self._assert_cart_items(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        expected_region_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )

        # Assertions
        assert len(cart_items) == 1
        sofa_item = cart_items[0]
        assert sofa_item['content_type'] == 'sotty'
        assert sofa_item['deliveryPrice'] == sotty_delivery_price
        assert sofa_item['regionDeliveryPrice'] == expected_region_delivery_price
        assert sofa_item['deliveryPromo'] is False
        assert sofa_item['regionDeliveryPromoPrice'] == expected_region_delivery_price

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_sofa_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}],
            delivery_voucher=delivery_voucher,
        )
        response = api_client.get(url)
        cart_items = self._assert_cart_items(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        expected_region_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )
        promo_sotty_delivery_price = self._calculate_promo_price(sotty_delivery_price)
        expected_region_promo_price = self._calculate_price_for_region(
            promo_sotty_delivery_price, rate
        )

        # Assertions
        assert len(cart_items) == 1
        sofa_item = cart_items[0]
        assert sofa_item['content_type'] == 'sotty'
        assert sofa_item['deliveryPrice'] == sotty_delivery_price
        assert sofa_item['regionDeliveryPrice'] == expected_region_delivery_price
        assert sofa_item['deliveryPromo'] is True
        assert sofa_item['regionDeliveryPromoPrice'] == expected_region_promo_price

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_shelf_no_promo(
        self, cart_factory, cart_item_factory, api_client, request, region_fixture, rate
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_jetty': True}],
        )
        response = self._get_response_with_jetty_mock(cart, url, api_client)
        cart_items = self._assert_cart_items(response)

        # Assertions
        assert len(cart_items) == 1
        shelf_item = cart_items[0]
        assert shelf_item['content_type'] == 'jetty'
        assert shelf_item['deliveryPrice'] == 0
        assert shelf_item['regionDeliveryPrice'] == 0
        assert shelf_item['deliveryPromo'] is False
        assert shelf_item['regionDeliveryPromoPrice'] == 0

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_shelf_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_jetty': True}],
            delivery_voucher=delivery_voucher,
        )

        CartService(cart).add_voucher(delivery_voucher)
        response = api_client.get(url)

        cart_items = self._assert_cart_items(response)
        # Assertions
        assert len(cart_items) == 1
        shelf_item = cart_items[0]
        assert shelf_item['content_type'] == 'jetty'
        assert shelf_item['deliveryPrice'] == 0
        assert shelf_item['regionDeliveryPrice'] == 0
        assert shelf_item['deliveryPromo'] is False
        assert shelf_item['regionDeliveryPromoPrice'] == 0

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_sofa_and_shelf_no_promo(
        self, cart_factory, cart_item_factory, api_client, request, region_fixture, rate
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}, {'is_jetty': True}],
        )
        response = self._get_response_with_jetty_mock(cart, url, api_client)
        cart_items = self._assert_cart_items(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        expected_region_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )

        # Assertions
        assert len(cart_items) == 2

        # Find sofa and shelf items
        sofa_item = next(
            (item for item in cart_items if item['content_type'] == 'sotty'), None
        )
        shelf_item = next(
            (item for item in cart_items if item['content_type'] == 'jetty'), None
        )

        assert sofa_item is not None
        assert shelf_item is not None

        # Sofa assertions
        assert sofa_item['deliveryPrice'] == sotty_delivery_price
        assert sofa_item['regionDeliveryPrice'] == expected_region_delivery_price
        assert sofa_item['deliveryPromo'] is False
        assert sofa_item['regionDeliveryPromoPrice'] == expected_region_delivery_price

        # Shelf assertions
        assert shelf_item['deliveryPrice'] == 0
        assert shelf_item['regionDeliveryPrice'] == 0
        assert shelf_item['deliveryPromo'] is False
        assert shelf_item['regionDeliveryPromoPrice'] == 0

    @pytest.mark.parametrize(
        ('region_fixture', 'rate'),
        (
            ('region_de', Decimal('1')),
            ('region_pl_with_strong_currency', Decimal('0.5')),
        ),
    )
    def test_items_sofa_and_shelf_with_promo(
        self,
        cart_factory,
        cart_item_factory,
        delivery_voucher,
        api_client,
        request,
        region_fixture,
        rate,
    ):
        # Setup
        region = request.getfixturevalue(region_fixture)
        cart, url, api_client = self._setup_cart_and_get_response(
            cart_factory,
            cart_item_factory,
            region,
            api_client,
            items_config=[{'is_two_module_sotty': True}, {'is_jetty': True}],
            delivery_voucher=delivery_voucher,
        )

        # Need to add voucher inside the mock context
        with mock.patch(
            'gallery.models.models.Jetty.get_assembly_price',
            return_value=self.JETTY_ASSEMBLY_PRICE,
        ):
            CartService(cart).add_voucher(delivery_voucher)
            response = api_client.get(url)

        cart_items = self._assert_cart_items(response)

        # Expected values
        sotty_delivery_price = SOTTY_DELIVERY_PRICE_MAPPER[NEAR][TWO_MODULES]
        expected_region_delivery_price = self._calculate_price_for_region(
            sotty_delivery_price, rate
        )
        promo_sotty_delivery_price = self._calculate_promo_price(sotty_delivery_price)
        expected_region_promo_price = self._calculate_price_for_region(
            promo_sotty_delivery_price, rate
        )

        # Assertions
        assert len(cart_items) == 2

        # Find sofa and shelf items
        sofa_item = next(
            (item for item in cart_items if item['content_type'] == 'sotty'), None
        )
        shelf_item = next(
            (item for item in cart_items if item['content_type'] == 'jetty'), None
        )

        assert sofa_item is not None
        assert shelf_item is not None

        # Sofa assertions
        assert sofa_item['deliveryPrice'] == sotty_delivery_price
        assert sofa_item['regionDeliveryPrice'] == expected_region_delivery_price
        assert sofa_item['deliveryPromo'] is True
        assert sofa_item['regionDeliveryPromoPrice'] == expected_region_promo_price

        # Shelf assertions
        assert shelf_item['deliveryPrice'] == 0
        assert shelf_item['regionDeliveryPrice'] == 0
        assert shelf_item['deliveryPromo'] is False
        assert shelf_item['regionDeliveryPromoPrice'] == 0
