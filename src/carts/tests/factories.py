import factory

from custom.enums import (
    ShelfType,
    Sofa01Color,
)
from gallery.enums import FurnitureCategory
from orders.enums import OrderStatus
from pricing_v3.tests.factories import PriceItemAbstractModelFactory
from services.enums import AdditionalServiceKind

# A marker for cases where no order should be created.
NO_ORDER = object()


class CartFactory(factory.django.DjangoModelFactory):
    owner = factory.SubFactory('user_profile.tests.factories.UserFactory')
    region = factory.SubFactory('regions.tests.factories.RegionFactory', germany=True)

    class Meta:
        model = 'carts.Cart'

    @factory.post_generation
    def order(self, create, extracted, **kwargs):
        from orders.tests.factories import OrderFactory

        if not create or extracted is NO_ORDER:
            return

        if extracted:
            self.order = extracted
        else:
            if not self.order:
                self.order = OrderFactory(
                    owner=self.owner,
                    region=self.region,
                    status=OrderStatus.DRAFT,
                    cart=self,
                    **kwargs,
                )

    @factory.post_generation
    def items(self, create, extracted, **kwargs):
        from carts.tests.factories import CartItemFactory

        if not create:
            return

        if extracted:
            self.items.add(*extracted)
        elif extracted is None:
            self.items.add(*[CartItemFactory(cart=self, **kwargs) for _ in range(2)])


class CartItemFactory(PriceItemAbstractModelFactory):
    cart = factory.SubFactory('carts.tests.factories.CartFactory')
    cart_item = factory.SubFactory(
        'gallery.tests.factories.JettyFactory',
        owner=factory.SelfAttribute('..cart.owner'),
    )
    region = factory.SelfAttribute('.cart.region')

    class Meta:
        model = 'carts.CartItem'

    class Params:
        is_sample_box = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.SampleBoxFactory',
                owner=factory.SelfAttribute('..cart.owner'),
            ),
        )
        is_jetty = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.JettyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
            ),
        )
        is_sotty = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
            ),
        )
        is_two_module_sotty = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
                two_modules=True,
            ),
        )
        is_watty = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.WattyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
            ),
        )
        is_sofa_corduroy = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
                materials=[Sofa01Color.CORDUROY_ECRU],
            ),
        )
        is_sofa_wool = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.SottyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
                materials=[Sofa01Color.REWOOL2_BROWN],
            ),
        )
        is_tone_wardrobe = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.WattyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
                shelf_type=ShelfType.TYPE03,
                shelf_category=FurnitureCategory.WARDROBE,
            ),
        )
        is_edge_wardrobe = factory.Trait(
            cart_item=factory.SubFactory(
                'gallery.tests.factories.WattyFactory',
                owner=factory.SelfAttribute('..cart.owner'),
                shelf_type=ShelfType.TYPE13,
                shelf_category=FurnitureCategory.WARDROBE,
            ),
        )
        is_old_sofa_collection = factory.Trait(
            cart_item=factory.SubFactory(
                'services.tests.factories.AdditionalServiceFactory',
                kind=AdditionalServiceKind.OLD_SOFA_COLLECTION,
            ),
        )
