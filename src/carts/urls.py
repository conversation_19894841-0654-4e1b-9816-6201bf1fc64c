from django.conf import settings
from django.urls import (
    include,
    path,
)

from rest_framework import routers

from carts.views import (
    CartItemEnableAssemblyAPIView,
    CartItemQuantityChangeAPIView,
    CartViewSet,
    MailingCartStatusAPIView,
)

router = routers.SimpleRouter()
router.register('cart', CartViewSet, basename='cart-v2')
router.register(
    'mailing-cart-status',
    MailingCartStatusAPIView,
    basename='mailing-cart-status',
)

urlpatterns = [
    path('', include(router.urls)),
    path(
        'cart/item/<int:item_id>/increase/',
        CartItemQuantityChangeAPIView.as_view(),
        {'action': 'increase'},
        name='cart-item-increase',
    ),
    path(
        'cart/item/<int:item_id>/decrease/',
        CartItemQuantityChangeAPIView.as_view(),
        {'action': 'decrease'},
        name='cart-item-decrease',
    ),
]

if settings.IS_DEV:
    urlpatterns.append(
        path(
            'cart/item/<int:item_id>/assembly/',
            CartItemEnableAssemblyAPIView.as_view(),
            name='cart-item-assembly',
        )
    )
