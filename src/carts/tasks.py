from datetime import timedelta

from django.contrib.auth.models import User
from django.core.paginator import Paginator
from django.db import transaction
from django.db.models import Count
from django.utils import timezone

from celery import shared_task

from carts.choices import CartStatusChoices
from carts.models import Cart
from user_profile.choices import UserType


@shared_task
def clean_expired_carts():
    """Remove inactive carts."""
    expiration_date = timezone.now() - timedelta(weeks=12)  # 3 months inactive
    carts = (
        Cart.objects.select_related('owner', 'owner__profile', 'order')
        .annotate(owner_orders_count=Count('owner__order'))
        .filter(
            order__isnull=True,
            owner__profile__first_name='',
            owner__profile__last_name='',
            owner__profile__email='',
            owner__profile__user_type=UserType.GUEST_CUSTOMER,
            status__in=[CartStatusChoices.ACTIVE],
            updated_at__lte=expiration_date,
            owner__last_login__lte=expiration_date,
            owner_orders_count=0,
        )
        .values_list('id', 'owner_id')
    )

    paginator = Paginator(carts, 100)
    for page_number in paginator.page_range:
        page = paginator.page(page_number)
        carts_ids = [cart[0] for cart in page]
        owner_ids = [cart[1] for cart in page]
        with transaction.atomic():
            Cart.objects.filter(id__in=carts_ids).delete()
            User.objects.filter(
                id__in=owner_ids,
                jetty__isnull=True,
                watty__isnull=True,
                samplebox__isnull=True,
            ).delete()
