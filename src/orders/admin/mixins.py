class OrderAdminMixin:
    fieldsets = (
        (
            None,
            {
                'fields': (
                    'order_pretty_id',
                    'parent_order',
                    'chosen_payment_method',
                    'assembly',
                    'order_type',
                    'cached_items_type',
                    'owner',
                    'status_previous',
                    'status',
                    'order_notes',
                    'notes',
                    'cs_notes',
                    'order_source',
                    'package_tracking',
                    'paymant_status_history',
                    'additional_text',
                    'used_promo',
                    'used_promo_config',
                    'region',
                    'currency',
                    'order_placed_email_sent',
                    'showed_confirmation',
                    'klarna_email_notification_for_accounting',
                    'klarna_capture_attempt_date',
                    'is_chargeback',
                    'ab_tests',
                    'sent_invoice_date',
                    'returning_client',
                    'is_estimated_delivery_date',
                    'token',
                    'created_at',
                    'estimated_delivery_time',
                    'first_item_added_at',
                    'items_changed_at',
                    'price_updated_at',
                    'placed_at',
                    'paid_at',
                    'settled_at',
                    'payable_booking_date',
                    'delay_notification_sent',
                    'serialized_logistic_info',
                    'is_barter',
                )
            },
        ),
        (
            'Address Details',
            {
                'fields': (
                    'email',
                    'company_name',
                    'first_name',
                    'last_name',
                    'phone_prefix',
                    'phone',
                    'street_address_1',
                    'street_address_2',
                    'city',
                    'postal_code',
                    'country',
                    'country_area',
                    'vat',
                    'hard_parking',
                    'above_3rd_floor',
                    'no_elevator',
                    'floor_number',
                )
            },
        ),
        (
            'Invoice Related',
            {
                'fields': (
                    'invoice_for_company',
                    'invoice_email',
                    'invoice_company_name',
                    'invoice_first_name',
                    'invoice_last_name',
                    'invoice_street_address_1',
                    'invoice_street_address_2',
                    'invoice_city',
                    'invoice_postal_code',
                    'invoice_country',
                    'invoice_country_area',
                    'invoice_vat',
                )
            },
        ),
        (
            'Price Related',
            {
                'fields': (
                    'region_vat',
                    'vat_type',
                    'vat_rate',
                    ('total_price', 'total_price_net'),
                    ('region_total_price', 'region_total_price_net'),
                    ('promo_amount', 'promo_amount_net'),
                    ('region_promo_amount', 'region_promo_amount_net'),
                )
            },
        ),
        (
            'Switch Related',
            {
                'fields': (
                    'switch_status',
                    'source_order_item',
                    'target_order_item',
                    'completed_target_order_items',
                    ('source_total_price', 'source_total_price_net'),
                    ('source_region_total_price', 'source_region_total_price_net'),
                )
            },
        ),
        (
            'Extra Discount Related',
            {
                'fields': (
                    'extra_discount_status',
                    'completed_used_promos',
                    (
                        'source_extra_discount_total_price',
                        'source_extra_discount_total_price_net',
                    ),
                    (
                        'source_extra_discount_region_total_price',
                        'source_extra_discount_region_total_price_net',
                    ),
                )
            },
        ),
    )
