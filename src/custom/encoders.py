import decimal
import json


class ExplicitDecimalJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, decimal.Decimal):
            return f'Decimal_{obj}'
        return super().default(obj)


class ExplicitDecimalJSONDecoder(json.JSONDecoder):
    def __init__(self, *args, **kwargs):
        kwargs['object_hook'] = self._object_hook_with_decimal
        super().__init__(*args, **kwargs)

    def _object_hook_with_decimal(self, data):
        for key, value in data.items():
            if not value:
                continue
            if isinstance(value, str) and 'Decimal_' in value:
                split_value = value.split('_')
                data[key] = decimal.Decimal(split_value[1])
        return data
