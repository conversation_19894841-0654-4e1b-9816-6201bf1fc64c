from functools import wraps
from typing import TYPE_CHECKING

from django.contrib import messages
from django.shortcuts import redirect
from django.urls import reverse

if TYPE_CHECKING:
    from django.contrib.admin import ModelAdmin


def get_admin_changelist_by_modeladmin(modeladmin: 'ModelAdmin') -> str:
    return reverse(
        f'admin:{modeladmin.opts.app_label}_{modeladmin.opts.model_name}_changelist',
    )


def group_member_required(group_name):
    def decorator(view_func):
        @wraps(view_func)
        def wrapper(modeladmin, request, queryset):
            if not request.user.groups.filter(name=group_name).exists():
                modeladmin.message_user(
                    request,
                    f'This action is available only for group `{group_name}`',
                    level=messages.WARNING,
                )
                url = request.META.get(
                    'HTTP_REFERER', get_admin_changelist_by_modeladmin(modeladmin)
                )
                return redirect(url)
            return view_func(modeladmin, request, queryset)

        return wrapper

    return decorator
