from django.apps import AppConfig
from django.contrib.admin import ModelAdmin


class CustomConfig(AppConfig):
    name = 'custom'
    verbose_name = 'Custom'

    def ready(self):
        from cstm_be.admin import response_action

        # patch standard Django's `ModelAdmin` class to setup actions usage counting

        ModelAdmin.response_action = response_action

        from django.contrib.auth.models import User

        def save(self, *args, **kwargs):
            if hasattr(self, 'profile') and self.profile.user_type == 4:
                old = User.objects.filter(pk=self.pk).last()
                if old and 'tylko.com' in old.email and 'tylko.com' not in self.email:
                    raise ValueError('Something try to change CS user email!')
            super(User, self).save(*args, **kwargs)

        User.save = save

        super().ready()
