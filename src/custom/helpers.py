from datetime import (
    date,
    timedelta,
)

from custom.constants import POLISH_HOLIDAYS


def add_business_days(
    from_date: date,
    business_days_to_add: int,
    reverse: bool = False,
    check_holidays: bool = False,
) -> date:
    """Calculate the date after number of business days from start date"""
    while business_days_to_add > 0:
        days_to_add = -1 if reverse else 1
        from_date += timedelta(days=days_to_add)
        weekday = from_date.weekday()
        if weekday >= 5 or (check_holidays and from_date in POLISH_HOLIDAYS):
            continue
        business_days_to_add -= 1
    return from_date


def calculate_calendar_week_range(calendar_week_date, offset=7):
    """Returns first monday-friday range from passed date with offset."""
    week_day = calendar_week_date.weekday()
    start_date = calendar_week_date + timedelta(days=(offset - week_day))
    end_date = start_date + timedelta(days=4)

    return start_date, end_date


def calculate_day_in_range_by_percentage(earlier_date, later_date, percentage):
    delta = later_date - earlier_date
    percentage_range = delta * percentage / 100
    return earlier_date + percentage_range
