from openpyxl.styles.alignment import Alignment
from openpyxl.styles.borders import (
    Border,
    Side,
)
from openpyxl.styles.fills import PatternFill
from openpyxl.styles.fonts import Font

dotted_border = Border(
    left=Side('dotted'),
    right=Side('dotted'),
    top=Side('dotted'),
    bottom=Side('dotted'),
)
thin_border = Border(
    left=Side('thin'),
    right=Side('thin'),
    top=Side('thin'),
    bottom=Side('thin'),
)

black_background = PatternFill(fgColor='000000', patternType='solid')
ice_blue_background = PatternFill(fgColor='DCF3FF', patternType='solid')
pale_blue_background = PatternFill(fgColor='A2CCFA', patternType='solid')
gray25_background = PatternFill(fgColor='404040', patternType='solid')
gray50_background = PatternFill(fgColor='7f7f7f', patternType='solid')
gray80_background = PatternFill(fgColor='222222', patternType='solid')
gray70_background = PatternFill(fgColor='444444', patternType='solid')
light_gray_background = PatternFill(fgColor='BFBFBF', patternType='solid')
purple_background = PatternFill(fgColor='dda0dd', patternType='solid')
yellow_background = PatternFill(fgColor='ffff00', patternType='solid')
red_background = PatternFill(fgColor='ff0000', patternType='solid')
indigo_background = PatternFill(fgColor='4b0082', patternType='solid')
ivory_background = PatternFill(fgColor='FFFFF0', patternType='solid')
sea_green_background = PatternFill(fgColor='2E8B57', patternType='solid')
rose_background = PatternFill(fgColor='ff007f', patternType='solid')
brown_background = PatternFill(fgColor='A52A2A', patternType='solid')

white_font = Font(color='ffffff')
black_font = Font(color='000000')
bold_font = Font(bold=1)

center_alignment = Alignment(horizontal='center', vertical='center', wrap_text=True)
horizontal_left_alignment = Alignment(
    vertical='center',
    horizontal='left',
    wrap_text=True,
)

top_header_style = {
    'fill': black_background,
    'font': white_font,
    'alignment': horizontal_left_alignment,
}
summary_style = {
    'border': thin_border,
    'alignment': horizontal_left_alignment,
}
bold_style = {
    'font': bold_font,
    'alignment': center_alignment,
}
border_style = {
    'border': thin_border,
    'alignment': center_alignment,
}
batch_type_header_style = {'fill': ice_blue_background}
batch_header_base = {
    'alignment': center_alignment,
    'border': dotted_border,
}
batch_white_header = {
    **batch_header_base,
    'fill': gray25_background,
}
batch_gray_header = {
    **batch_header_base,
    'fill': gray50_background,
}
batch_black_header = {
    **batch_header_base,
    'fill': gray80_background,
    'font': white_font,
}
batch_purple_header = {
    **batch_header_base,
    'font': white_font,
    'fill': purple_background,
}
batch_yellow_header = {
    **batch_header_base,
    'fill': yellow_background,
}
batch_ceramic_header = {
    **batch_header_base,
    'fill': red_background,
}
batch_indygo_header = {
    **batch_header_base,
    'fill': indigo_background,
}
batch_beige_header = {
    **batch_header_base,
    'fill': ivory_background,
}
batch_mint_header = {
    **batch_header_base,
    'fill': sea_green_background,
}
batch_red_header = {
    **batch_header_base,
    'fill': red_background,
}
batch_pink_header = {
    **batch_header_base,
    'fill': rose_background,
}
batch_ash_header = {
    **batch_header_base,
    'fill': ivory_background,
}
batch_oak_header = {
    **batch_header_base,
    'fill': brown_background,
}
