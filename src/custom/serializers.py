from typing import Iterable

import inflection

from rest_framework import serializers


class ShelfStateSerializer(serializers.Serializer):
    log_created_at = serializers.CharField()
    shelf_id = serializers.IntegerField()
    source = serializers.ChoiceField(
        choices=('add_to_cart', 'save_for_later', 'view', 'transaction')
    )
    pagepath = serializers.CharField(allow_blank=True, allow_null=True)
    user_id = serializers.IntegerField()


class GoogleRecaptchaSerializer(serializers.Serializer):
    token = serializers.CharField()


def dynamic_serializer(instance_model, instance_serializer, instance_excluded_fields):
    class DynamicSerializer(instance_serializer):
        class Meta:
            model = instance_model
            exclude = instance_excluded_fields

    return DynamicSerializer


class CamelizeSelectedKeysMixin:
    CAMEL_CASE_FIELDS: Iterable[str] = ()

    def to_representation(self, instance):
        data = super().to_representation(instance)
        return self._camelize_keys(data, self.CAMEL_CASE_FIELDS)

    def _camelize_keys(self, data: dict, keys: Iterable[str]) -> dict:
        for key in keys:
            if key not in data:
                continue
            formatted_key = inflection.camelize(key, uppercase_first_letter=False)
            data[formatted_key] = data.pop(key)
        return data
