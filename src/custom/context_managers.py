from typing import (
    Callable,
    Optional,
)
from urllib.parse import urljoin

from django.conf import settings
from django.core.files.base import ContentFile
from django.urls import reverse

from celery import states

from custom.export_file_strategies import csv_export_strategy
from custom.models.models import DocumentRequest
from custom.utils.emails import send_html_mail


class DocumentRequestWithStatus:
    def __init__(self, document_request_id: int, filename: str):
        self.document_request_id = document_request_id
        self.filename = filename

    def __enter__(self):
        self.document_request = DocumentRequest.objects.get(pk=self.document_request_id)
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type:
            self.document_request.status = states.FAILURE
            self.document_request.exception_meta = {
                'exception_type': str(exc_type),
                'exception_value': str(exc_val),
            }
        else:
            self.document_request.status = states.SUCCESS
        self.document_request.save()
        send_html_mail(
            'mail_document_request_notification.html',
            'Tylko notification',
            context={
                'status': self.document_request.get_status_display(),
                'created_at': self.document_request.created_at,
                'admin_url': urljoin(
                    settings.SITE_URL,
                    reverse(
                        'admin:custom_documentrequest_change',
                        args=(self.document_request.pk,),
                    ),
                ),
            },
            to=self.document_request.requested_by.email,
        )

    def export(
        self,
        func: Callable,
        args=(),
        kwargs: Optional[dict] = None,
        export_strategy: Callable = csv_export_strategy,
    ):
        kwargs = kwargs or {}

        result = func(*args, **kwargs)
        file_result = export_strategy(result['content'], result.get('header'))
        self.document_request.file.save(self.filename, file_result)

    def export_with_file(
        self,
        func: Callable,
        args=(),
        kwargs: Optional[dict] = None,
    ):
        kwargs = kwargs or {}

        file_result = func(*args, **kwargs)
        self.document_request.file.save(self.filename, file_result)

    def export_with_report_file(
        self,
        func: Callable,
        args=(),
        kwargs: Optional[dict] = None,
    ):
        kwargs = kwargs or {}

        report_file = func(*args, **kwargs)
        self.document_request.file.save(self.filename, ContentFile(report_file.content))
