from django.db.models import TextChoices


class UrlReverseAddresses(TextChoices):
    """Enum class for reverse url addresses

    After the switch to nuxt from Django templates,
    we are no longer able to use 'reverse'
    Method to get URL address. But still,
    we need to use full URLs for example inside mailing
    braze or feeds. So this enum class is created to store
    all URLs in one place as a main source of truth.
    """

    ACCOUNT = '/account'
    LOGIN = '/login'
