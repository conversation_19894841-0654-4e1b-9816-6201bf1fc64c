import functools
import time

from threading import <PERSON><PERSON><PERSON>


def expiring_lru_cache(ttl: int, maxsize: int = 128, typed: bool = False):
    """Thin wrapper around functools.lru_cache, clears cache after *ttl* seconds"""

    def decorator(func):
        cached_func = functools.lru_cache(maxsize=maxsize, typed=typed)(func)
        expiration = int(time.monotonic()) + ttl
        lock = RLock()

        @functools.wraps(cached_func)
        def wrapper(*args, **kwargs):
            nonlocal expiration
            with lock:
                if int(time.monotonic()) >= expiration:
                    cached_func.cache_clear()
                    expiration = int(time.monotonic()) + ttl
                return cached_func(*args, **kwargs)

        wrapper.cache_info = cached_func.cache_info
        wrapper.cache_clear = cached_func.cache_clear
        return wrapper

    return decorator
