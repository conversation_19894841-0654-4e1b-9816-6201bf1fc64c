from django.db import models

from rest_framework.serializers import ModelSerializer
from rest_framework.throttling import SimpleRateThrottle

from custom.utils.fields import SanitizedSerializedCharField


class HeavyThrottle(SimpleRateThrottle):
    """
    I think it's only used because of the `safe_getattr`.
    In py3 can be replaced by `UserRateThrottle`
    """

    scope = 'heavy_throttle'

    def __init__(self):
        pass

    def allow_request(self, request, view):
        self.rate = self.get_rate()
        self.num_requests, self.duration = self.parse_rate(self.rate)
        return super(HeavyThrottle, self).allow_request(request, view)

    def get_cache_key(self, request, view):
        if request.user.is_authenticated:
            ident = request.user.pk
        else:
            ident = self.get_ident(request)
        return self.cache_format % {'scope': self.scope, 'ident': ident}


class SanitizedModelSerializer(ModelSerializer):
    serializer_field_mapping = {
        **ModelSerializer.serializer_field_mapping,
        models.CharField: SanitizedSerialized<PERSON>har<PERSON>ield,
        models.TextField: SanitizedSerialized<PERSON>har<PERSON>ield,
    }
