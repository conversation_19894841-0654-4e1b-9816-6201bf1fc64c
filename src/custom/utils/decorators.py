import csv
import inspect
import io
import logging

from dataclasses import dataclass
from datetime import datetime
from functools import wraps
from typing import (
    Iterable,
    Optional,
    Union,
)

from django.conf import settings
from django.core.cache import cache
from django.core.mail.message import EmailMessage
from django.http import (
    HttpResponse,
    HttpResponseRedirect,
)
from django.utils import timezone
from django.utils.html import format_html

from custom.models.models import DocumentRequest

logger = logging.getLogger('cstm')


def cache_function(*args, **kwargs):
    func = None
    if len(args) == 1 and __builtins__['callable'](args[0]):
        func = args[0]
    cache_period = kwargs.get('cache_period', None) if not func else None

    def _kpi_cache_value(func):
        @wraps(func)
        def _decorator(*args, **kwargs):
            kwargs_hash = hash(frozenset(list(kwargs.items())))
            cache_key = 'function_cache_%s_%s_%s' % (
                func.__name__,
                'static',
                kwargs_hash,
            )
            value = cache.get(cache_key)
            if value is not None:
                return value
            value = func(*args, **kwargs)
            cache.set(cache_key, value, cache_period)
            return value

        return _decorator

    return _kpi_cache_value(func) if func else _kpi_cache_value


def cache_model_method(*args, **kwargs):
    func = None
    if len(args) == 1 and __builtins__['callable'](args[0]):
        func = args[0]
    cache_period = kwargs.get('cache_period', None) if not func else None
    ignore_args = kwargs.get('ignore_args', False)

    def _kpi_cache_value(func):
        @wraps(func)
        def _decorator(self, *args, **kwargs):
            if not hasattr(self, 'pk') or self.pk is None:
                return func(self, *args, **kwargs)
            dont_use_cache = kwargs.pop('dont_use_cache', None)
            if dont_use_cache:
                return func(self, *args, **kwargs)
            kwargs_hash = '' if ignore_args else hash(frozenset(list(kwargs.items())))
            cache_key = 'methods_cache_%s_%s_%s_%s' % (
                self.__class__.__name__,
                self.pk,
                func.__name__,
                kwargs_hash,
            )
            value = cache.get(cache_key)
            if value is not None:
                return value
            value = func(self, *args, **kwargs)
            cache.set(cache_key, value, cache_period)
            return value

        return _decorator

    return _kpi_cache_value(func) if func else _kpi_cache_value


def clear_cache_for_kwargs(instance, func, **kwargs):
    kwargs_hash = hash(frozenset(kwargs.items()))
    cache_key = 'methods_cache_{}_{}_{}_{}'.format(
        instance.__class__.__name__,
        instance.pk,
        func.__name__,
        kwargs_hash,
    )
    cache.delete(cache_key)


def notify_after(
    subject='Task finished',
    body='Task finished',
    to=tuple(),
    attachment_base_name=None,
    attachment_extension=None,
    attachment_mime=None,
):
    """
    @notify_after(subject='Test function finished processing')
    def test_function():
        print 'Important calculations'
    test_function(__notify_to=['<EMAIL>', '<EMAIL>'])
    """

    def _notify_after(func):
        @wraps(func)
        def _decorator(*args, **kwargs):
            _subject = subject
            _body = body
            _to = to
            if '__subject' in kwargs:
                _subject = kwargs['__subject']
                del kwargs['__subject']
            if '__body' in kwargs:
                _body = kwargs['__body']
                del kwargs['__body']
            if '__to' in kwargs:
                _to = kwargs['__to']
                del kwargs['__to']
            func_args = inspect.getargspec(func)[0]
            if len(func_args) == 0:
                result = func()
            else:
                result = func(*args, **kwargs)
            valid_format = isinstance(result, (io.StringIO, io.BytesIO))

            if _to is not None and len(_to) > 0 and _subject is not None:
                message = EmailMessage(
                    subject=_subject,
                    body=_body,
                    from_email='Tylko <<EMAIL>>',
                    to=_to,
                )
                attachment = attachment_extension and attachment_mime
                if attachment and valid_format:
                    base_name = (
                        attachment_base_name if attachment_base_name else func.__name__
                    )
                    message.attach(
                        '{}_{}.{}'.format(
                            base_name,
                            timezone.now().strftime('%Y%m%d'),
                            attachment_extension,
                        ),
                        result.getvalue(),
                        attachment_mime,
                    )
                message.send()
            if valid_format and not result.closed:
                result.close()
            return result

        return _decorator

    return _notify_after


@dataclass
class CSVObject:
    file_name: str
    rows: Union[list, object]
    add_timestamp: bool = True
    header_row: Optional[Iterable[str]] = None


def csv_export(func):
    @wraps(func)
    def func_wrapper(modeladmin, request, queryset):
        csv_object = func(modeladmin, request, queryset)
        response = HttpResponse(content_type='text/csv')

        file_name = f'filename={csv_object.file_name}'
        if csv_object.add_timestamp:
            file_name = f'{file_name}_{datetime.today().strftime("%y%m%d_%H%M")}'

        response['Content-Disposition'] = f'attachment; {file_name}.csv'
        writer = csv.writer(response, delimiter=';')
        if csv_object.header_row:
            writer.writerow(csv_object.header_row)
        for row in csv_object.rows:
            writer.writerow(row)
        return response

    return func_wrapper


def production_only(fun):
    @wraps(fun)
    def _decorator(*args, **kwargs):
        if not settings.IS_PRODUCTION:
            return
        return fun(*args, **kwargs)

    return _decorator


def with_document_async_message(admin_action):
    @wraps(admin_action)
    def func_wrapper(modeladmin, request, queryset, **kwargs):
        document_request = DocumentRequest.objects.create(
            requested_by=request.user,
            action_name=request.POST['action'],
        )
        admin_action(modeladmin, document_request, request, queryset, **kwargs)
        message = format_html(
            f'Document request was created, when ready you can '
            f'find it here: {document_request.get_admin_url()}'
        )
        modeladmin.message_user(request, message)
        return HttpResponseRedirect(request.get_full_path())

    return func_wrapper
