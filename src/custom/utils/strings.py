import binascii
import os
import os.path

from django.conf import settings

from cryptography.hazmat.backends import default_backend
from cryptography.hazmat.primitives.ciphers import (
    Cipher,
    algorithms,
    modes,
)


def sanitize_incoming_string(string, max_length=None):
    if type(string) not in [str, str]:
        return string
    if not isinstance(string, str):
        string = str(string, errors='ignore')
    if max_length is not None:
        string = string[:max_length]
    return string


def encrypt_string(value):
    value = value.encode('utf-8') if isinstance(value, str) else value
    padding = algorithms.AES.block_size - len(value) % algorithms.AES.block_size
    if padding and padding < algorithms.AES.block_size:
        value += b'\0' + os.urandom(padding - 1)

    key = bytes(settings.SECRET_KEY[:32], encoding='utf-8')
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    encryptor = cipher.encryptor()
    ct = encryptor.update(value) + encryptor.finalize()
    return binascii.b2a_hex(ct)[:32]


def decrypt_string(value):
    key = bytes(settings.SECRET_KEY[:32], encoding='utf-8')
    cipher = Cipher(algorithms.AES(key), modes.ECB(), backend=default_backend())
    decryptor = cipher.decryptor()
    dt = decryptor.update(binascii.a2b_hex(value)) + decryptor.finalize()
    return dt.split(b'\0')[0]


def format_price(amount, currency_code='€'):
    return '%.2f %s' % (float(amount), currency_code)
