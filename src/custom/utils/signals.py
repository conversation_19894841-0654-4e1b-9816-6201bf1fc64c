from contextlib import contextmanager

from django.db.models.signals import (
    m2m_changed,
    post_delete,
    post_init,
    post_migrate,
    post_save,
    pre_delete,
    pre_init,
    pre_migrate,
    pre_save,
)


@contextmanager
def suppress_signals(signals=None):
    disconnected_signal_receivers = {}
    suppressed_signals = signals or [
        pre_init,
        post_init,
        pre_save,
        post_save,
        pre_delete,
        post_delete,
        pre_migrate,
        post_migrate,
        m2m_changed,
    ]

    for signal in suppressed_signals:
        disconnected_signal_receivers[signal] = signal.receivers
        signal.receivers = []

    try:
        yield
    finally:
        for signal in disconnected_signal_receivers:
            signal.receivers = disconnected_signal_receivers.get(signal, [])
