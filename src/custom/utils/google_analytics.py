import logging
import os

from datetime import (
    date,
    datetime,
    timedelta,
)

from django.conf import settings
from django.core.cache import cache
from django.utils import timezone

from google.oauth2 import service_account
from googleapiclient.discovery import build

from custom.utils.mixins import Singleton

logger = logging.getLogger('cstm')


def google_analytics_funnel_fetch(
    widget_set, cache_key, cache_period=86400, multi_dimensions=False
):
    profile_id = settings.GA_PROFILE_ID_WEB
    if settings.DEBUG is True:
        return None
    try:
        ga = GoogleAnalyticsHelper(settings.GA_ACCOUNT, settings.GA_ACCOUNT_KEY_PATH)
    except IOError:
        logger.exception('Error while initializing GoogleAnalyticsHelper.')
        return

    data = {'items': []}

    today = date.today()
    if widget_set['date_from'] == 'yesterday':
        date_from = 'yesterday'
    elif widget_set['date_from'] == 'this_month':
        date_from = date(today.year, today.month, 1).strftime('%Y-%m-%d')
    elif widget_set['date_from'] == '7d':
        date_from = '7daysAgo'
    elif widget_set['date_from'] == '30d':
        date_from = '30daysAgo'
    else:
        date_from = widget_set.get('date_from', 'today')

    date_to = widget_set.get('date_to', None)
    if not date_to:
        now = timezone.now()
        end_next_day = timezone.make_aware(
            datetime(now.year, now.month, now.day)
        ) + timedelta(days=1)
        date_to = end_next_day.strftime('%Y-%m-%d')
    for step in widget_set['steps']:
        combined_filters = {
            'value': None,
            'dimensions': None,
            'segment': None,
            'name': '',
        }
        for k in combined_filters:
            combined = (
                [ga_filter[k] for ga_filter in step['filters'] if k in ga_filter]
                if 'filters' in step
                else []
            )
            combined_filters[k] = (
                ','.join(combined) if len(combined) > 0 else combined_filters[k]
            )
        result = ga.get_data(
            profile_id,
            date_from,
            date_to,
            step['metric']['value'],
            filters=combined_filters['value'],
            dimensions=combined_filters['dimensions'],
            segment=combined_filters['segment'],
        )

        if 'rows' in result and not multi_dimensions:
            if combined_filters['dimensions'] is None:
                value = format_data(result['rows'][0][0])
                if len(combined_filters['name']) > 0:
                    label = ' - '.join(
                        (step['metric']['name'], combined_filters['name'])
                    )
                else:
                    label = step['metric']['name']
                data['items'].append(
                    (
                        value,
                        label,
                    )
                )
            else:
                for row in result['rows']:
                    data['items'].append((row[1], row[0]))
        elif multi_dimensions:
            cache.set(cache_key, result, cache_period)
            return result

    cache.set(cache_key, data, cache_period)
    return data


class GoogleAnalyticsHelper(object, metaclass=Singleton):
    api_name = 'analytics'
    api_version = 'v3'
    scope = ['https://www.googleapis.com/auth/analytics.readonly']

    def __init__(self, service_account_email, key_file_location):
        try:
            credentials = service_account.Credentials.from_service_account_file(
                os.path.join(settings.ROOT_PATH, 'kpi/tylko-62f6acabb248.json'),
                scopes=self.scope,
            )
            self.service = build(
                self.api_name,
                self.api_version,
                credentials=credentials,
            )
        except Exception:
            logger.exception('Could not init GoogleAnalyticsHelper.')

    def get_profile_id(self, profile_name):
        try:
            accounts = self.service.management().accounts().list().execute()
            if accounts.get('items'):
                account = accounts.get('items')[0].get('id')
                properties = (
                    self.service.management()
                    .webproperties()
                    .list(accountId=account)
                    .execute()
                )
                if properties.get('items'):
                    prop = properties.get('items')[0].get('id')
                    profiles = (
                        self.service.management()
                        .profiles()
                        .list(accountId=account, webPropertyId=prop)
                        .execute()
                    )
                    if profiles.get('items'):
                        for profile in profiles.get('items'):
                            if profile.get('name') == profile_name:
                                return profile.get('id')
        except AttributeError:
            logger.exception('Could not get_profile_id GoogleAnalyticsHelper.')
            return None
        return None

    def get_data(
        self,
        profile_id,
        start_date,
        end_date,
        metrics,
        dimensions=None,
        filters=None,
        segment=None,
        **kwargs,
    ):
        return (
            self.service.data()
            .ga()
            .get(
                ids='ga:' + str(profile_id),
                start_date=start_date,
                end_date=end_date,
                metrics=metrics,
                dimensions=dimensions,
                filters=filters,
                segment=segment,
                **kwargs,
            )
            .execute()
        )


def format_data(data, multiply_percents=False):
    formatted_data = data
    if not type(data) in (
        float,
        int,
    ):
        try:
            formatted_data = float(data)
        except ValueError:
            pass
        try:
            formatted_data = int(data)
        except ValueError:
            pass
    if isinstance(formatted_data, float):
        if formatted_data < 1 and multiply_percents:
            formatted_data *= 100
        formatted_data = '%.2f%%' % formatted_data
    return formatted_data
