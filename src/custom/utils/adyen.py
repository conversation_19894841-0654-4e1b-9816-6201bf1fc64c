import base64
import binascii
import hashlib
import hmac
import logging

from collections import OrderedDict
from datetime import (
    datetime,
    timedelta,
)
from hashlib import sha1

from django.conf import settings
from django.utils.encoding import force_str
from django.utils.translation import to_locale

from custom.enums import LanguageEnum
from custom.models import (
    Countries,
    GlobalSettings,
)

logger = logging.getLogger('cstm')


def adyen_sign_data(parms, hmac_key):
    excluded_fields = (
        'billingAddresSig',
        'deliveryAddresSig',
        'shopperSig',
        'companySig',
        'docQuoteSig',
    )

    def escape_val(val):
        return force_str(val).replace('\\', '\\\\').replace(':', '\\:')

    parms_for_signing = {k: parms[k] for k in parms if k not in excluded_fields}
    parms_ordered = OrderedDict(
        sorted(list(parms_for_signing.items()), key=lambda t: t[0])
    )
    signing_string = ':'.join(
        map(escape_val, list(parms_ordered.keys()) + list(parms_ordered.values()))
    )
    hm = hmac.new(hmac_key, bytearray(signing_string, 'utf-8'), hashlib.sha256)
    parms['merchantSig'] = base64.b64encode(hm.digest())
    return parms


def adyen_structure_helper(
    marchant_reference,
    amount,
    shopper_email,
    shopper_reference,
    live=None,
    skin_override=False,
    shopper_locale='en_GB',
    country_code='GB',
    brand_code=None,
    issuer_id=None,
    ship_days=56,
    currency_code='EUR',
    additional_data=None,
    legacy_structure=False,
):

    if country_code == 'UK':
        country_code = 'GB'
    adyen_settings = get_current_payment_settings(live)
    skincode = adyen_settings.get('DEFAULT_SKIN')
    merchant_account = adyen_settings.get('MERCHANT_ACCOUNT')
    hmac_key = binascii.a2b_hex(adyen_settings.get('ADYEN_HMAC_KEY'))

    raw_data = {
        'skinCode': skincode if skin_override is False else skin_override,
        'merchantAccount': merchant_account,
        'merchantReference': marchant_reference,
        'paymentAmount': int(float(amount) * 100),  # amount in cents
        # currency, in the 3 letter format (see Adyen docs)
        'currencyCode': currency_code,
        'shipBeforeDate': datetime.now() + timedelta(days=ship_days),
        'shopperEmail': shopper_email,
        'shopperLocale': shopper_locale,
        'shopperReference': shopper_reference,
        'sessionValidity': timedelta(hours=24),  # how long is the payment session valid
        'countryCode': country_code,
    }

    if additional_data:
        raw_data.update(additional_data)

    raw_data['shipBeforeDate'] = raw_data.get('shipBeforeDate').strftime('%Y-%m-%d')
    raw_data['sessionValidity'] = (
        datetime.now() + raw_data.get('sessionValidity')
    ).strftime(
        '%Y-%m-%dT%H:%M:%S+01:00'
    )  # hardcoded warsaw timezone

    if brand_code:
        raw_data['brandCode'] = brand_code
    if issuer_id:
        raw_data['issuerId'] = issuer_id

    # Make sure it's all unicode strings from here on
    for field in list(raw_data.keys()):
        raw_data[field] = str(raw_data[field])

    if legacy_structure:
        plaintext = ''
        signature_fields = [
            'paymentAmount',
            'currencyCode',
            'shipBeforeDate',
            'merchantReference',
            'skinCode',
            'merchantAccount',
            'sessionValidity',
            'shopperEmail',
            'shopperReference',
            'allowedMethods',
            'blockedMethods',
            'shopperStatement',
            'billingAddressType',
            'recurringContract',
            'billingAddressType',
            'deliveryAddressType',
        ]
        for field in signature_fields:
            plaintext += raw_data.get(field, '').encode('utf-8')
        merchant_secret = adyen_settings.get('MERCHANT_SECRET')
        hm = hmac.new(merchant_secret, plaintext, sha1)
        raw_data['merchantSig'] = base64.encodestring(hm.digest()).strip()
        return raw_data

    return adyen_sign_data(raw_data, hmac_key)


def get_locale(language_code, country_code):
    if language_code.lower() in LanguageEnum:
        return LanguageEnum.get_locale(language_code, country_code=country_code)

    country_by_language = Countries.get_country_by_attribute(
        'language_code',
        language_code,
    )
    locale_from_language_code = to_locale(language_code)
    if len(locale_from_language_code) > 2:
        return locale_from_language_code
    elif country_by_language:
        return country_by_language.locale
    return f'{language_code}_{country_code}'


def get_current_payment_settings(live=None):
    if live is None:
        live = GlobalSettings.live_payment_switch()
    return settings.ADYEN_LIVE if live else settings.ADYEN
