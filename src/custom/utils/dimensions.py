from enum import Enum

from django.template.defaultfilters import floatformat
from django.utils.translation import gettext_lazy as _


class Dimensions(object):
    """
    Represents furniture's dimensions
    """

    class DimensionType(Enum):
        HEIGHT = _('height')
        WIDTH = _('width')
        DEPTH = _('depth')

    @staticmethod
    def get_ordering():
        """
        Returns the order in which dimensions should be returned
        :return: :class:`list`
        """
        return (
            Dimensions.DimensionType.HEIGHT,
            Dimensions.DimensionType.WIDTH,
            Dimensions.DimensionType.DEPTH,
        )

    @staticmethod
    def _abbreviate_dimension_type(dimension_type):
        assert isinstance(dimension_type, Dimensions.DimensionType)
        return str(dimension_type.value).upper()[0]

    def __init__(self):
        self._dimensions = {}
        self._dimensions_changed = {}
        for dt in Dimensions.get_ordering():
            self._dimensions[dt] = None
            self._dimensions_changed[dt] = False

    def __iter__(self):
        return iter(self.get_dimensions_list())

    def __str__(self):
        return self.formatted_str()

    def get_dimension(self, dimension_type):
        """
        Returns value of the given dimension
        :param dimension_type: :class:`DimensionType`
        :return: dimension value
        """
        assert isinstance(dimension_type, Dimensions.DimensionType)
        return self._dimensions[dimension_type]

    def set_dimension(self, dimension_type, value):
        """
        Sets value of the given dimension
        :param dimension_type: :class:`DimensionType`
        :param value: value of the dimension
        """
        assert isinstance(dimension_type, Dimensions.DimensionType)
        self._dimensions[dimension_type] = value

    def mark_changed_dimension(self, dimension_type):
        """
        Sets value of the given dimension
        :param dimension_type: :class:`DimensionType`
        :param value: value of the dimension
        """
        assert isinstance(dimension_type, Dimensions.DimensionType)
        self._dimensions_changed[dimension_type] = True

    def get_dimensions(self):
        """
        Returns all dimensions as {{:class:`DimensionType`: :class:`int`}, ]
        :return: :class:`list` of :class:`dict`s
        """
        return self._dimensions

    def get_dimensions_list(self):
        """
        Returns all dimensions as [{'label':<lazy_string>, 'value': :class:`int`}, ]
        :return: :class:`list` of :class:`dict`s
        """
        dimensions_list = []
        for dt in Dimensions.get_ordering():
            value = self._dimensions[dt]
            if value:
                dimensions_list.append(
                    {
                        'label': Dimensions._abbreviate_dimension_type(dt),
                        'value': int(value),
                        'has_changed': self._dimensions_changed[dt],
                    }
                )
        return dimensions_list

    def formatted_str(self, with_x=True, with_html_space=True, include_cm=False):
        """
        Returns all dimensions as a string formatted like
            "Abbr.: value x Abbr.: value ..."
        :param with_x: Whether dimensions should be joined with 'x' character
            or just a space
        :return: :class:`unicode`
        """
        space = '&nbsp;' if with_html_space else ' '
        join_str = space + 'x' + space if with_x else space
        result_list = []
        for d in self.get_dimensions_list():
            result_list.append(
                '%s%s%s'
                % (
                    str(d['label']),
                    space,
                    floatformat(d['value']),
                )
            )
        return '%s%s' % (join_str.join(result_list), ' cm' if include_cm else '')
