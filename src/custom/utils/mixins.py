from django.contrib import messages

from admin_customization.templatetags.admin_tags import can_change


class Singleton(type):
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super(Singleton, cls).__call__(*args, **kwargs)
        return cls._instances[cls]


class ClassNameHashableMixin(object):
    def __key(self):
        return self.__class__.__name__

    def __eq__(self, other):
        return isinstance(self, type(other)) and self.__key() == other.__key()

    def __hash__(self):
        return hash(self.__key())


class ModelStrSummaryMixin(object):
    def __str__(self):
        field_names = [f.name for f in self._meta.fields]
        field_values = [getattr(self, fname) for fname in field_names]
        zipped = list(zip(field_names, field_values))
        fields_summary = ' '.join(['='.join([str(v) for v in z]) for z in zipped])
        return '%s[%s]' % (self.__class__.__name__, fields_summary)

    def __repr__(self):
        field_names = [f.name for f in self._meta.fields]
        field_values = [getattr(self, fname) for fname in field_names]
        zipped = list(zip(field_names, field_values))
        fields_summary = ' '.join(['='.join([str(v) for v in z]) for z in zipped])
        return '%s[%s]' % (self.__class__.__name__, fields_summary)


class ReadOnlyAdminMixin(object):
    def save_model(self, request, obj, form, change):
        if can_change(request.user, form._meta.model._meta):
            obj.save()
        else:
            messages.warning(request, 'Read-only permission')
