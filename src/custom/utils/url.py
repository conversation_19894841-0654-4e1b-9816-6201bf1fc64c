import urllib
import urllib.error
import urllib.parse
import urllib.request

from custom.enums import Platform
from orders.choices import OrderSource


def get_request_source(request):
    user_agent = request.META.get('HTTP_USER_AGENT', '')
    if (
        request.META.get('HTTP_X_TYLKO_PLATFORM', 'missing').lower()
        == Platform.android.value
    ):
        return OrderSource.MOBILE_ANDROID
    elif user_agent.startswith('BestHTTP') and 'iphone' in user_agent:
        return OrderSource.MOBILE_IPHONE
    elif user_agent.startswith('BestHTTP') and 'ipad' in user_agent:
        return OrderSource.MOBILE_IPAD
    else:
        if hasattr(request, 'user_agent') and (
            request.user_agent.is_mobile or request.user_agent.is_tablet
        ):
            return OrderSource.WEB_MOBILE
        else:
            return OrderSource.WEB_DESKTOP


def tracking_utms_from_url(url):
    return {
        qk[qk.index('_') + 1 :]: qv[0]  # noqa E203
        for qk, qv in list(
            urllib.parse.parse_qs(urllib.parse.urlsplit(url).query).items()
        )
        if 'utm_' in qk or 'be_' in qk and len(qv[0]) > 0
    }


def remove_url_params(url: str) -> str:
    return url.split('?')[0].split('#')[0]
