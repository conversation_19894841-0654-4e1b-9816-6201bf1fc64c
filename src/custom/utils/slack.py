import typing

from urllib.parse import urljoin

from django.conf import settings
from django.urls import reverse

import requests

if typing.TYPE_CHECKING:
    from orders.models import OrderToProduction


def log_bigquery_error_to_slack(exception: Exception, table_with_space: str) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': (
                    f'@here Export to bigquery failed: {table_with_space} '
                    f'with following error: {str(exception)}'
                ),  # TODO: More details?
                'channel': 'op4-it',
                'username': '<PERSON><PERSON><PERSON> fails',
                'icon_emoji': ':pikachu:',
            },
        )


def notify_about_vip_order_on_slack(order_id: int) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': f'New VIP order: {order_id}',
                'channel': 'vip',
                'username': 'VIP bot',
                'icon_emoji': ':pikachu:',
            },
        )


def notify_about_influ_order_on_slack(order_id: int) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': f'New INFLU order: {order_id}',
                'channel': 'vip',
                'username': 'INFLU bot',
                'icon_emoji': ':slowpoke:',
            },
        )


def notify_about_big_order_on_slack(text: str) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'b2b_big-orders',
                'username': 'Big Order bot',
                'icon_emoji': ':pikachu:',
            },
        )


def notify_about_b2b_on_slack(text: str) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'b2b-orders',
                'username': 'Tax office',
                'icon_emoji': ':feelsgoodman:',
            },
        )


def notify_about_new_b2b_reward(b2b_reward_link) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': f'New B2B reward: {b2b_reward_link}',
                'channel': 'b2b_mentionme_reward',
                'username': 'B2B reward bot',
            },
        )


def notify_about_auto_batch_with_fix_errors(text) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'need-halp-prod-love-maintenance',
                'username': 'Batchowanie',
            },
        )


def notify_about_automatic_batching_status(text) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'batchowanie',
                'username': 'Automatic batching',
                'icon_emoji': ':wtf-seba:',
            },
        )


def notify_about_op4_errors(text) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'op4-errors',
                'username': 'Advanced AI',
                'icon_emoji': ':bartek-szef:',
            },
        )


def notify_about_sold_samples(text) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'samplepost',
                'username': 'Today news!',
                'icon_emoji': ':dollar:',
            },
        )


def notify_about_wrong_order_to_production(
    order_to_production: 'OrderToProduction', error: dict
) -> None:
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        message = f'Wrong Order: {order_to_production.original_order_id}: ```{error}```'

        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': message,
                'channel': 'op4-vs-ecom',
                'username': 'Process To Production',
                'icon_emoji': ':sos:',
            },
        )


def notify_about_complaint_to_verification(complaint_id: int, dixa_url: str):
    url = urljoin(
        settings.SITE_URL,
        reverse('admin:complaints_complaint_change', args=[complaint_id]),
    )
    message = f'New complaint for verification: {url}, dixa conversation: {dixa_url}'
    notify_about_complaint_verification_status(message)


def notify_about_rejected_complaint(complaint_id: int, dixa_url: str):
    url = urljoin(
        settings.SITE_URL,
        reverse('admin:complaints_complaint_change', args=[complaint_id]),
    )
    message = f'Complaint was rejected: {url}, dixa conversation: {dixa_url}'
    notify_about_complaint_verification_status(message)


def notify_about_accepted_complaint(complaint_id: int, dixa_url: str):
    url = urljoin(
        settings.SITE_URL,
        reverse('admin:complaints_complaint_change', args=[complaint_id]),
    )
    message = f'Complaint was accepted: {url}, dixa conversation: {dixa_url}'
    notify_about_complaint_verification_status(message)


def notify_about_complaint_verification_status(text: str):
    if settings.SLACK_WEBHOOK and settings.IS_PRODUCTION:
        requests.post(
            settings.SLACK_WEBHOOK,
            json={
                'text': text,
                'channel': 'complaint-verification',
                'username': 'Sotty complaint',
                'icon_emoji': ':sos:',
            },
        )
