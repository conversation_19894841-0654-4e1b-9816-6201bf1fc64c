from itertools import chain

from django.utils.functional import Promise
from django.utils.translation import get_language
from django.utils.translation import gettext as _

from custom.enums import (
    GridLabelType,
    ShelfType,
)
from custom.enums.enums import GridLabelValue
from gallery.enums import ShelfPatternEnum
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)
from gallery.slugs import get_slug_for_all_colors
from gallery.types import FurnitureType
from promotions.utils import get_active_promotion
from rating_tool.utils import get_additional_images_for_grid_object


def _get_image_webp_url(image):
    image_webp = None
    if image:
        image_webp = image.image_webp
    image_webp_url = image_webp.url if image_webp else None
    return image_webp_url


def _get_external_render_data(item):
    render = item.grid_all_colors_render
    render_url = render.url if render else None
    render_webp_url = _get_image_webp_url(render)
    render_small = item.grid_all_colors_render_small
    render_small_url = render_small.url if render_small else None
    render_small_webp_url = _get_image_webp_url(render_small)
    hover_render = item.grid_all_colors_hover_render
    hover_render_url = hover_render.url if hover_render else None
    hover_render_webp_url = _get_image_webp_url(hover_render)
    hover_render_small = item.grid_all_colors_hover_render_small
    hover_render_small_url = hover_render_small.url if hover_render_small else None
    hover_render_small_webp_url = _get_image_webp_url(hover_render_small)
    urls = [
        render_webp_url,
        render_url,
        render_small_webp_url,
        render_small_url,
        hover_render_webp_url,
        hover_render_url,
        hover_render_small_webp_url,
        hover_render_small_url,
    ]
    if all(url is None for url in urls):
        return None

    external_render = {
        'main': {
            'lg': {
                'webp': render_webp_url,
                'jpg': render_url,
            },
            'sm': {
                'webp': render_small_webp_url,
                'jpg': render_small_url,
            },
        },
        'hover': {
            'lg': {
                'webp': hover_render_webp_url,
                'jpg': hover_render_url,
            },
            'sm': {
                'webp': hover_render_small_webp_url,
                'jpg': hover_render_small_url,
            },
        },
    }
    return external_render


def _get_large_images(item):
    image = item.grid_all_colors_lg
    image_url = image.url if image else None
    image_webp_url = _get_image_webp_url(image)
    if not image_url and not image_webp_url:
        return None
    return {
        'grid_all_colors_lg': image_url,
        'grid_all_colors_lg_webp': image_webp_url,
    }


def _is_valid_for_voucher(furniture, voucher):
    item_filters = voucher.item_filters
    if not item_filters:
        return True

    return voucher.check_conditions(
        furniture,
        item_filters.get('include', []),
        item_filters.get('exclude', []),
    )


def get_label_promo(furniture, voucher, promotion, promotion_config=None, region=None):
    if not promotion or not voucher:
        return None

    if not promotion_config or not promotion_config.grid_show_promo_value:
        return None

    if region and promotion_config.enabled_regions.exists():
        enabled_regions_ids = [
            region.id for region in promotion_config.enabled_regions.all()
        ]
        if region.id not in enabled_regions_ids:
            return None

    if not _is_valid_for_voucher(furniture, voucher):
        return None

    value = int(voucher.get_discount_value_for_item(furniture))
    if not value:
        return None

    return {
        'value': f'-{value}%',
        'type': GridLabelType.PROMOTION,
        'sale_type': promotion_config.sale_type,
        'translated_name': promotion_config.get_sale_type_display(),
    }


def is_new(furniture: FurnitureType) -> bool:
    shelf_type = ShelfType(furniture.shelf_type)
    new_categories = shelf_type.get_new_categories_for_shelf_type()
    return (
        shelf_type.is_new
        or furniture.material in shelf_type.colors.get_new_colors()
        or furniture.pattern in ShelfPatternEnum.get_new_patterns()
        or furniture.shelf_category in new_categories
    )


def get_label_new(
    furniture: FurnitureType, for_catalogue=False
) -> dict[str, GridLabelValue | Promise] | None:
    if not is_new(furniture=furniture):
        return
    return {
        'value': GridLabelValue.NEW if for_catalogue else _('grid_label_new'),
        'type': GridLabelType.FEATURE,
    }


def get_grid_items_with_usp_for_boards(
    items,
    region,
    create_your_own_usps=True,
):
    furniture_queryset = _get_furniture_queryset(items)
    promotion = get_active_promotion(region=region)
    voucher = getattr(promotion, 'promo_code', None)
    items = _fill_furniture_data(items, furniture_queryset, region, voucher, promotion)
    items = _fill_usps(items)
    if len(items) in {24, 36}:
        items = items[:-1]

    if create_your_own_usps:
        items.append(_create_your_own_usp())

    return items


def _get_furniture_queryset(items):
    jetty_ids = {
        int(board_item[0])
        for board_item in items
        if isinstance(board_item, list) and board_item[2] == 'jetty'
    }
    watty_ids = {
        int(board_item[0])
        for board_item in items
        if isinstance(board_item, list) and board_item[2] == 'watty'
    }
    return {
        'jetty': (
            Jetty.objects.filter(id__in=jetty_ids).prefetch_related('additional_images')
        ),
        'watty': (
            Watty.objects.filter(id__in=watty_ids).prefetch_related('additional_images')
        ),
    }


def _fill_usps(items):
    items_copy = items.copy()
    for i, item_entry in enumerate(items):
        if isinstance(item_entry, list) and item_entry[2] == 'usp':
            item = {'entry_type': 'usp', 'usp_type': item_entry[0]}
            if item['usp_type'] == 'U11':
                item['usp_type'] = 'U12'
                item['instead_of_promo'] = True
                item['header'] = _('martin_grid_product_slot_1_header_1')
                item['paragraph'] = _('')
            elif item['usp_type'] == 'U12':
                item['header'] = _('martin_grid_product_slot_1_header_1')
                item['paragraph'] = _('')
            elif item['usp_type'] == 'U13':
                item['header'] = _('martin_grid_product_slot_2_header_1')
                item['paragraph'] = _('')
            elif item['usp_type'] == 'U21':
                item['header'] = _('martin_grid_content_slot_1_header_1')
                item['paragraph'] = _('martin_grid_content_slot_1_paragraph_1')
            elif item['usp_type'] == 'U22':
                item['header'] = _('martin_grid_content_slot_2_header_1')
                item['paragraph'] = _('martin_grid_content_slot_2_paragraph_1')
            elif item['usp_type'] == 'U23':
                item['header'] = _('martin_grid_content_slot_3_header_1')
                item['paragraph'] = _('martin_grid_content_slot_3_paragraph_1')
            elif item['usp_type'] == 'U31':
                item['header'] = _('martin_grid_material_slot_1_header_1')
                item['paragraph'] = _('martin_grid_material_slot_1_header_2')
            elif item['usp_type'] == 'U32':
                item['header'] = _('martin_grid_material_slot_2_header_1')
                item['paragraph'] = _('martin_grid_material_slot_2_header_2')
            elif item['usp_type'] == 'U33':
                item['header'] = _('martin_grid_material_slot_3_header_1')
                item['paragraph'] = _('martin_grid_material_slot_3_header_2')
            elif item['usp_type'] == 'U34':
                item['header'] = _('martin_grid_product_slot_3_header_1')
                item['paragraph'] = _('martin_grid_product_slot_3_header_1')
            elif item['usp_type'] == 'U35':
                item['header'] = _('martin_grid_product_slot_4_header_1')
                item['paragraph'] = _('martin_grid_product_slot_4_header_1')
            items_copy[i] = item

    return items_copy


def _prepare_furniture_data(furniture, item, material, region, voucher, promotion):
    return {
        'id': furniture.id,
        'entry_type': 'shelf' if isinstance(furniture, Jetty) else 'wardrobe',
        'furniture_category': furniture.furniture_category,
        'physical_product_version': furniture.physical_product_version,
        'configurator_type': furniture.configurator_type,
        'grid_color_class': f'color-{int(item[1])}',
        'grid_color': int(material),
        'grid_all_colors': (
            furniture.grid_all_colors.url if furniture.grid_all_colors else ''
        ),
        'grid_all_colors_webp': (
            furniture.grid_all_colors_webp.url if furniture.grid_all_colors_webp else ''
        ),
        'slugs_all_colors': get_slug_for_all_colors(
            furniture,
            get_language(),
        ),
        'external_render': _get_external_render_data(furniture),
        'shelf_type': furniture.shelf_type,
        'additional_images': get_additional_images_for_grid_object(furniture) or [],
        'large_images': _get_large_images(furniture),
    }


def _fill_furniture_data(items, furniture_queryset, region, voucher, promotion):
    furniture_to_item_map = {(item[0], item[2]): item for item in items}
    for furniture in chain(furniture_queryset['jetty'], furniture_queryset['watty']):
        key = (furniture.id, furniture.furniture_type)
        related_item_entry = furniture_to_item_map[key]
        json_item = _prepare_furniture_data(
            furniture=furniture,
            item=related_item_entry,
            material=related_item_entry[1],
            region=region,
            voucher=voucher,
            promotion=promotion,
        )

        if len(related_item_entry) > 2:
            json_item['additional_dict'] = related_item_entry[3]

        furniture_to_item_map[key] = json_item

    return list(furniture_to_item_map.values())


def _create_your_own_usp():
    return {
        'entry_type': 'usp',
        'usp_type': 'end',
        'filter_used_paragraph': _('martin_grid_configurator_slot_1_header_1'),
        'filter_used_button': _('martin_grid_configurator_slot_1_buton_1'),
        'filter_not_used_paragraph': _('martin_grid_configurator_slot_2_header_1'),
        'filter_not_used_button': _('martin_grid_configurator_slot_2_buton_1'),
        'filter_not_used_secondaryButton': _('martin_grid_configurator_slot_2_buton_2'),
        'filter_not_used_separator': _('martin_grid_configurator_slot_2_header_2'),
    }
