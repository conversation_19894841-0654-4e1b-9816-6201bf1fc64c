import math

from enum import Enum


class TimeHelper(object):
    """
    Represents delivery time for furniture
    """

    class TimeUnit(Enum):
        DAYS = 'days'
        HOURS = 'hours'
        WEEKS = 'weeks'
        MONTHS = 'months'

    def __init__(self, days):
        """
        Creates TimeHelper instance
        :param days: Estimated delivery time in days
        """
        self._days = days

    def get_time(self, time_unit=TimeUnit.DAYS):
        """
        Returns estimated delivery time in given units
        :param time_unit: :class:`TimeUnit` estimated time in days will be converted to
        :return: :class:`int` representing estimated delivery time
        """
        if time_unit == TimeHelper.TimeUnit.HOURS:
            return self._days * 24
        elif time_unit == TimeHelper.TimeUnit.WEEKS:
            return int(math.ceil(self._days / 7.0))
        elif time_unit == TimeHelper.TimeUnit.MONTHS:
            return int(math.ceil(self._days / 30.0))
        else:
            return self._days

    @property
    def days(self):
        """
        :return: Estimated delivery time in days
        """
        return self._days

    @days.setter
    def days(self, days):
        """
        :param days: Estimated delivery time in days
        """
        self._days = days

    @property
    def weeks(self):
        """
        :return: Estimated delivery time in weeks
        """
        return self.get_time(TimeHelper.TimeUnit.WEEKS)

    @weeks.setter
    def weeks(self, weeks):
        """
        :param weeks: Estimated delivery time in weeks
        """
        self._days = weeks * 7

    @property
    def months(self):
        """
        :return: Estimated delivery time in months
        """
        return self.get_time(TimeHelper.TimeUnit.MONTHS)

    @months.setter
    def months(self, months):
        """
        :param months: Estimated delivery time in months
        """
        self._days = months * 30

    def __str__(self):
        return 'TimeHelper[days=%s]' % self._days
