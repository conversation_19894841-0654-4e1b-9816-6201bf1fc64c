from typing import (
    Generator,
    List,
    TypeVar,
)

from django.db.models import QuerySet

T = TypeVar('T')


def batched(lst: QuerySet[T], size: int = 1000) -> Generator[List[T], None, None]:
    """
    TODO remove after updating python version to 3.12

    Yield successive chunks of `size` from `lst`.

    Example:
    batched([1, 2, 3, 4, 5, 6, 7], 2)
    ➜ [[1, 2], [3, 4], [5, 6], [7]]
    """
    for i in range(0, len(lst), size):
        yield lst[i : i + size]
