def map_keys_in_dict(data: dict, fields_mapping: dict) -> dict:
    """
    Maps the keys in the provided data dictionary to the keys specified in the
    fields_mapping dictionary, and returns the mapped dictionary.

    Args:
        data (dict): The dictionary containing the data to be mapped.
        fields_mapping (dict): The dictionary specifying the mapping between the
            keys in the data dictionary and the desired keys.
            {'name_of_the_column_in_csv': 'key_in_data_object'}
                Example:
                {'shipping(price)': 'shipping_price'}

    Returns:
        dict: The dictionary containing the mapped data.
    """
    keys_to_remove = []

    for key, value in fields_mapping.items():
        if value in data:
            data[key] = data[value]
            keys_to_remove.append(value)

    for key in keys_to_remove:
        if key not in fields_mapping.keys():
            del data[key]

    return data
