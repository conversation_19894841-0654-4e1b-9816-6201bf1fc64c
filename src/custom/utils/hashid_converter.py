from django.conf import settings

from hashids import Hashids

hashids = Hashids(settings.HASHIDS_SALT, min_length=settings.HASHIDS_MIN_LENGTH)


def h_encode(_id):
    if isinstance(_id, str):
        _id = int(_id)
    return hashids.encode(_id)


def h_decode(_hash):
    decoded = hashids.decode(_hash)
    if decoded:
        return decoded[0]
    return 0


class HashIdConverter:
    regex = f'[a-zA-Z0-9]{{{settings.HASHIDS_MIN_LENGTH},}}'

    def to_python(self, value):
        return h_decode(value)

    def to_url(self, value):
        return h_encode(value)
