from django.core.exceptions import ValidationError

import bleach

from rest_framework import serializers
from rest_framework.fields import Char<PERSON>ield

from events.domain_events.logistic_events import LOGISTIC_SMS_EVENTS_MAPPING
from events.utils import LogisticSMSEventTypes


class DRFJ<PERSON>NField(serializers.Field):
    def to_internal_value(self, value):
        if (
            value is not None
            and not isinstance(value, dict)
            and not isinstance(value, list)
        ):
            raise ValidationError('Invalid JSON <{}>'.format(value))

        return value

    def to_representation(self, obj):
        return obj


class SanitizedSerializedCharField(CharField):
    def to_internal_value(self, data):
        data = bleach.clean(data, tags=[], attributes={}, styles=[], strip=True)
        return super(SanitizedSerializedCharField, self).to_internal_value(data)


class NullableEnumField(serializers.Field):
    """
    Intended to be used with Enums that have a defined fallback ('null') value.

    It substitutes the fallback value for None upon serialization and does
    the reverse translation when receiving a null in the serialized data.
    """

    def __init__(self, enum_class, null_value=None, *args, **kwargs):
        self.enum_class = enum_class
        self.null_value = null_value or enum_class._missing_(None)
        super().__init__(*args, **kwargs)

    def to_representation(self, value):
        if value == self.null_value:
            return None
        return value

    def to_internal_value(self, data):
        if data is None:
            return self.null_value
        return self.enum_class(data).value

    def run_validation(self, data=serializers.empty):
        """Override base implementation that skips incoming null values."""
        value = self.to_internal_value(data)
        self.run_validators(value)
        return value


class LogisticSMSEventClassField(CharField):
    def to_representation(self, value):
        event_type = LogisticSMSEventTypes(value)
        event_class = LOGISTIC_SMS_EVENTS_MAPPING[event_type]
        return event_class
