from django.conf import settings
from django.core.mail.message import EmailMessage
from django.template.loader import get_template


def send_html_mail(template_name, subject, context, to, attachment=None):
    body = get_template(f'internal_emails/{template_name}').render(context)
    mail = EmailMessage(
        subject=subject,
        body=body,
        from_email=settings.DEFAULT_FROM_EMAIL,
        to=[to],
    )
    mail.content_subtype = 'html'
    if attachment:
        mail.attach_file(attachment)
    return mail.send()
