# Temporary solution, after migration form huey to celery should be removed
import logging
import time

from functools import wraps

logger = logging.getLogger('cstm')


def retry(retries: int, sleep: int, handled_exception: Exception = Exception):
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            attempt = 0
            while attempt < retries:
                try:
                    return func(*args, **kwargs)
                except handled_exception:
                    time.sleep(sleep)
                    attempt += 1
            return func(*args, **kwargs)

        return wrapper

    return decorator
