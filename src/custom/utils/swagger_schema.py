from drf_spectacular.openapi import AutoSchema


class CustomSchema(AutoSchema):
    def get_tags(self) -> list[str]:
        """Provide the ability to override the swagger's tags for a view.

        If not provided, tags will be inferred from the url path.
        To override the tags, add a class attribute to the view.

        Example:
            class MyView(APIView):
                swagger_tags = ['MyTag']
                ...

        Returns:
            list: A list of tags.
        """
        return getattr(self.view, 'swagger_tags', []) or super().get_tags()
