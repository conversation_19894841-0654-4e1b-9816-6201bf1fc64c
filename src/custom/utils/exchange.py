from datetime import date
from decimal import (
    ROUND_CEILING,
    Decimal,
)
from typing import Optional

from custom.models import ExchangeRate


class EuroRateNotFound(Exception):
    """Raise when euro rates not found in ExchangeRate"""


def convert_pln_to_euro(
    value: Decimal, exchange_date: Optional[date] = None, rates: Optional[dict] = None
) -> Decimal:
    if rates is None:
        exchange_date = exchange_date or date.today()
        rates = ExchangeRate.get_safe_exchange(
            exchange_date.year, exchange_date.month, exchange_date.day
        )

    if 'EUR' not in rates:
        raise EuroRateNotFound('Euro rates not found in ExchangeRate')
    return (value / Decimal(rates.get('EUR'))).quantize(
        Decimal('0.01'), rounding=ROUND_CEILING
    )
