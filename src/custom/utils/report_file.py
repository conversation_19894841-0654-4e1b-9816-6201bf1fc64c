import csv
import io
import mimetypes
import os
import zipfile

from django.conf import settings
from django.core.mail import EmailMessage
from django.http import HttpResponse

from openpyxl import Workbook


class WrongExtensionError(Exception):
    """Raise when ReportFile initiate with filename without extension"""


class ReportFile:
    def __init__(self, name: str, content: bytes):
        self.name = name
        self.content = content
        self.validate_name()

    def validate_name(self):
        if not self.extension:
            raise WrongExtensionError('File name without extension')
        if self.extension not in mimetypes.types_map:
            raise WrongExtensionError(f'Extension {self.extension} is not known')

    @property
    def extension(self):
        return self.get_extension(self.name)

    @classmethod
    def get_extension(cls, file_name: str):
        return os.path.splitext(file_name)[1]

    @property
    def content_type(self):
        return mimetypes.types_map[self.extension]

    @classmethod
    def load_list_as_csv_file(
        cls, data: list[list], file_name: str, headers: list[str] = None, delimiter=';'
    ):
        file_name = file_name if cls.get_extension(file_name) else f'{file_name}.csv'
        stream = io.StringIO()
        writer = csv.writer(stream, delimiter=delimiter)
        if headers:
            writer.writerow(headers)
        writer.writerows(data)
        output_bytes = stream.getvalue().encode()
        return ReportFile(name=file_name, content=output_bytes)

    @classmethod
    def load_workbook(cls, workbook: Workbook, file_name: str):
        file_name = file_name if cls.get_extension(file_name) else f'{file_name}.xlsx'
        worksheet_file = io.BytesIO()
        workbook.save(worksheet_file)
        return ReportFile(name=file_name, content=worksheet_file.getvalue())

    def send_as_email_attachment(
        self, emails: list[str], subject: str = 'Report file', body: str = ''
    ):
        if settings.IS_LOCAL:
            self._save_to_disk(self.name)
        message = EmailMessage(
            subject=subject, body=body, from_email='<EMAIL>', to=emails
        )
        message.attach(filename=self.name, content=self.content)
        message.send(fail_silently=False)

    def get_as_http_response(self):
        response = HttpResponse(self.content, content_type=self.content_type)
        response['Content-Disposition'] = f'attachment; filename={self.name}'
        return response

    @classmethod
    def merge_files_into_zip(cls, files: list['ReportFile'], zip_file_name: str):
        zip_file_name = f'{zip_file_name}.zip'
        stream = io.BytesIO()
        with zipfile.ZipFile(stream, 'w') as zip_file:
            for file in files:
                zip_file.writestr(file.name, file.content)
        return ReportFile(name=zip_file_name, content=stream.getvalue())

    @classmethod
    def send_files_as_email_attachment(
        cls,
        report_files: list['ReportFile'],
        emails: list[str],
        cc_emails: list[str],
        subject: str = 'Report file',
        body: str = '',
        from_email: str = '<EMAIL>',
    ):
        if settings.IS_LOCAL:
            for report_file in report_files:
                report_file._save_to_disk(report_file.name)
        message = EmailMessage(
            subject=subject,
            body=body,
            from_email=from_email,
            to=emails,
            cc=cc_emails,
        )
        for report_file in report_files:
            message.attach(filename=report_file.name, content=report_file.content)
        message.send(fail_silently=False)

    def _save_to_disk(self, path: str):
        """
        Use for tests only
        """
        with open(path, 'wb') as file:
            file.write(self.content)
