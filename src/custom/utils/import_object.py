import importlib


def import_object(object_path):
    """Import objects given by absolute `object_path`.

    This function is intended to replace deprecated
    `peak.util.imports.importString` function.
    """
    if object_path.startswith('.'):
        raise ValueError(
            '"{}" is not absolute object path'.format(object_path),
        )
    object_path = object_path.replace(':', '.')
    object_name = None
    module_path = object_path.rsplit('.', 1)
    if len(module_path) > 1:
        module_path, object_name = module_path
    else:
        module_path = module_path[0]
    module_object = importlib.import_module(module_path)
    if object_name:
        try:
            return getattr(module_object, object_name)
        except AttributeError:
            raise ImportError(
                'Cannot import "{}" from "{}"'.format(
                    object_name,
                    module_path,
                ),
            )
    return module_object
