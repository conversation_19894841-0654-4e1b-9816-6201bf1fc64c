import logging

from django.conf import settings
from django.core.cache import cache

import requests

from celery import shared_task

from gallery.services.importer import ImportObjectsService

logger = logging.getLogger('cstm')

BATCH_SIZE = 10


def get_random_furniture() -> dict:
    resp = requests.get(settings.FPM_API_URL + '/random-geom/')
    resp.raise_for_status()
    return resp.json()


def create_product_from_geometry(n: int, user_id: int) -> None:
    furniture = [get_random_furniture() for _ in range(n)]
    service = ImportObjectsService(furniture, user_id)
    order = service.create_order(owner=user_id, is_influencers=False)
    objects, _ = service.create_objects()
    for obj in objects:
        service.create_order_items(order, obj['gallery'])
    order.move_to_production_service.move()


@shared_task
def mass_generate_random_furniture(n: int, user_id: int) -> None:
    cache.set('random_furniture_target', n)
    progress = 0
    for _ in range(n // BATCH_SIZE):
        create_product_from_geometry(BATCH_SIZE, user_id)
        progress += BATCH_SIZE
        cache.set('random_furniture_progress', progress)
    if n % BATCH_SIZE:
        create_product_from_geometry(n % BATCH_SIZE, user_id)
    cache.delete('random_furniture_progress')
    cache.delete('random_furniture_target')
