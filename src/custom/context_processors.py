import hashlib
import hmac
import json

from django.conf import settings
from django.utils.encoding import force_bytes

from abtests.models import ABTest
from custom.enums import LanguageEnum
from django_user_agents.templatetags.user_agents import (
    is_ab_test,
    is_active_feature_flag,
)
from mailing.constants import (
    NEWSLETTER_VOUCHER_VALUES,
    VoucherValue,
)
from regions.cached_region import get_region_data_from_request
from regions.models import Region
from regions.serializers import RegionCheckoutSerializer


def get_user_hash(request):
    if request.user.id:
        return hmac.new(
            force_bytes(settings.USER_HMAC_HASH_SECRET),
            force_bytes(request.user.id),
            digestmod=hashlib.sha256,
        ).hexdigest()


def get_user_email_hash(request):
    if request.user.is_authenticated and request.user.email:
        return hmac.new(
            force_bytes(settings.USER_HMAC_HASH_SECRET),
            force_bytes(request.user.email.strip().lower()),
            digestmod=hashlib.sha256,
        ).hexdigest()


def get_user_email_hash_no_hmac(request):
    if request.user.is_authenticated and request.user.email:
        return hashlib.sha256(request.user.email.strip().lower().encode()).hexdigest()

    return ''


def _get_all_ab_tests_ids(ab_tests, request):
    ab_ids = []
    feature_flags_ids = []
    for test in ab_tests:
        if is_active_feature_flag(request, test.codename):
            feature_flags_ids.append(str(test.id))
        elif is_ab_test(request, test.codename):
            ab_ids.append(f'{test.id}:1')
        else:
            ab_ids.append(f'{test.id}:0')
    ab_ids = ','.join(ab_ids)
    feature_flags_ids = ','.join(feature_flags_ids)
    return ab_ids, feature_flags_ids


def settings_cp(request):
    if 'api' in request.META['PATH_INFO']:
        return {}

    if 'admin' in request.META['PATH_INFO']:
        return {
            'LANGUAGES': LanguageEnum.values,
            'IS_PRODUCTION': settings.IS_PRODUCTION,
            'DEBUG': settings.DEBUG,
        }
    user_hash = get_user_hash(request)
    user_email_hash = get_user_email_hash(request)
    try:
        region_data = get_region_data_from_request(request)
    except KeyError:
        # happen sometimes in social auth flow on callback
        currency_code = ''
        ab_tests = []
        voucher_value = None
        region_data = {}
    else:
        currency_code = region_data.currency_code
        ab_tests = ABTest.objects.get_active_tests_cached_list(region_data)
        voucher_value = NEWSLETTER_VOUCHER_VALUES.get(
            currency_code, VoucherValue()
        ).value

    ab_ids, feature_flags_ids = _get_all_ab_tests_ids(ab_tests, request)
    context = {
        'DEBUG': settings.DEBUG,
        'IS_PRODUCTION': settings.IS_PRODUCTION,
        'LANGUAGES': LanguageEnum.values,
        'SITE': request.get_host(),
        'SCHEME': request.scheme,
        'USER_ID_HASH': user_hash,
        'USER_EMAIL_HASH': user_email_hash,
        'TESTS': ab_tests,
        'AB_IDS': ab_ids,
        'FEATURE_FLAGS_IDS': feature_flags_ids,
    }
    context.update(
        {
            'user_region': region_data,
            'USER_REGION_CURRENCY': currency_code,
            'NEWSLETTER_VOUCHER_VALUE': voucher_value,
        }
    )

    if request.user.is_authenticated:
        context['USER_LIBRARY_ITEMS'] = request.user.profile.get_library_item_number()

    regions = RegionCheckoutSerializer(Region.objects.get_regions_cached(), many=True)
    context['regions'] = json.dumps([dict(region) for region in regions.data])
    return context


def data_to_merge(request):
    return request.session.get('data_to_merge', {})
