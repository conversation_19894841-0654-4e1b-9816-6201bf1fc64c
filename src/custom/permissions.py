from django.conf import settings

from rest_framework import permissions

from orders.enums import OrderStatus


class IsOwnerAndLogged(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return (
            request.user and request.user.is_authenticated and obj.owner == request.user
        )


class IsOwner(permissions.BasePermission):
    def has_object_permission(self, request, view, obj):
        return request.user and obj.owner == request.user


class IsOwnerExceptPost(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object to edit it.
    Assumes the model instance has an `owner` attribute.
    """

    def has_object_permission(self, request, view, obj):
        # Read permissions are allowed to any request,
        # so we'll always allow GET, HEAD or OPTIONS requests.
        if request.method in permissions.SAFE_METHODS:
            return True

        # Instance must have an attribute named `owner`.
        if request.method in ['POST', 'HEAD', 'OPTIONS']:
            return True
        return obj.owner == request.user


class IsAuthenticatedAndOwnerExceptPost(permissions.BasePermission):
    """
    Permission to check if user is authenticated and is not authenticated as guest user
    """

    def has_permission(self, request, view, obj=None):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        is_authenticated = request.user and request.user.is_authenticated
        is_owner = is_authenticated and (
            obj.owner == request.user if obj is not None else True
        )
        if request.method in permissions.SAFE_METHODS:
            return is_owner
        elif request.method == 'PATCH':
            return (
                is_owner
                and obj is not None
                and obj.status in {OrderStatus.CART, OrderStatus.DRAFT}
            )
        return is_authenticated


class IsAuthenticatedAndNotGuest(permissions.BasePermission):
    """
    Permission to check if user is authenticated and is not authenticated as guest user
    """

    def has_permission(self, request, view, obj=None):
        return (
            request.user
            and request.user.is_authenticated
            and request.user.profile
            and request.user.profile.user_type != 3
        )


class BrazePermission(permissions.IsAuthenticated):
    def has_permission(self, request, view):
        has_permission = super().has_permission(request, view)
        if not has_permission:
            return False

        app_id = request.META.get('HTTP_BRAZE_APP_ID')
        api_key = request.META.get('HTTP_BRAZE_API_KEY')
        return app_id == settings.BRAZE_APP_ID and api_key == settings.BRAZE_API_KEY


class IsLogisticUserPermission(permissions.BasePermission):
    def has_permission(self, request, view):
        return (
            request.user.is_authenticated
            and request.user.username == settings.LOGISTIC_USER_USERNAME
        )
