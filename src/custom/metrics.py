import logging
import time

from functools import wraps

from django.conf import settings

import datadog

DATADOG_STATSD_DEFAULT_PORT = 8125
logger = logging.getLogger('cstm')


def metrics_client():
    if getattr(settings, 'DATADOG_APP_NAME', ''):
        return DatadogMetricsClient
    return LoggingMetricsClient


class LoggingMetricsClient:
    @staticmethod
    def increment(metric_name, metric_value, **kwargs):
        tags = kwargs.get('tags', [])
        logger.info(
            '[increment] %s: %s tags: %s'
            % (
                metric_name,
                metric_value,
                ', '.join(tags),
            ),
        )

    @staticmethod
    def decrement(metric_name, metric_value, **kwargs):
        tags = kwargs.get('tags', [])
        logger.info(
            '[decrement] %s: %s tags: %s'
            % (
                metric_name,
                metric_value,
                ', '.join(tags),
            ),
        )

    @staticmethod
    def gauge(metric_name, metric_value, **kwargs):
        tags = kwargs.get('tags', [])
        logger.info(
            '[gauge] %s: %s tags: %s'
            % (
                metric_name,
                metric_value,
                ', '.join(tags),
            ),
        )

    @staticmethod
    def timing(metric_name, metric_value, **kwargs):
        tags = kwargs.get('tags', [])
        logger.info(
            '[timing] %s: %s tags: %s'
            % (
                metric_name,
                metric_value,
                ', '.join(tags),
            ),
        )

    @staticmethod
    def event(title, text, alert_type=None, tags=None):
        if tags is None:
            tags = []
        logger.info(
            '[event] %s: %s - %s tags: %s'
            % (
                title,
                text,
                alert_type,
                ', '.join(tags),
            ),
        )


class DatadogMetricsClient:
    @staticmethod
    def get_client():
        return datadog.statsd

    @staticmethod
    def increment(metric_name, value, **kwargs):
        client = DatadogMetricsClient.get_client()
        allowed_kwargs = (
            'tags',
            'sample_rate',
        )
        filtered_kwargs = {k: kwargs[k] for k in allowed_kwargs if k in kwargs}
        return client.increment(metric_name, value, **filtered_kwargs)

    @staticmethod
    def decrement(metric_name, value, **kwargs):
        client = DatadogMetricsClient.get_client()
        allowed_kwargs = (
            'tags',
            'sample_rate',
        )
        filtered_kwargs = {k: kwargs[k] for k in allowed_kwargs if k in kwargs}
        return client.decrement(metric_name, value, **filtered_kwargs)

    @staticmethod
    def gauge(metric_name, value, **kwargs):
        client = DatadogMetricsClient.get_client()
        allowed_kwargs = (
            'tags',
            'sample_rate',
        )
        filtered_kwargs = {k: kwargs[k] for k in allowed_kwargs if k in kwargs}
        return client.gauge(metric_name, value, **filtered_kwargs)

    @staticmethod
    def timing(metric_name, value, **kwargs):
        client = DatadogMetricsClient.get_client()
        allowed_kwargs = (
            'tags',
            'sample_rate',
        )
        filtered_kwargs = {k: kwargs[k] for k in allowed_kwargs if k in kwargs}
        return client.histogram(metric_name, value, **filtered_kwargs)

    @staticmethod
    def event(title, text, alert_type=None, tags=None):
        client = DatadogMetricsClient.get_client()
        return client.event(
            title=title,
            message=text,
            alert_type=alert_type,
            tags=tags,
        )


def task_metrics(fun):
    @wraps(fun)
    def _decorator(*args, **kwargs):
        tags = ['task_name:%s' % fun.__name__]
        start_time = time.time()
        try:
            result = fun(*args, **kwargs)
            tags.append('status:success')
            return result
        except Exception as e:
            tags.append('status:failure')
            logger.critical('Error while running task_metrics.', exc_info=True)
            metrics_client().event(
                'backend.task.failure',
                str(e),
                alert_type='error',
                tags=['task_name:%s' % fun.__name__],
            )
        finally:
            metrics_client().timing(
                'backend.task.run', time.time() - start_time, tags=tags
            )

    return _decorator
