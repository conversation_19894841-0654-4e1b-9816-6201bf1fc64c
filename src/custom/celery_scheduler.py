from celery.schedules import crontab

custom_tasks_scheduler = {
    'check_sendmail_lock': {
        'task': 'custom.tasks.check_sendmail_lock',
        'schedule': crontab(minute='*/15'),
    },
    'export_shelf_states_to_bigquery': {
        'task': 'custom.tasks.export_shelf_states_to_bigquery',
        'schedule': crontab(minute='*/30'),
    },
    'clear_session': {
        'task': 'custom.tasks.clear_sessions',
        'schedule': crontab(minute='0', hour='2', day_of_week='1'),
    },
}
