{% load i18n %}

<h3>{% blocktrans with filter_title=title %} By {{ filter_title }} {% endblocktrans %}</h3>
<ul>
  <li>
    {% with choices.0 as all_choice %}
    <form method="GET" action="">

        {% for k, v in all_choice.query_parts %}
        <input type="hidden" name="{{ k }}" value="{{ v }}" />
        {% endfor %}

        <input
            type="text"
            value="{{ spec.value|default_if_none:'' }}"
            name="{{ spec.parameter_name }}"/>

    </form>
    {% endwith %}
  </li>
</ul>