{% extends "admin/base_site.html" %}
{% load i18n l10n admin_urls static admin_modify admin_tags %}

{% block extrahead %}
    {{ block.super }}
    {{ media.js }}
    <link rel="stylesheet" type="text/css" href="{% static "admin/css/forms.css" %}" />
    <script type="text/javascript" src="{% url 'admin:jsi18n' %}"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/css/select2.min.css" rel="stylesheet" />
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.13/js/select2.min.js"></script>
    {{ form.media }}
    <script>
        $(document).ready(function() {
            $('.select2-multiple').select2({
                placeholder: 'Select categories',
                allowClear: true
            });
        });
    </script>
{% endblock %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
    </div>
{% endblock %}

{% block content %}

    <form action="" method="post" enctype="multipart/form-data">
        {% csrf_token %}
        {{ form.as_p }}

        {% if show_queryset %}
            <p>Objects to change:</p>
            <ul>
                {% for obj in queryset %}
                    <li>{{ obj }}</li>
                {% endfor %}
            </ul>
        {% endif %}
        {% for obj in queryset %}
            <input type="hidden" name="_selected_action" value="{{ obj.pk|unlocalize }}" />
        {% endfor %}
        <input type="hidden" name="action" value="{{ action }}" />
        <input type="submit" name="apply" value="Save" />
    </form>

{% endblock %}
