{% extends "admin/change_list.html" %}
{% load admin_urls %}
{% block extrastyle %}
    {{ block.super }}
    <style type="text/css">
        .exporter a.addlink {
            background-image: url(../img/tooltag-add.svg);
            display: block;
            float: left;
            padding: 3px 12px;
            background: #999;
            font-weight: 400;
            font-size: 11px;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            color: #fff;
            border-radius: 15px;
        }
        .exporter li {
            display: block;
            float: left;
            margin-left: 0px;
        }
    </style>
{% endblock %}
{% block result_list %}
    <div class="exporter">
        <ul>
            <li>
                {% url cl.opts|admin_urlname:'csvdownload' as download_url %}
                <a href="{{ download_url }}{% if querystring %}?{{ querystring }}{% endif %}" class="addlink">
                    Download CSV
                </a>
            </li>
            <li>
                {% url cl.opts|admin_urlname:'xlsxdownload' as download_url %}
                <a href="{{ download_url }}{% if querystring %}?{{ querystring }}{% endif %}" class="addlink">
                    Download XLSX
                </a>
            </li>
        </ul>
    </div>
    <br style="clear: both;">
    {{ block.super }}
{% endblock %}