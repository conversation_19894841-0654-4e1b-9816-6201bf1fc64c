import json

from django.conf import settings
from django.contrib.auth.models import User
from django.http import HttpResponseRedirect
from django.views.generic import TemplateView

from custom.internal_api.clients import (
    AssemblyServicePlanningAPIClient,
    ComplaintServicePlanningAPIClient,
    DeliveryTimeFrameProposalAPIClient,
)
from custom.internal_api.dto import ServiceDateProposalDTO
from producers.models import Product


class Email24View(TemplateView):
    template_name = 'front/logistic/email24.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)

        cstm_user = User.objects.get(username=settings.CSTM_USER_USERNAME)
        context['LOGISTIC_URL'] = settings.LOGISTIC_URL
        context['USER_TOKEN'] = cstm_user.auth_token.key
        return context


class ServiceAcceptDateProposalViewMixin:
    @staticmethod
    def get_date_proposal_context(
        date_proposal_dto: ServiceDateProposalDTO,
        date_proposal_id: int,
    ):
        service = date_proposal_dto.service
        return {
            'date_proposal_id': date_proposal_id,
            'service_id': service.id,
            'has_accepted_date': service.has_accepted_date,
            'date_expired_or_not_proposed': (
                date_proposal_dto.is_expired or not date_proposal_dto.is_proposed
            ),
            'show_request_new_dates_btn': (
                not service.has_accepted_date
                and (date_proposal_dto.is_expired or not date_proposal_dto.is_proposed)
            ),
            'date': date_proposal_dto.date,
            'from_hour': date_proposal_dto.from_hour,
            'to_hour': date_proposal_dto.to_hour,
        }

    def get(self, request, *args, **kwargs):
        confirm_param = request.GET.get('confirm', None)
        date_proposal_id = kwargs.get('date_proposal_id')

        if confirm_param is not None:
            confirm = confirm_param.lower() == 'true'
            if confirm:
                (
                    date_proposal,
                    date_accepted,
                ) = self.get_api_client().accept_date_proposal(date_proposal_id)
                context = self.get_context_data(**kwargs)
                context.update(
                    date_accepted=date_accepted,
                    **self.get_date_proposal_context(date_proposal, date_proposal_id),
                    confirmed=True,
                )
                return self.render_to_response(context)
            else:
                return HttpResponseRedirect('/')

        context = self.get_context_data(**kwargs)
        date_proposal = self.get_api_client().get_date_proposal(date_proposal_id)
        context.update(
            **self.get_date_proposal_context(date_proposal, date_proposal_id),
        )
        return self.render_to_response(context)


class AssemblyServiceAcceptDateProposalView(
    ServiceAcceptDateProposalViewMixin, TemplateView
):
    template_name = 'front/logistic/assembly_service_date_proposal_accepted.html'
    message = (
        'Assembly service date proposal accepted by client '
        '(assembly_service(id=%s), date_proposal(id=%s))'
    )

    def get_api_client(self):
        return AssemblyServicePlanningAPIClient()


class ComplaintServiceAcceptDateProposalView(
    ServiceAcceptDateProposalViewMixin, TemplateView
):
    template_name = 'front/logistic/complaint_service_date_proposal_accepted.html'
    message = (
        'Complaint service date proposal accepted by client '
        '(complaint_service(id=%s), date_proposal(id=%s))'
    )

    def get_api_client(self):
        return ComplaintServicePlanningAPIClient()


class ServiceNewDateRequestViewMixin:
    def get(self, request, *args, **kwargs):
        """Used for reject assembly proposal dates."""
        is_service_accepted = self.get_api_client().is_service_already_accepted(
            service_id=kwargs.get('service_id')
        )
        context = self.get_context_data(**kwargs)
        if is_service_accepted:
            self.template_name = self.already_accepted_template_name
            return self.render_to_response(context=context)
        is_service_rejected = self.get_api_client().is_service_already_rejected(
            service_id=kwargs.get('service_id')
        )
        if is_service_rejected:
            context['confirmation_page'] = True
        return self.render_to_response(context=context)

    def post(self, request, *args, **kwargs):
        """Used for reject assembly proposal dates."""
        self.get_api_client().request_new_date_on_service(
            service_id=kwargs.get('service_id'),
            customer_note=request.POST.get('customer_note'),
        )
        context = self.get_context_data(**kwargs)
        context['confirmation_page'] = True
        return self.render_to_response(context=context)


class AssemblyServiceNewDateRequestView(ServiceNewDateRequestViewMixin, TemplateView):
    template_name = (
        'front/logistic/assembly_service_date_proposal_new_date_request.html'
    )
    already_accepted_template_name = (
        'front/logistic/assembly_service_date_proposal_already_accepted.html'
    )

    def get_api_client(self):
        return AssemblyServicePlanningAPIClient()


class ComplaintServiceNewDateRequestView(ServiceNewDateRequestViewMixin, TemplateView):
    template_name = (
        'front/logistic/complaint_service_date_proposal_new_date_request.html'
    )
    already_accepted_template_name = (
        'front/logistic/complaint_service_date_proposal_already_accepted.html'
    )

    def get_api_client(self):
        return ComplaintServicePlanningAPIClient()


class DeliveryTimeFrameView(TemplateView):
    template_name = 'front/logistic/delivery-time-frames.html'

    def get_context_data(self, **kwargs):
        delivery_time_frame_proposal_api = DeliveryTimeFrameProposalAPIClient()
        dtf_id = kwargs['dtf_id']
        response = delivery_time_frame_proposal_api.get_order_details_by_dtf_id(
            delivery_time_frame_proposal_id=dtf_id,
            request_user_id=self.request.user.id,
        )
        cstm_user = User.objects.get(username=settings.CSTM_USER_USERNAME)

        order_details = json.loads(response['order_details'])
        for entry in order_details.values():
            for item in entry:
                product = Product.objects.get(id=item['product_number'])
                item['url'] = (
                    product.details.instruction.url
                    if product.details.instruction
                    else ''
                )

        return {
            'LOGISTIC_URL': settings.LOGISTIC_URL,
            'USER_TOKEN': cstm_user.auth_token.key,
            'dtf_id': dtf_id,
            'username': response['username'],
            'order_details': json.dumps(order_details),
            'last_date_for_change': json.dumps(response['last_date_for_change']),
            'accepted_timeslot_date': response['accepted_timeslot_date'],
            'accepted_timeslot_start_hour': response['accepted_timeslot_start_hour'],
            'accepted_timeslot_end_hour': response['accepted_timeslot_end_hour'],
            'accepted_timeslot_all_day': json.dumps(
                response['accepted_timeslot_all_day']
            ),
        }
