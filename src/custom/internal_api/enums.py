from django.db import models


class AssemblyTypeChoices(models.IntegerChoices):
    ASSEMBLY_PAID = 1
    ASSEMBLY_FREE = 2
    ASSEMBLY_INCLUDED = 3


class Shipper(models.TextChoices):
    TNT = 'TNT'
    UPS = 'UPS'
    FEDEX = 'FEDEX'
    DPD = 'DPD'


class ProposalType(models.TextChoices):
    RESEND_ACCEPTED_DTF = 'RESEND_ACCEPTED_DTF'
    RESEND_NOT_ACCEPTED_DTF = 'RESEND_NOT_ACCEPTED_DTF'
    NEW_DTF = 'NEW_DTF'
