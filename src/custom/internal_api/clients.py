from unittest.mock import MagicMock

from django.conf import settings
from django.contrib.auth.models import User

from apiclient import exceptions
from apiclient.authentication_methods import HeaderAuthentication
from apiclient.client import APIClient
from apiclient.decorates import endpoint
from apiclient.error_handlers import Base<PERSON><PERSON>r<PERSON>and<PERSON>
from apiclient.request_formatters import JsonRequestFormatter
from apiclient.request_strategies import BaseRequestStrategy
from apiclient.response import Response
from apiclient.response_handlers import <PERSON>sonR<PERSON>po<PERSON><PERSON><PERSON>ler

from custom.internal_api.deserializers import ServiceDateProposalDeserializer
from custom.utils.decorators import cache_function


@cache_function(cache_period=2 * 60)
def get_cstm_token():
    return User.objects.get(username=settings.CSTM_USER_USERNAME).auth_token.key


class ErrorHandlerWithValidationInfo(BaseErrorHandler):
    @staticmethod
    def get_exception(response: Response) -> exceptions.APIRequestError:
        status_code = response.get_status_code()
        exception_class = exceptions.UnexpectedError

        if 300 <= status_code < 400:
            exception_class = exceptions.RedirectionError
        elif 400 <= status_code < 403:

            return exception_class(
                message=(
                    f'{status_code} Error: {response.get_status_reason()} '
                    f'for url: {response.get_requested_url()} '
                    f'Validation info: {response.get_json()}'
                )
            )
        elif 403 <= status_code < 500:
            exception_class = exceptions.ClientError
        elif 500 <= status_code < 600:
            exception_class = exceptions.ServerError

        return exception_class(
            message=(
                f'{status_code} Error: {response.get_status_reason()} '
                f'for url: {response.get_requested_url()}'
            ),
            status_code=status_code,
            info=response.get_raw_data(),
        )


class JsonAPIClient(APIClient):
    request_strategy = (
        MagicMock(spec=BaseRequestStrategy)
        if settings.LOGISTIC_MOCK_REQUEST_STRATEGY
        else None
    )

    def __init__(self, **kwargs):
        authentication_method = HeaderAuthentication(
            token=get_cstm_token(), scheme='Token'
        )
        request_formatter = JsonRequestFormatter
        response_handler = JsonResponseHandler
        super().__init__(
            authentication_method=authentication_method,
            response_handler=response_handler,
            request_formatter=request_formatter,
            request_strategy=self.request_strategy,
            error_handler=ErrorHandlerWithValidationInfo,
        )

    def get_request_timeout(self) -> float:
        """Return the number of seconds before the request times out."""
        return 60.0


@endpoint(base_url=settings.LOGISTIC_URL)
class AssemblyServicePlanningAPIEndpoint(APIClient):
    request_new_date_on_service = (
        'internal-api/v1/assembly-service-planning/{pk}/request-new-date/'
    )
    is_service_accepted = (
        'internal-api/v1/assembly-service-planning/{pk}/is-service-accepted/'
    )
    is_service_rejected = (
        'internal-api/v1/assembly-service-planning/{pk}/is-service-rejected/'
    )
    accept_date_proposal = (
        'internal-api/v1/assembly-service-date-proposals/{pk}/accept-date/'
    )
    get_date_proposal = (
        'internal-api/v1/assembly-service-date-proposals/{pk}/get-date-proposal/'
    )


@endpoint(base_url=settings.LOGISTIC_URL)
class ComplaintServicePlanningAPIEndpoint(APIClient):
    request_new_date_on_service = (
        'internal-api/v1/complaint-service-planning/{pk}/request-new-date/'
    )
    is_service_accepted = (
        'internal-api/v1/complaint-service-planning/{pk}/is-service-accepted/'
    )
    is_service_rejected = (
        'internal-api/v1/complaint-service-planning/{pk}/is-service-rejected/'
    )
    accept_date_proposal = (
        'internal-api/v1/complaint-service-date-proposals/{pk}/accept-date/'
    )
    get_date_proposal = (
        'internal-api/v1/complaint-service-date-proposals/{pk}/get-date-proposal/'
    )


class AssemblyServicePlanningAPIClient(JsonAPIClient):
    def request_new_date_on_service(self, service_id: int, customer_note: str):
        response = self.post(
            AssemblyServicePlanningAPIEndpoint.request_new_date_on_service.format(
                pk=service_id
            ),
            data={'customer_note': customer_note},
        )
        is_service_accepted = response['service_accepted']
        return is_service_accepted

    def is_service_already_accepted(self, service_id: int):
        response = self.get(
            AssemblyServicePlanningAPIEndpoint.is_service_accepted.format(
                pk=service_id
            ),
        )
        is_service_accepted = response['service_accepted']
        return is_service_accepted

    def is_service_already_rejected(self, service_id: int):
        response = self.get(
            AssemblyServicePlanningAPIEndpoint.is_service_rejected.format(
                pk=service_id
            ),
        )
        is_service_rejected = response['service_rejected']
        return is_service_rejected

    def accept_date_proposal(self, date_proposal_id: int):
        response = self.post(
            AssemblyServicePlanningAPIEndpoint.accept_date_proposal.format(
                pk=date_proposal_id
            ),
            data={},
        )
        deserializer = ServiceDateProposalDeserializer(data=response['date_proposal'])
        deserializer.is_valid(raise_exception=True)
        return deserializer.validated_data, response['date_accepted']

    def get_date_proposal(self, date_proposal_id: int):
        response = self.get(
            AssemblyServicePlanningAPIEndpoint.get_date_proposal.format(
                pk=date_proposal_id
            ),
        )
        deserializer = ServiceDateProposalDeserializer(data=response['date_proposal'])
        deserializer.is_valid(raise_exception=True)
        return deserializer.validated_data


class ComplaintServicePlanningAPIClient(JsonAPIClient):
    def request_new_date_on_service(self, service_id: int, customer_note: str):
        response = self.post(
            ComplaintServicePlanningAPIEndpoint.request_new_date_on_service.format(
                pk=service_id
            ),
            data={'customer_note': customer_note},
        )
        is_service_accepted = response['service_accepted']
        return is_service_accepted

    def is_service_already_accepted(self, service_id: int):
        response = self.get(
            ComplaintServicePlanningAPIEndpoint.is_service_accepted.format(
                pk=service_id
            ),
        )
        is_service_accepted = response['service_accepted']
        return is_service_accepted

    def is_service_already_rejected(self, service_id: int):
        response = self.get(
            ComplaintServicePlanningAPIEndpoint.is_service_rejected.format(
                pk=service_id
            ),
        )
        is_service_rejected = response['service_rejected']
        return is_service_rejected

    def accept_date_proposal(self, date_proposal_id: int):
        response = self.post(
            ComplaintServicePlanningAPIEndpoint.accept_date_proposal.format(
                pk=date_proposal_id
            ),
            data={},
        )

        deserializer = ServiceDateProposalDeserializer(data=response['date_proposal'])
        deserializer.is_valid(raise_exception=True)
        return deserializer.validated_data, response['date_accepted']

    def get_date_proposal(self, date_proposal_id: int):
        response = self.get(
            ComplaintServicePlanningAPIEndpoint.get_date_proposal.format(
                pk=date_proposal_id
            ),
        )
        deserializer = ServiceDateProposalDeserializer(data=response['date_proposal'])
        deserializer.is_valid(raise_exception=True)
        return deserializer.validated_data


@endpoint(base_url=settings.LOGISTIC_URL)
class DeliveryTimeFrameProposalAPIEndpoint(APIClient):
    get_order_details = 'internal-api/v1/delivery_time_frame/{pk}/get-order-details'


class DeliveryTimeFrameProposalAPIClient(JsonAPIClient):
    def get_order_details_by_dtf_id(
        self, delivery_time_frame_proposal_id: int, request_user_id: int
    ):
        response = self.get(
            DeliveryTimeFrameProposalAPIEndpoint.get_order_details.format(
                pk=delivery_time_frame_proposal_id
            ),
            params={'request_user_id': request_user_id},
        )
        return response
