from rest_framework_dataclasses.serializers import DataclassSerializer

from custom.internal_api.dto import (
    LogisticOrderDTO,
    LogisticOrderLeadTimeDTO,
    ServiceDateProposalDTO,
)


class ServiceDateProposalDeserializer(DataclassSerializer):
    class Meta:
        dataclass = ServiceDateProposalDTO


class LogisticOrderDeserializer(DataclassSerializer):
    class Meta:
        dataclass = LogisticOrderDTO
        extra_kwargs = {
            'order_type': {'allow_blank': True},
            'tracking_number': {'allow_blank': True},
            'tracking_link': {'allow_blank': True},
            'carrier': {'allow_blank': True},
            'additional_info': {'allow_blank': True},
            'invoice_number': {'allow_blank': True},
            'get_packaging': {'allow_blank': True},
            'cmr_documents': {
                'child_kwargs': {
                    'extra_kwargs': {
                        'get_contractor_phone_number': {'allow_blank': True},
                    }
                }
            },
            'to_be_shipped_email24': {
                'child_kwargs': {
                    'extra_kwargs': {
                        'note_english': {'allow_blank': True},
                        'note': {'allow_blank': True},
                        'email': {'allow_blank': True},
                        'url': {'allow_blank': True},
                    }
                }
            },
        }


class LogisticOrderLeadTimeDTODeserializer(DataclassSerializer):
    class Meta:
        dataclass = LogisticOrderLeadTimeDTO
        extra_kwargs = {
            'order_type': {'allow_blank': True},
            'carrier': {'allow_blank': True},
        }
