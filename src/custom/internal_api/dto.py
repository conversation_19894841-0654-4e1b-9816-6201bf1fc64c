import datetime

from dataclasses import field
from decimal import Decimal
from typing import (
    List,
    Optional,
)

from django.conf import settings

from pydantic.dataclasses import dataclass

from custom.logistic_enums import (
    STATUSES_SEND,
    AsMaxStatusEnum,
    DeliveryTimeFrameProposalStatus,
    Email24StatusChoices,
    ServiceDateProposalStatus,
    ServiceStatus,
    TransportMethod,
)


@dataclass
class ServiceDTO:
    id: int
    has_accepted_date: bool


@dataclass
class ShipperDTO:
    name: str


@dataclass
class ServiceDateProposalDTO:
    service: ServiceDTO
    is_expired: bool
    is_proposed: bool
    date: datetime.date
    from_hour: datetime.time
    to_hour: datetime.time


@dataclass
class AssemblyServiceDateProposalDTO:
    id: int
    date: datetime.date
    from_hour: datetime.time
    to_hour: datetime.time
    status: ServiceDateProposalStatus

    def get_status_display(self):
        return ServiceDateProposalStatus.choices()[self.status]


@dataclass
class AssemblyServiceDTO:
    id: int
    logistic_order: int
    status: ServiceStatus
    transport_method: TransportMethod
    service_price: Decimal
    shipping_price: Decimal
    internal_transport_price: Optional[Decimal] = Decimal('0.0')
    dt_potential_transport_price: Optional[Decimal] = Decimal('0.0')
    accepted_date: Optional[AssemblyServiceDateProposalDTO] = None
    date_proposals: List[AssemblyServiceDateProposalDTO] = field(default_factory=list)

    def get_transport_method_display(self):
        return TransportMethod.choices()[self.transport_method]

    def get_status_display(self):
        return ServiceStatus.choices()[self.status]

    def cost(self):
        return (
            self.service_price
            + self.shipping_price
            + self.internal_transport_price
            - self.dt_potential_transport_price
        )


@dataclass
class AssemblyServiceEstimatedCostDTO:
    id: int
    service_price: Decimal
    shipping_price: Decimal
    internal_transport_price: Optional[Decimal] = Decimal('0.0')
    dt_potential_transport_price: Optional[Decimal] = Decimal('0.0')

    def cost(self):
        return (
            self.service_price
            + self.shipping_price
            + self.internal_transport_price
            - self.dt_potential_transport_price
        )


@dataclass
class DedicatedTransportDTO:
    id: int
    is_active: bool


@dataclass
class CmrDocumentDTO:
    id: int
    get_contractor_phone_number: Optional[str] = ''


@dataclass
class DeliveryTimeFrameSelectedSlotDTO:
    all_day: bool
    date: Optional[datetime.date] = None
    start_hour: Optional[datetime.time] = None
    end_hour: Optional[datetime.time] = None


@dataclass
class DeliveryTimeFrameAvailableDateDTO:
    date: Optional[datetime.date] = None


@dataclass
class DeliveryTimeFrameProposalDTO:
    id: int
    status: DeliveryTimeFrameProposalStatus
    is_confirmed: bool
    proposal_dates: Optional[List[DeliveryTimeFrameAvailableDateDTO]] = field(
        default_factory=list
    )
    selected_dates: Optional[List[DeliveryTimeFrameSelectedSlotDTO]] = field(
        default_factory=list
    )
    accepted_date: Optional[DeliveryTimeFrameSelectedSlotDTO] = field(
        default_factory=list
    )


@dataclass
class ToBeShippedEmail24DTO:
    id: int
    status: Email24StatusChoices
    created_at: datetime.datetime
    has_elevator: Optional[bool] = None
    date_confirmed: Optional[datetime.datetime] = None
    floor: Optional[int] = None
    note_english: Optional[str] = ''
    note: Optional[str] = ''
    email: Optional[str] = ''
    url: Optional[str] = ''

    def get_admin_url(self):
        return f'{settings.LOGISTIC_URL}/admin/logistic/tobeshippedemail24/{self.id}'

    def get_status_display(self):
        return Email24StatusChoices.choices()[self.status]

    def get_data_for_customer_service_panel(self):
        data = {
            'id': self.id,
            'status': self.get_status_display().lower(),
            'admin_url': self.get_admin_url(),
            'date_sent': self.created_at,
            'email': self.email or '-',
        }
        url_information = None
        if self.status in STATUSES_SEND:
            url_information = self.url
        elif self.status == Email24StatusChoices.CONFIRMED:
            url_information = 'Client has confirmed data'
            data.update({'details': self.get_confirmed_details()})
        elif self.status == Email24StatusChoices.EXPIRED:
            url_information = (
                "Client hasn't confirmed email within 24 hours and URL expired"
            )
        data.update(
            {
                'url': url_information,
            }
        )
        return data

    def get_confirmed_details(self):
        details = {
            'has_elevator': self.has_elevator,
            'floor': self.floor,
            'date_confirmed': self.date_confirmed,
        }
        note = self.note_english or self.note
        if note:
            details.update({'note': note})
        return details


@dataclass
class UndamagedDeliveryPredictionDTO:
    DT: int
    DPD: int
    TNT: int
    UPS: int


@dataclass
class AsMaxStatusHistoryDTO:
    new_status: int
    previous_status: int
    changed_at: datetime.datetime
    planned_start_date: Optional[datetime.date] = None

    @property
    def new_status_display(self):
        return AsMaxStatusEnum(self.new_status).display_value

    @property
    def previous_status_display(self):
        return AsMaxStatusEnum(self.previous_status).display_value


@dataclass
class LogisticOrderMockDTO:
    id: Optional[int] = None


@dataclass
class LogisticOrderDTO:
    id: int
    order_type: str
    total_brutto_weight: float
    total_netto_weight: float
    to_be_shipped: bool
    mailing_disabled: bool
    cost_in_pln: float
    is_split_candidate: bool | None = None
    as_max_status: int | None = None
    as_max_status_history: list[AsMaxStatusHistoryDTO] | None = field(
        default_factory=list
    )
    additional_info: str | None = ''
    invoice_number: str | None = ''
    mailing_reenable_at: datetime.date | None = None
    shipper: ShipperDTO | None = None
    tracking_number: str | None = None
    internal_tracking_number: str | None = None
    tracking_link: str | None = ''
    get_packages: list | None = field(default_factory=list)
    product_ids: list | None = field(default_factory=list)
    get_packaging: str | None = ''
    delivered_date: datetime.date | None = None
    carrier: str | None = None
    sent_to_customer: datetime.date | None = None
    assembly_type: int | None = None

    to_be_shipped_email24: list[ToBeShippedEmail24DTO] | None = field(
        default_factory=list
    )
    dtf_proposals: list[DeliveryTimeFrameProposalDTO] | None = field(
        default_factory=list
    )
    assembly_service: AssemblyServiceDTO | None = None
    assembly_service_estimated_cost: AssemblyServiceEstimatedCostDTO | None = None
    dedicated_transport: DedicatedTransportDTO | None = None
    cmr_documents: list[CmrDocumentDTO] | None = field(default_factory=list)
    undamaged_delivery_prediction: UndamagedDeliveryPredictionDTO | None = None
    domestic_injection_mail_sent_at: datetime.date | None = None
    domestic_injection_days: int = 0

    def total_brutto_weight_string(self):
        return f'{self.total_brutto_weight:.2f} kg'

    @property
    def is_assembly_service_in_planning(self):
        if self.assembly_service:
            return ServiceStatus.is_planning(self.assembly_service.status)
        return False


@dataclass
class LogisticOrderLeadTimeDTO:
    id: int
    order_type: str
    delivered_date: Optional[datetime.date] = None
    sent_to_customer: Optional[datetime.date] = None
    lead_time: Optional[int] = None
    carrier: Optional[str] = None
