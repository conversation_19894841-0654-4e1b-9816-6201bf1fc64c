from typing import Union

from django.conf import settings
from django.template.defaultfilters import register

from custom.enums import LanguageEnum
from gallery.models.models import CloudinaryFileFieldPlaceholder
from regions.models import Country


@register.simple_tag
def country_trans(country: Union[Country, str], lang: str = 'en') -> str:
    if not isinstance(country, Country):
        try:
            country = Country.objects.get(name=country)
        except Country.DoesNotExist:
            return country

    lang = LanguageEnum(lang)
    return country.get_translated_name(language_code=lang)


@register.simple_tag
def get_media_full_url(media_url, site_url):
    if settings.USE_AWS_S3_MEDIA_STORAGE:
        return media_url
    else:
        return f'https://{site_url}{media_url}'


@register.filter()
def file_url(file_field, default=''):
    if isinstance(file_field, str):
        return file_field
    if isinstance(file_field, CloudinaryFileFieldPlaceholder):
        return file_field.url
    if file_field and file_field.storage.exists(file_field.name):
        return file_field.url

    return default
