from django import template

from custom import enums

register = template.Library()


@register.filter
def display_shelf_type(shelf_type):
    try:
        return enums.ShelfType(shelf_type).name
    except ValueError:
        return 'Uknown shelf type - {}'.format(shelf_type)


@register.filter
def replace_shelf_type_color(value, shelf_type):
    try:
        shelf_type = enums.ShelfType(shelf_type)
    except ValueError:
        return value
    for color in shelf_type.colors:
        if color.slug in value:
            value = value.replace(color.slug, color.display_name)
            return value.replace('_', ' ')
    return value
