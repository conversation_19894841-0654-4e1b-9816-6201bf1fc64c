import enum

from django.utils.translation import gettext_lazy as _


def default_choices_for(enum_class):
    return {member.value: member.name.replace('_', ' ') for member in enum_class}


class ServiceStatus(enum.IntEnum):
    TO_BE_RELEASED = 1
    IN_PROGRESS = 2
    READY_TO_PROPOSE = 3
    PROPOSED_TO_CLIENT = 4
    ACCEPTED_BY_CLIENT = 5
    REJECTED_BY_CLIENT = 6
    COMPLETED = 7
    ABANDONED = 8

    @classmethod
    def choices(cls):
        return {value: text.title() for value, text in default_choices_for(cls).items()}

    @classmethod
    def is_planning(cls, status):
        try:
            status = cls(status)
        except ValueError:
            return False
        return status in (
            cls.TO_BE_RELEASED,
            cls.IN_PROGRESS,
            cls.REJECTED_BY_CLIENT,
        )


class ServiceDateProposalStatus(enum.IntEnum):
    NEW = 1
    PROPOSED = 2
    ACCEPTED = 3
    REJECTED = 4
    ABANDONED = 5

    @classmethod
    def choices(cls):
        return default_choices_for(cls)


class TransportMethod(enum.IntEnum):
    UNKNOWN = 1
    ASSEMBLY_TEAM = 2
    ALREADY_AT_CUSTOMER = 3
    TNT = 4
    UPS = 5
    DT = 6

    @classmethod
    def choices(cls):
        return default_choices_for(cls)


class Shipper(enum.Enum):
    TNT = 'TNT'
    UPS = 'UPS'
    FEDEX = 'FEDEX'
    DPD = 'DPD'


class DeliveryTimeFrameProposalStatus(enum.IntEnum):
    NEW = 1
    DATES_CHOSEN = 2
    NEW_DATES_REQUESTED = 3
    ACCEPTED_BY_LOGISTIC = 4
    CANCELLED = 5
    FINISHED = 6
    PREPARED_BY_LOGISTICS = 35


class Email24StatusChoices(enum.IntEnum):
    CREATED = 10
    SENT = 20
    OPENED = 30
    CONFIRMED = 40
    EXPIRED = 50

    @classmethod
    def choices(cls):
        return {member.value: member.name.replace('_', ' ') for member in cls}


STATUSES_SEND = [
    Email24StatusChoices.CREATED,
    Email24StatusChoices.SENT,
    Email24StatusChoices.OPENED,
]


class AsMaxStatusEnum(enum.IntEnum):
    EMPTY = 0
    NEW = 10
    WAITING_FOR_CLIENT_ACCEPTATION = 20
    ACCEPTED = 30
    DONE = 40

    @property
    def display_value(self):
        values = {
            self.EMPTY: '-',
            self.NEW: _('new'),
            self.WAITING_FOR_CLIENT_ACCEPTATION: _('waiting_for_client_acceptation'),
            self.ACCEPTED: _('accepted'),
            self.DONE: _('done'),
        }
        return values[self]
