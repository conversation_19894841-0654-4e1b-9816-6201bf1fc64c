import logging

from datetime import (
    datetime,
    timedelta,
)
from typing import Final
from urllib.parse import (
    parse_qs,
    urlencode,
    urlparse,
)

from django import http
from django.conf import settings
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from django.db import IntegrityError
from django.http.response import (
    HttpResponseForbidden,
    JsonResponse,
)
from django.shortcuts import render
from django.urls import (
    NoReverseMatch,
    resolve,
    reverse,
)
from django.utils.http import url_has_allowed_host_and_scheme
from django.utils.translation import (
    activate,
    check_for_language,
)
from django.views import defaults

import requests

from rest_framework import (
    permissions,
    status,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import (
    APIException,
    Throttled,
)
from rest_framework.permissions import (
    AllowAny,
    BasePermission,
)
from rest_framework.request import Request
from rest_framework.status import HTTP_404_NOT_FOUND
from rest_framework.views import (
    APIView,
    Response,
    exception_handler,
)

from abtests.models import ABTest
from custom.enums import LanguageEnum
from custom.metrics import metrics_client
from custom.serializers import GoogleRecaptchaSerializer
from custom.utils.cloudflare_cache import purge_cloudflare_cache
from custom.utils.python2_specific import HeavyThrottle
from events.domain_events.marketing_events import LanguageUpdateEvent
from mailing.models import RetargetingBlacklist

logger = logging.getLogger('cstm')
ABTEST_ENABLED: Final[str] = 'ok'


def _trim_title_param_from_url(url):
    """
    'title' param is used on PDP for SEO purposes, and on language change
    it is better to trim this param as it is not translated.
    """
    parsed_url = urlparse(url)
    query_dict = parse_qs(parsed_url.query)
    query_dict.pop('s_title', '')
    url = parsed_url._replace(query=urlencode(query_dict, doseq=True)).geturl()
    return url


def set_language(request):
    """
    Own version, to accept get paremeters instead of post.
    """
    lang_code = request.GET.get('language', None)

    if lang_code and check_for_language(lang_code):
        if request.user.is_authenticated:
            request.user.profile.language = lang_code
            request.user.profile.save()
            request.LANGUAGE_CODE = lang_code
            LanguageUpdateEvent(user=request.user, language=lang_code)

    next_url = request.GET.get('next', '/')
    next_url = _trim_title_param_from_url(next_url)

    if not url_has_allowed_host_and_scheme(
        url=next_url,
        allowed_hosts={request.get_host()},
    ):
        next_url = request.META.get('HTTP_REFERER')
        if not url_has_allowed_host_and_scheme(
            url=next_url,
            allowed_hosts={request.get_host()},
        ):
            next_url = '/'
    url_beginning = next_url[:4]
    lang_prefixes = (lang.url_prefix for lang in LanguageEnum)
    if url_beginning in lang_prefixes:
        next_url = next_url[3:]
    next_url_components = urlparse(next_url)
    found_url = resolve(next_url_components.path)
    activate(lang_code)

    # lets add query string here:
    if found_url.url_name is None:
        response = http.HttpResponseRedirect(
            '/' if lang_code == LanguageEnum.EN else next_url_components.geturl()
        )
    else:
        try:
            path = reverse(
                found_url.url_name,
                args=found_url.args,
                kwargs=found_url.kwargs,
            )
        except NoReverseMatch:
            path = '/' if lang_code == LanguageEnum.EN else next_url_components.geturl()
        next_url_components = next_url_components._replace(path=path)
        response = http.HttpResponseRedirect(next_url_components.geturl())
    if not hasattr(request, 'session'):
        response.set_cookie(
            settings.LANGUAGE_COOKIE_NAME,
            lang_code,
            max_age=settings.LANGUAGE_COOKIE_AGE,
            path=settings.LANGUAGE_COOKIE_PATH,
            domain=settings.LANGUAGE_COOKIE_DOMAIN,
        )
    return response


def switch_ab_test(request):
    set_cookie = False
    response = http.HttpResponseRedirect(request.GET.get('next', '/'))
    test_to_change = request.GET.get('test', None)
    value_to_change = request.GET.get('value', None)
    actual_test = ABTest.objects.filter(codename=test_to_change).first()
    if not test_to_change or not actual_test:
        return response
    test_group = value_to_change
    if not actual_test.feature_flag:
        if not test_group and request.COOKIES.get(actual_test.codename, None) == 'ok':
            test_group = 'nok'
        elif not test_group:
            test_group = 'ok'
        set_cookie = True
    if actual_test.feature_flag and actual_test.rate_split:
        if not test_group:
            test_group = 'ok'
        set_cookie = True
    if set_cookie:
        response.set_cookie(
            actual_test.codename,
            test_group,
            domain=settings.SESSION_COOKIE_DOMAIN,
            secure=settings.SESSION_COOKIE_SECURE or None,
            expires=datetime.today() + timedelta(days=120),
        )
    return response


def is_ab_test_enabled(request: Request, codename: str) -> bool:
    value = request.COOKIES.get(codename, 'nok')
    return value == ABTEST_ENABLED


def csrf_failure(request, reason=''):
    metrics_client().increment('backend.403csrf', 1)
    tr = render(request, '403.html', context={})
    return HttpResponseForbidden(tr, content_type='text/html')


def rest_exception_handler(exc, context):
    if isinstance(exc, Throttled):
        # metrics_client().increment('backend.405', 1)
        metrics_client().increment(
            'backend.catchall',
            1,
            tags=['what:405', 'where:%s' % context['view'].__class__.__name__],
        )
    response = exception_handler(exc, context)

    if response and isinstance(exc, APIException) and isinstance(response.data, dict):
        response.data['error_codes'] = exc.get_codes()

    return response


class HealthCheckPermission(BasePermission):
    def has_permission(self, request, view):
        return 't' in request.GET and request.GET['t'] == 'klikam_hc_2017'


class MailchimpUnsubscribeHandle(APIView):
    permission_classes = (permissions.AllowAny,)
    throttle_classes = [
        HeavyThrottle,
    ]

    def post(self, request):
        secret = request.query_params.get('secret', None)
        if not secret and secret != 'db9d0b233b75b':
            return Response(status=status.HTTP_400_BAD_REQUEST)
        action = request.data.get('data[action]', None)
        email = request.data.get('data[email]', None)
        try:
            validate_email(email)
        except ValidationError:
            return Response(status=status.HTTP_400_BAD_REQUEST)
        if action == 'unsub' and email:
            try:
                RetargetingBlacklist(email=email).save(from_mailchimp=True)
            except IntegrityError:
                pass
            return Response(status=status.HTTP_200_OK)
        else:
            return Response(status=status.HTTP_400_BAD_REQUEST)

    def get(self, request):
        return Response(status=status.HTTP_200_OK)


class GoogleRecaptcha(APIView):
    serializer_class = GoogleRecaptchaSerializer
    url = 'https://www.google.com/recaptcha/api/siteverify'
    permission_classes = [AllowAny]

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        serializer.is_valid(raise_exception=True)
        token = serializer.validated_data['token']
        response = self._verify_token(token)

        return Response(response)

    def _verify_token(self, token):
        data = {'secret': settings.GOOGLE_RECAPTCHA_SECRET, 'response': token}
        response = requests.post(self.url, data=data)

        return response.json()


def page_not_found_handler(request, exception):
    if request.path.startswith('/api/'):
        data = {
            'detail': 'Page not found.',
        }
        return JsonResponse(data, status=HTTP_404_NOT_FOUND)

    else:
        return defaults.page_not_found(request, exception)


class ForceCloudflarePurgeView(APIView):
    authentication_classes = (TokenAuthentication,)

    def post(self, request):
        purge_cloudflare_cache()
        return Response(status=status.HTTP_200_OK)
