from django.contrib import admin


def renamed_filter(title):
    class Filter(admin.FieldListFilter):
        def __new__(cls, *args, **kwargs):
            instance = admin.FieldListFilter.create(*args, **kwargs)
            instance.title = title
            return instance

    return Filter


class InputFilter(admin.SimpleListFilter):
    template = 'admin/input_filter.html'

    def lookups(self, request, model_admin):
        # Dummy, required to show the filter.
        return ((),)

    def choices(self, changelist):
        # Grab only the "all" option.
        all_choice = next(super().choices(changelist))
        all_choice['query_parts'] = (
            (k, v)
            for k, v in changelist.get_filters_params().items()
            if k != self.parameter_name
        )
        yield all_choice


class InputCommaSeparatedFilter(InputFilter):
    lookup_field = None

    def queryset(self, request, queryset):
        if self.value():
            query_dict = {
                self.lookup_field: [int(_id.strip()) for _id in self.value().split(',')]
            }
            return queryset.filter(**query_dict)
        return queryset
