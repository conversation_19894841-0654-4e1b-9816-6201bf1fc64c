import json
import logging

from django.utils import timezone

from django_redis import get_redis_connection

from custom.serializers import ShelfStateSerializer
from custom.utils.decorators import production_only
from gallery.models import Jetty
from gallery.serializers import JettyBigQuerySerializer

logger = logging.getLogger('cstm')


class RedisInteractor:
    queue_name = ''

    def __init__(self):
        self.conn = get_redis_connection()

    def lrange_all(self):
        return self.conn.lrange(self.queue_name, 0, -1)

    def add_to_queue(self, elem):
        pickled_elem = json.dumps(elem)
        self.conn.rpush(self.queue_name, pickled_elem)

    def get_all_states_on_queue(self):
        serialized_states = [json.loads(elem) for elem in self.lrange_all()]
        self.conn.delete(self.queue_name)
        return serialized_states


class ShelfStatesInteractor(RedisInteractor):
    queue_name = 'shelf_states'

    def add_to_queue_from_data(self, data):
        serializer = ShelfStateSerializer(data=data)

        if serializer.is_valid():
            self.add_to_queue(serializer.validated_data)
        else:
            logger.error(
                "%s: Couldn't serialize data to save in bigquery queue. Reason: %s",
                ShelfStatesInteractor.__name__,
                serializer.errors,
            )

    def get_all_states_on_queue(self):
        jetty_states_to_be_send = []
        for state in self.lrange_all():
            state_json = json.loads(state)
            try:
                jetty = Jetty.objects.get(pk=state_json['shelf_id'])
            except Jetty.DoesNotExist:
                continue
            instance_data = JettyBigQuerySerializer(instance=jetty).data
            state_json.update(instance_data)
            jetty_states_to_be_send.append(state_json)

        self.conn.delete(self.queue_name)
        return jetty_states_to_be_send


class FailedShelfStatesInteractor(ShelfStatesInteractor):
    queue_name = 'failed_shelf_states'


@production_only
def add_jetty_state_to_redis(user_id, jetty, source, pagepath=''):
    if not isinstance(jetty, Jetty) or user_id is None:
        return

    interactor = ShelfStatesInteractor()
    interactor.add_to_queue_from_data(
        data={
            'shelf_id': jetty.id,
            'source': source,
            'log_created_at': str(timezone.now()),
            'pagepath': pagepath,
            'user_id': user_id,
        }
    )
