import inspect

from django.db import models


class CacheInvalidationMixin(models.Model):
    """Cleans cached methods at save()"""

    class Meta:
        abstract = True

    @classmethod
    def clear_related_cache(cls):
        for name, member in inspect.getmembers(cls):
            if isinstance(getattr(cls, name), (classmethod, staticmethod)):
                func = getattr(cls, name).__func__
            else:
                func = member

            if callable(func) and hasattr(func, 'cache_clear'):
                func.cache_clear()

    def save(self, *args, **kwargs):
        super().save(*args, **kwargs)
        self.clear_related_cache()
