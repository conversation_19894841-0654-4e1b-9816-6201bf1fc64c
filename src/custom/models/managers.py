from django.db import models
from django.db.models import Q
from django.utils import timezone


class DateRangeQuerySet(models.QuerySet):
    def valid_for_today(self):
        today = timezone.now().date()
        return self.valid_for_date(today)

    def valid_for_date(self, date):
        return self.filter(
            Q(date_to__gte=date) | Q(date_to__isnull=True),
            date_from__lte=date,
        )


class SingletonQueryset(models.QuerySet):
    def first(self):
        obj = super().first()
        if not obj:
            obj = self.create()
        return obj


class SingletonManager(models.Manager):
    def get_queryset(self):
        return SingletonQueryset(self.model)
