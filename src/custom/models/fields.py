import os

from django import forms
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.db import models
from django.utils.crypto import get_random_string


class ChoiceArrayField(ArrayField):
    """
    A field that allows us to store an array of choices.
    Uses Django's Postgres ArrayField
    and a MultipleChoiceField for its formfield.
    """

    def formfield(self, **kwargs):
        defaults = {
            'form_class': forms.TypedMultipleChoiceField,
            'choices': self.base_field.choices,
            'coerce': self.base_field.to_python,
        }
        defaults.update(kwargs)
        # Skip parent's ``ArrayField`` formfield implementation
        return super(ArrayField, self).formfield(**defaults)


class FileFieldWithHash(models.FileField):
    def generate_filename(self, instance, filename):
        filename = super().generate_filename(instance, filename)
        return self.add_random_string_to_name(filename)

    @staticmethod
    def add_random_string_to_name(name):
        dir_name, file_name = os.path.split(name)
        file_root, file_ext = os.path.splitext(file_name)
        return os.path.join(
            dir_name,
            f'{file_root}_{get_random_string(7)}{file_ext}',
        )
