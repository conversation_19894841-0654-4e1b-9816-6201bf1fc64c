from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _


class DateRangeValidator:
    def __init__(self, start_date_field='start_date', end_date_field='end_date'):
        self.start_date_field = start_date_field
        self.end_date_field = end_date_field

    def __call__(self, instance):
        start_date = getattr(instance, self.start_date_field, None)
        end_date = getattr(instance, self.end_date_field, None)

        if start_date and start_date.tzinfo is None:
            start_date = timezone.make_aware(start_date)
        if end_date and end_date.tzinfo is None:
            end_date = timezone.make_aware(end_date)

        if start_date and end_date and start_date > end_date:
            raise ValidationError(
                {
                    self.start_date_field: _('Start date cannot be after end date.'),
                    self.end_date_field: _('End date cannot be before start date.'),
                }
            )
