from datetime import date
from decimal import Decimal

from django.utils.functional import classproperty
from django.utils.translation import gettext_lazy as _

from custom.constants import (
    VAT_FACTORS,
    VAT_SWITZERLAND,
)
from custom.models.models import ExchangeRate


class Countries(object):
    """
    Provides list of countries with their codes, translations, etc.
    """

    __default_vat = Decimal(0.23)

    class Country(object):
        """
        Represents single country
        """

        def __init__(
            self,
            name,
            translated_name,
            code,
            vat,
            language_code,
            locale,
            currency,
        ):
            self.name = name
            self.translated_name = translated_name
            self.code = code
            self.vat = vat
            self.language_code = language_code
            self.locale = locale
            self.currency = currency

        def __str__(self):
            return (
                f'CountryMember[name={self.name} '
                f'code={self.code} '
                f'vat={self.vat:.2f} '
                f'language_code={self.language_code} '
                f'locale={self.locale} '
                f'current={self.currency}]'
            )

        def get_exchange_rate(self):
            today = date.today()
            exchange_rates = ExchangeRate.get_safe_exchange(
                today.year, today.month, today.day
            )
            currency_code = self.currency if self.currency in exchange_rates else 'EUR'
            return exchange_rates[currency_code]

        @property
        def iso_alpha_2_country_code(self):
            """
            Our constant country codes fit to ISO Alpha-2 norm, except United Kingdom
            """
            return 'GB' if self.code == 'UK' else self.code

    austria = Country(
        'austria', _('common_austria'), 'AT', __default_vat, 'de', 'de_AT', 'EUR'
    )
    belgium = Country(
        'belgium', _('common_belgium'), 'BE', __default_vat, 'nl', 'nl_BE', 'EUR'
    )
    bulgaria = Country(
        'bulgaria', _('common_bulgaria'), 'BG', __default_vat, 'bg', 'bg_BG', 'EUR'
    )
    croatia = Country(
        'croatia', _('common_croatia'), 'HR', __default_vat, 'hr', 'hr_HR', 'HRK'
    )
    czech = Country(
        'czech', _('common_czech'), 'CZ', __default_vat, 'cs', 'cs_CZ', 'CZK'
    )
    denmark = Country(
        'denmark', _('common_denmark'), 'DK', __default_vat, 'da', 'da_DK', 'DKK'
    )
    estonia = Country(
        'estonia', _('common_estonia'), 'EE', __default_vat, 'et', 'et_EE', 'EUR'
    )
    finland = Country(
        'finland', _('common_finland'), 'FI', __default_vat, 'fi', 'fi_FI', 'EUR'
    )
    france = Country(
        'france', _('common_france'), 'FR', __default_vat, 'fr', 'fr_FR', 'EUR'
    )
    germany = Country(
        'germany', _('common_germany'), 'DE', __default_vat, 'de', 'de_DE', 'EUR'
    )
    greece = Country(
        'greece', _('common_greece'), 'GR', __default_vat, 'el', 'el_GR', 'EUR'
    )
    hungary = Country(
        'hungary', _('common_hungary'), 'HU', __default_vat, 'hu', 'hu_HU', 'HUF'
    )
    ireland = Country(
        'ireland', _('common_ireland'), 'IE', __default_vat, 'ga', 'ga_IE', 'EUR'
    )
    italy = Country(
        'italy', _('common_italy'), 'IT', __default_vat, 'it', 'it_IT', 'EUR'
    )
    latvia = Country(
        'latvia', _('common_latvia'), 'LV', __default_vat, 'lv', 'lv_LV', 'EUR'
    )
    lithuania = Country(
        'lithuania', _('common_lithuania'), 'LT', __default_vat, 'lt', 'lt_LT', 'EUR'
    )
    luxembourg = Country(
        'luxembourg', _('common_luxembourg'), 'LU', __default_vat, 'fr', 'fr_LU', 'EUR'
    )
    netherlands = Country(
        'netherlands',
        _('common_netherlands'),
        'NL',
        __default_vat,
        'nl',
        'nl_NL',
        'EUR',
    )
    norway = Country(
        'norway',
        _('common_norway'),
        'NO',
        VAT_FACTORS[VAT_SWITZERLAND],
        'no',
        'no_NO',
        'NOK',
    )
    poland = Country(
        'poland', _('common_poland'), 'PL', __default_vat, 'pl', 'pl_PL', 'PLN'
    )
    portugal = Country(
        'portugal', _('common_portugal'), 'PT', __default_vat, 'pt', 'pt_PT', 'EUR'
    )
    romania = Country(
        'romania', _('common_romania'), 'RO', __default_vat, 'ro', 'ro_RO', 'RON'
    )
    slovakia = Country(
        'slovakia', _('common_slovakia'), 'SK', __default_vat, 'sk', 'sk_SK', 'EUR'
    )
    slovenia = Country(
        'slovenia', _('common_slovenia'), 'SI', __default_vat, 'sl', 'sl_SI', 'EUR'
    )
    spain = Country(
        'spain', _('common_spain'), 'ES', __default_vat, 'es', 'es_ES', 'EUR'
    )
    sweden = Country(
        'sweden', _('common_sweden'), 'SE', __default_vat, 'sv', 'sv_SE', 'SEK'
    )
    switzerland = Country(
        'switzerland',
        _('common_switzerland'),
        'CH',
        VAT_FACTORS[VAT_SWITZERLAND],
        'de',
        'de_CH',
        'CHF',
    )
    united_kingdom = Country(
        'united_kingdom',
        _('common_united_kingdom'),
        'UK',
        VAT_FACTORS[VAT_SWITZERLAND],
        'en',
        'en_GB',
        'GBP',
    )

    countries_pl = {
        'EE': 'Estonia',
        'CZ': 'Czechy',
        'GR': 'Grecja',
        'RO': 'Rumunia',
        'AT': 'Austria',
        'HU': 'Węgry',
        'IT': 'Włochy',
        'PT': 'Portugalia',
        'LT': 'Litwa',
        'LU': 'Łotwa',
        'FR': 'Francja',
        'SK': 'Słowacja',
        'IE': 'Irlandia',
        'HR': 'Chorwacja',
        'NO': 'Norwegia',
        'CH': 'Szwajcaria',
        'SI': 'Słowenia',
        'DE': 'Niemcy',
        'BE': 'Belgia',
        'ES': 'Hiszpania',
        'NL': 'Holandia',
        'DK': 'Dania',
        'PL': 'Polska',
        'FI': 'Finlandia',
        'SE': 'Szwecja',
        'LV': 'Łotwa',
        'BG': 'Bułgaria',
        'UK': 'Wielka Brytania',
    }

    @classproperty
    def __members__(cls):
        return {
            m: d
            for m, d in Countries.__dict__.items()
            if isinstance(d, Countries.Country)
        }

    @staticmethod
    def get_all():
        return Countries.__members__

    @staticmethod
    def get_all_country_codes():
        return [value.code for key, value in Countries.__members__.items()]

    @staticmethod
    def get_country_by_attribute(attribute, value):
        """
        Returns :class:`Country` by it's ISO 639-1 code.
        :param code: ISO 639-1 code
        :return: class:`Country`
        """
        for country in list(Countries.__members__.values()):
            if hasattr(country, attribute) and getattr(country, attribute) == value:
                return country
        return None

    @staticmethod
    def get_countries_by_language_code(language_code):
        countries = []
        for country in list(Countries.__members__.values()):
            if country.language_code == language_code:
                countries.append(country)
        return countries

    @staticmethod
    def get_country_by_code(code):
        for country in list(Countries.__members__.values()):
            if country.code == code:
                return country
        return None

    @staticmethod
    def get_country_by_currency(currency):
        for country in list(Countries.__members__.values()):
            if country.currency == currency:
                return country
        return None

    @staticmethod
    def get_country_by_name(name):
        countries = Countries.__members__
        return countries[name] if name in countries else None

    @staticmethod
    def as_choices():
        return [
            (country.name, country.translated_name)
            for country in list(Countries.__members__.values())
        ]

    @staticmethod
    def get_countries_with_0_vat():
        countries_zero_vat = []
        all_countries = Countries.__members__
        for country_name in all_countries:
            if all_countries[country_name].vat == 0:
                countries_zero_vat.append(all_countries[country_name])
        return countries_zero_vat
