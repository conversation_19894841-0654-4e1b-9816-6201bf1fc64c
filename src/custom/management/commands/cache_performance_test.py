from enum import Enum

from django.contrib.auth.models import User
from django.core.cache import cache
from django.core.management import (
    BaseCommand,
    CommandError,
)
from django.test.client import Client
from django.urls import reverse

PERFORMANCE_USERNAME = 'command_admin'
EXPECTED_CACHE_COUNTS = {
    'admin:invoice_invoice_changelist': {'get': 2, 'set': 2},
    'admin:complaints_complaint_changelist': {'get': 1, 'set': 1},
    'admin:complaints_productioncomplaint_changelist': {'get': 1, 'set': 1},
    'admin:gallery_jetty_changelist': {'get': 1, 'set': 1},
    'admin:gallery_watty_changelist': {'get': 1, 'set': 1},
    'admin:orders_order_changelist': {'get': 1, 'set': 1},
    'admin:orders_paidorders_changelist': {'get': 337, 'set': 337},
    'admin:producers_productbatch_changelist': {'get': 1, 'set': 1},
    'admin:producers_productbatchcomplaint_changelist': {'get': 1, 'set': 1},
    'admin:producers_productcomplaint_changelist': {'get': 51, 'set': 51},
}


class CacheMethod(Enum):
    GET = 'get'
    SET = 'set'


class Command(BaseCommand):
    help = 'Checks sum of cache queries performed by admin views'

    def handle(self, *args, **options):

        if not self._run_performance_test():
            raise CommandError('Cache performance test failed.')

        self.stdout.write(self.style.SUCCESS('Cache performance test succeeded.'))

    @staticmethod
    def _setup_client():
        client = Client()

        try:
            superuser = User.objects.get(username=PERFORMANCE_USERNAME)
        except User.DoesNotExist:
            superuser = User.objects.create_superuser(username=PERFORMANCE_USERNAME)

        client.force_login(superuser)

        return client

    def _run_performance_test(self):
        client = self._setup_client()
        success = True

        for view_name, expected_counts in EXPECTED_CACHE_COUNTS.items():
            client.get(reverse(view_name))
            performance_set_count, performance_get_count = cache.current_count

            is_set_count_valid = self._validate(
                CacheMethod.SET,
                view_name,
                expected_counts['set'],
                performance_set_count,
            )
            is_get_count_valid = self._validate(
                CacheMethod.GET,
                view_name,
                expected_counts['get'],
                performance_get_count,
            )

            if not (is_set_count_valid and is_get_count_valid):
                success = False

            cache.reset_counters()
        return success

    def _validate(self, method, view_name, expected_count, performance_count):
        if expected_count == performance_count:
            return True

        self.stderr.write(
            f'View {view_name} expected {expected_count} {method.name} '
            f'cache calls but got {performance_count}'
        )
        return False
