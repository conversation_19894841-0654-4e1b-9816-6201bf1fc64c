from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
from django.utils import timezone

from custom.models import GlobalSettings
from user_profile.choices import UserType
from vouchers.models import Voucher


class Command(BaseCommand):
    help = ''

    def handle(self, **options):
        self.stdout.write('Setting as not production/live')
        GlobalSettings.set_payment_sandbox()

        try:
            user_automaty = User.objects.get(username='<EMAIL>')
            user_automaty.is_staff = True
            user_automaty.is_superuser = True
            user_automaty.save()
        except User.DoesNotExist:
            user_automaty = User.objects.create_superuser(
                '<EMAIL>',
                '<EMAIL>',
                password='klikam123',
            )
        user_automaty.profile.user_type = UserType.STAFF
        user_automaty.profile.save()

        Voucher.objects.get_or_create(
            code='okejokejokej',
            defaults={
                'value': 100,
                'amount_limit': 100000,
                'amount_starts': 0,
                'code': 'okejokejokej',
                'creator': user_automaty,
                'origin': 0,
                'quantity': 1000,
                'quantity_left': 1000,
                'start_date': timezone.now(),
            },
        )
