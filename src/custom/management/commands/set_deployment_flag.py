from django.core.management.base import BaseCommand

from custom.models import GlobalSettings


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            '--in_progress',
            action='store_true',
        )
        parser.add_argument(
            '--finished',
            action='store_true',
        )

    def handle(self, *args, **options):
        if options['in_progress']:
            GlobalSettings.set_deployment_in_progress()
        elif options['finished']:
            GlobalSettings.set_deployment_finished()
