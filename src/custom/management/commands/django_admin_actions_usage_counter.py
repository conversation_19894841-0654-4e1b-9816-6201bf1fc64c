import csv
import enum
import sys

from collections import (
    defaultdict,
    namedtuple,
)
from unittest import mock

from django.core.cache import cache
from django.core.management.base import BaseCommand

# hack to load custom admin site without setupping whole django
from cstm_be.urls import admin

DjangoAdminAction = namedtuple(
    'DjangoAdminAction',
    ['app_label', 'model_admin_name', 'action_name', 'usage_count'],
)


class OutputFormat(enum.Enum):
    DEFAULT = 'default'
    CSV = 'csv'


class Command(BaseCommand):
    help = "Prints django's admin actions usage counts"

    def add_arguments(self, parser):
        parser.add_argument(
            '--max-usage-limit',
            help='Show django admin actions used at most given times',
            type=int,
            default=sys.maxsize,
        )
        parser.add_argument(
            '--min-usage-limit',
            help='Show django admin actions used at least given times',
            type=int,
            default=1,
        )
        parser.add_argument(
            '--format',
            type=str,
            help="Output's format",
            choices=[member.value for member in OutputFormat],
            default=OutputFormat.DEFAULT.value,
        )

    def handle(self, *args, **options):
        prefix = 'django_admin_action:'
        pattern = '{}*'.format(prefix)
        data = []
        for key, usage_count in cache.get_many(cache.keys(pattern)).items():
            stripped_key = key[len(prefix) :]  # noqa E203
            try:
                model_admin_str, action_name = stripped_key.rsplit('.', 1)
                app_label, model_admin_name = model_admin_str.split('.', 1)
            except ValueError:
                continue
            else:
                data.append(
                    DjangoAdminAction(
                        app_label,
                        model_admin_name,
                        action_name,
                        usage_count,
                    ),
                )
        data = self._filter_admin_actions_usage(
            data,
            options['min_usage_limit'],
            options['max_usage_limit'],
        )
        output_method = getattr(
            self,
            'output_{}'.format(
                options.get('format', OutputFormat.DEFAULT.value),
            ),
            self.output_default,
        )
        output_method(data)

    def output_csv(self, data):
        csv_writer = csv.writer(self.stdout)
        csv_writer.writerow(
            ['app_label', 'model_admin_name', 'action_name', 'usage_count'],
        )
        for row in sorted(data):
            csv_writer.writerow(row)

    def output_default(self, data):
        indentation = '\t'
        _data = defaultdict(lambda: defaultdict(dict))
        for app_label, model_admin_name, action_name, usage_count in data:
            _data[app_label][model_admin_name][action_name] = usage_count
        for app_label, app_data in _data.items():
            self.stdout.write('{}:\n'.format(app_label))
            for model_admin_str, model_admin_data in app_data.items():
                self.stdout.write(
                    '{}{}:\n'.format(indentation, model_admin_str),
                )
                for action_name, usage_count in model_admin_data.items():
                    self.stdout.write(
                        '{}{}: {}\n'.format(
                            indentation * 2,
                            action_name,
                            usage_count,
                        ),
                    )

    def _filter_admin_actions_usage(
        self,
        data,
        min_usage_limit,
        max_usage_limit,
    ):
        unused_data = []
        all_admin_actions_counter = self._get_all_admin_actions_counter()
        for row in data:
            all_admin_actions_counter[tuple(row[:-1])] = row.usage_count
        for row_data, usage_count in all_admin_actions_counter.items():
            if min_usage_limit <= usage_count <= max_usage_limit:
                unused_data.append(
                    DjangoAdminAction(*row_data, usage_count=usage_count),
                )
        return unused_data

    def _get_all_admin_actions_counter(self):
        all_actions = {}
        for model_admin in admin.site._registry.values():
            app_label = model_admin.model._meta.app_label
            model_admin_name = self._get_model_admin_name(model_admin)
            for action_name in model_admin.get_actions(mock.Mock(GET={})):
                all_actions[(app_label, model_admin_name, action_name)] = 0
        return all_actions

    @staticmethod
    def _get_model_admin_name(model_admin):
        if str(model_admin).endswith('.DynamicAdminViewPermissionModelAdmin'):
            return model_admin.__class__.__bases__[0].__name__
        return str(model_admin).split('.')[-1]
