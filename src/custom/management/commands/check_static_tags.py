import os
import re

from collections import defaultdict
from dataclasses import dataclass

import django.apps

from django.conf import settings
from django.contrib.staticfiles.management.commands.findstatic import (
    Command as FindStaticCommand,
)
from django.core.exceptions import SuspiciousFileOperation
from django.core.management.base import (
    BaseCommand,
    SystemCheckError,
)

FINDSTATIC_OPTIONS = {
    'verbosity': 1,
    'all': False,
}


@dataclass
class StaticWithTemplate:
    static_name: str
    template_names: list


class MissingStaticFilesStdOut:
    def __init__(self, statics_per_template):
        self.statics_per_template = statics_per_template
        self.missing_files = []

    def write(self, msg=''):
        if msg.startswith('No matching file found for '):
            path = (
                msg.strip()
                .rstrip('.')
                .replace('No matching file found for ', '')
                .replace("'", '')
            )
            missing_static = StaticWithTemplate(path, self.statics_per_template[path])
            self.missing_files.append(missing_static)


class Command(BaseCommand):
    help = 'Checks for missing static refs in templates using the {% static %} tag.'

    def add_arguments(self, parser):
        parser.add_argument(
            '--with-error',
            action='store_true',
            help='Raise errors if missing static files are found',
        )

    def handle(self, *args, **options):
        template_files = self.find_templates()
        static_paths, statics_per_template = self.extract_static_paths(template_files)
        missing_files_out = self.check_static_files(static_paths, statics_per_template)

        if missing_files_out.missing_files:
            self.stdout.write(self.style.ERROR('Missing static files:'))
            for file in missing_files_out.missing_files:
                self.stdout.write(
                    self.style.ERROR(f'{file.static_name}, {file.template_names}')
                )
            if options['with_error']:
                raise SystemCheckError('Missing static files')
        else:
            self.stdout.write(self.style.SUCCESS('All static files found.'))

    def find_templates(self):
        templates_dirs = settings.TEMPLATES[0]['DIRS'] + [
            os.path.join(app.path, 'templates')
            for app in django.apps.apps.get_app_configs()
        ]
        template_files = []

        for dir_path in templates_dirs:
            for root, dirs, files in os.walk(dir_path):
                for file in files:
                    if file.endswith('.html'):
                        template_files.append(os.path.join(root, file))

        return template_files

    def extract_static_paths(self, template_files):
        static_paths = []
        statics_per_template = defaultdict(list)
        pattern = re.compile(r'\{%\s*static\s*[\'"]([^\'"]+)[\'"]\s*%\}')

        for template_file in template_files:
            with open(template_file, 'r') as file:
                content = file.read()
                matches = pattern.findall(content)
                static_paths.extend(matches)
                for match in matches:
                    statics_per_template[match].append(template_file)

        return static_paths, statics_per_template

    def check_static_files(self, static_paths, statics_per_template):
        missing_files_out = MissingStaticFilesStdOut(statics_per_template)

        for path in static_paths:
            try:
                FindStaticCommand(stderr=missing_files_out).handle(
                    path, **FINDSTATIC_OPTIONS
                )
            except SuspiciousFileOperation:
                missing_static = StaticWithTemplate(
                    path, missing_files_out.statics_per_template[path]
                )
                missing_files_out.missing_files.append(missing_static)

        return missing_files_out
