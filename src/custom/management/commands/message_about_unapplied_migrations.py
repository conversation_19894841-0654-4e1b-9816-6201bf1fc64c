from dataclasses import dataclass

from django.core.management import BaseCommand
from django.core.management.commands.showmigrations import (
    Command as ShowMigrationsCommand,
)
from django.core.management.commands.sqlmigrate import Command as SqlMigrateCommand
from django.db import DEFAULT_DB_ALIAS

import environ
import requests

env = environ.Env()

SHOW_MIGRATIONS_OPTIONS = {
    'app_label': [],
    'database': DEFAULT_DB_ALIAS,
    'format': 'plan',
    'verbosity': 1,
}


SQL_MIGRATE_OPTIONS = {
    'database': DEFAULT_DB_ALIAS,
    'verbosity': 1,
    'backwards': False,
}

ENV_TO_SLACK_CHANNEL = {
    'dev': 'pr-bi-migrations',
    'stg': 'bi-migrations',
}

LONG_RUNNING_MIGRATION_TABLES = [
    'user_profile_userprofile',
    'auth_user',
    'orders_order',
    'vouchers_voucher',
]


@dataclass
class MigrationDTO:
    app_label: str
    migration_name: str


class UnappliedMigrationStdOut:
    def __init__(self):
        self.unapplied_migrations = []

    def write(self, msg=''):
        if msg.startswith('[ ] '):
            migration = msg.strip().replace('[ ]  ', '').split('.')
            migration_dto = MigrationDTO(
                app_label=migration[0],
                migration_name=migration[1],
            )
            self.unapplied_migrations.append(migration_dto)


class Command(BaseCommand):
    def add_arguments(self, parser):
        parser.add_argument(
            '--env',
            help='',
            type=str,
            default='dev',
        )
        parser.add_argument(
            '--plan',
            type=str,
            default='database',
        )
        parser.add_argument(
            '--pull-request-url',
            type=str,
        )
        parser.add_argument(
            'files',
            nargs='*',
            type=str,
            help='List of files to process',
        )

    def handle(self, *args, **options):
        if options['plan'] == 'database':
            unapplied_migrations = self.migrations_from_database_state()
        else:
            unapplied_migrations = self.migrations_from_files(options['files'])

        if not unapplied_migrations:
            return None

        if options['pull_request_url']:
            pull_request = f"Pull Request: {options['pull_request_url']}\n"
        else:
            pull_request = ''

        sql_migrate_command = SqlMigrateCommand()
        sql_commands = ''
        sql_long_running_commands = ''

        for migration in unapplied_migrations:
            migration_path = f'\n *src/{migration.app_label}/migrations/{migration.migration_name}.py:* \n'  # noqa: E501

            commands = sql_migrate_command.handle(
                app_label=migration.app_label,
                migration_name=migration.migration_name,
                **SQL_MIGRATE_OPTIONS,
            )
            sql_commands += migration_path
            sql_commands += f'```{commands}```'
            if self.is_long_running_dml_update(commands):
                sql_long_running_commands += migration_path
                sql_long_running_commands += f'```{commands}```'

        requests.post(
            env.str('UNAPPLIED_MIGRATION_SLACK_WEBHOOK', default=''),
            json={
                'text': f'{pull_request}{sql_commands}',
                'channel': ENV_TO_SLACK_CHANNEL[options['env']],
                'username': 'Migration CSTM Bot',
                'icon_emoji': ':pikachu:',
            },
        )

        if options['env'] == 'stg' and sql_long_running_commands:
            requests.post(
                env.str('UNAPPLIED_MIGRATION_SLACK_WEBHOOK', default=''),
                json={
                    'text': f'There are some long-running migrations in the upcoming '
                    f'release. Please be aware of this when planning the '
                    f'deployment.{sql_long_running_commands}',
                    'channel': 'it-deployment',
                    'username': 'Migration CSTM Bot',
                    'icon_emoji': ':pikachu:',
                },
            )

    @classmethod
    def migrations_from_files(cls, files):
        unapplied_migrations = []
        for file_path in files:
            splitted = file_path.split('/migrations/')
            migration_dto = MigrationDTO(
                app_label=splitted[0].replace('src/', ''),
                migration_name=splitted[1].replace('.py', ''),
            )
            unapplied_migrations.append(migration_dto)
        return unapplied_migrations

    @classmethod
    def migrations_from_database_state(cls):
        std_out = UnappliedMigrationStdOut()
        ShowMigrationsCommand(stdout=std_out).handle(**SHOW_MIGRATIONS_OPTIONS)
        return std_out.unapplied_migrations

    def is_long_running_dml_update(self, commands):
        long_running_updates = [
            f'UPDATE "{table}"' for table in LONG_RUNNING_MIGRATION_TABLES
        ]
        return any(long_update in commands for long_update in long_running_updates)
