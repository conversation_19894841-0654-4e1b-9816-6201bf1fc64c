from datetime import datetime
from decimal import Decimal

from django.conf import settings
from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import User
from django.core.management.base import BaseCommand
from django.utils import timezone

from dateutil import relativedelta
from rest_framework.authtoken.models import Token

from custom.models import GlobalSettings
from pricing_v3.models import PricingVersion
from producers.models import (
    Manufactor,
    ManufactorAdditionalAccounts,
)
from promotions.models import (
    Promotion,
    PromotionConfig,
)
from rating_tool.models import BoardCategory
from regions.models import Region
from user_profile.choices import UserType
from vouchers.enums import (
    VoucherOrigin,
    VoucherType,
)
from vouchers.models import (
    Voucher,
    VoucherGroup,
)


def generate_key(suffix):
    return f'46076137574ab{suffix}'


class Command(BaseCommand):
    help = ''

    def handle(self, *args, **options):
        GlobalSettings.set_payment_sandbox()
        admin = User.objects.get(username='admin')

        voucher, created = Voucher.objects.get_or_create(
            code='gosia20',
            defaults={
                'kind_of': VoucherType.PERCENTAGE,
                'creator': admin,
                'origin': VoucherOrigin.MANUAL,
                'value': 20,
                'start_date': timezone.now(),
                'end_date': datetime(
                    year=2025,
                    month=12,
                    day=31,
                    hour=23,
                    minute=59,
                ),
                'quantity': 5000,
                'quantity_left': 5000,
                'amount_limit': 100000,
            },
        )

        Voucher.objects.get_or_create(
            code=Voucher.INFLUENCER_GENERIC_CODE,
            defaults={
                'kind_of': VoucherType.PERCENTAGE,
                'creator': admin,
                'origin': VoucherOrigin.INFLUENCERS_BARTER_DEAL,
                'value': 0,
                'start_date': timezone.now(),
                'quantity': -1,
                'quantity_left': 0,
                'amount_limit': 100000,
            },
        )

        user, created = User.objects.get_or_create(
            username='<EMAIL>',
            defaults={
                'email': '<EMAIL>',
                'password': make_password(settings.SELENIUM_USER_PASSWORD),
                'is_superuser': True,
                'is_active': True,
                'is_staff': True,
            },
        )

        user.profile.user_type = UserType.STAFF
        user.profile.region = Region.objects.get(name='germany')
        user.profile.save(update_fields=['user_type', 'region'])

        # add token used in parametric tests
        Token.objects.get_or_create(
            key='149e5b36fce81cdb6efaef1cb4296c1fb3fe90b9',
            defaults={'user': user},
        )

        user, _ = User.objects.get_or_create(username='cstm')
        Token.objects.filter(user=user).delete()
        Token.objects.create(
            user=user,
            key='f5a6f7bbf230b31aeaa43d62c0eeab56c8d0bd52',
        )
        user, _ = User.objects.get_or_create(username='logistic')
        Token.objects.filter(user=user).delete()
        Token.objects.create(
            user=user,
            key='1bfee92f4b83889fff371ce3797711bd6f199b4a',
        )

        user, _ = User.objects.get_or_create(username='marathon@prod')
        Token.objects.filter(user=user).delete()
        Token.objects.get_or_create(
            user=user,
            defaults={'key': generate_key('Marathon')},
        )

        for manufactor in Manufactor.objects.all():
            Token.objects.get_or_create(
                user=manufactor.owner,
                defaults={'key': generate_key(manufactor.name)},
            )

        for manufactor_additional_account in ManufactorAdditionalAccounts.objects.all():
            Token.objects.get_or_create(
                user=manufactor_additional_account.user,
                defaults={'key': generate_key(str(manufactor_additional_account.id))},
            )

        # minidb has broken coefficients
        self.fix_pricing_versions()

        promo = Promotion.objects.filter(active=True).exists()
        if not promo:
            self._create_promo(voucher)

        for board_category in BoardCategory.objects.all():
            board_category.generate_boards_data_dict()

    @staticmethod
    def fix_pricing_versions():
        for pricing_version in PricingVersion.objects.all():
            for key, value in pricing_version.coefficients.items():
                if value and isinstance(value, str):
                    pricing_version.coefficients[key] = Decimal(value)
            pricing_version.save(update_fields=['coefficients'])

    @staticmethod
    def _create_promo(voucher):
        promo = Promotion.objects.create(
            promo_code=voucher,
            active=True,
            start_date=timezone.now(),
            end_date=timezone.now() + relativedelta.relativedelta(years=1),
        )
        voucher_group = VoucherGroup.objects.last()
        promo.promo_group.add(voucher_group)

        promo_config = PromotionConfig.objects.create(
            promotion=promo,
            ribbon_enabled=True,
        )

        regions = Region.objects.all()
        promo_config.enabled_regions.set(regions)
