import subprocess

from collections import (
    deque,
    namedtuple,
)
from datetime import (
    datetime,
    timedelta,
)
from optparse import make_option

from django.core.management.base import (
    BaseCommand,
    CommandError,
)

LogRecord = namedtuple('LogRecord', 'date message')


class Command(BaseCommand):
    help = 'Exports presets, materials, pricing'

    option_list = list(BaseCommand.option_list) + [
        make_option(
            '-d',
            '--dates',
            action='store',
            type='string',
            dest='dates',
            help='Dates comma-separated',
        ),
    ]

    def handle(self, *args, **options):

        if len(args) == 0 and not options['dates']:
            raise CommandError(
                'Pass the comma separated dates, eg. -d 2015-12-01,2015-12-07'
            )

        deploy_dates = [
            datetime.strptime(d, '%Y-%m-%d') + timedelta(hours=23, minutes=59)
            for d in options['dates'].split(',')
        ]
        first_deploy_date = (deploy_dates[0] - timedelta(weeks=1)).strftime('%Y-%m-%d')

        gitlog = {}
        gitlog_command = ['git'] + (
            'log --oneline --pretty=format:%%ci::%%s --after="%s" -1000'
            % first_deploy_date
        ).split()
        gitlog_output = (
            subprocess.Popen(gitlog_command, stdout=subprocess.PIPE)
            .communicate()[0]
            .split('\n')
        )
        for go in reversed(gitlog_output):
            if 'Merge' in go:
                continue
            log = go.split('::')
            gitlog[datetime.strptime(log[0], '%Y-%m-%d %H:%M:%S +0100')] = log[1]

        deploy_history = {}
        deploy_date_it = iter(deploy_dates)
        deploy_date = next(deploy_date_it)
        for log_date in sorted(gitlog.keys()):
            if log_date > deploy_date:
                try:
                    deploy_date = next(deploy_date_it)
                except StopIteration:
                    break
            if deploy_date not in deploy_history:
                deploy_history[deploy_date] = deque()
            deploy_history[deploy_date].append(
                LogRecord(date=log_date, message=gitlog[log_date])
            )

        for deploy_date in sorted(deploy_history.keys()):
            self.stdout.write(str(deploy_date))
            for log_record in deploy_history[deploy_date]:
                self.stdout.write(
                    '\t%s: %s'
                    % (
                        log_record.date,
                        log_record.message.decode('utf-8', errors='ignore'),
                    )
                )
