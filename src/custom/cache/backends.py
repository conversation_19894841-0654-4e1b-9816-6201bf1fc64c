from django.core.cache.backends.dummy import DummyCache


class DummyCounterCache(DummyCache):
    method_set_counter = 0
    method_get_counter = 0

    def set(self, *args, **kwargs):
        self.method_set_counter += 1
        return super().set(*args, **kwargs)

    def get(self, *args, **kwargs):
        self.method_get_counter += 1
        return super().get(*args, **kwargs)

    def reset_counters(self):
        self.method_set_counter = 0
        self.method_get_counter = 0

    @property
    def current_count(self):
        return self.method_set_counter, self.method_get_counter
