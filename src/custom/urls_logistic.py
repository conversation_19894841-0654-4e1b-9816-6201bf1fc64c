from django.urls import (
    path,
    re_path,
)

from custom.views_logistic import (
    AssemblyServiceAcceptDateProposalView,
    AssemblyServiceNewDateRequestView,
    ComplaintServiceAcceptDateProposalView,
    ComplaintServiceNewDateRequestView,
    DeliveryTimeFrameView,
    Email24View,
)

urlpatterns = [
    path(
        'assembly-service/date-proposals/<hashid:date_proposal_id>/accept/',
        AssemblyServiceAcceptDateProposalView.as_view(),
        name='assembly_service_accept_date_proposal',
    ),
    path(
        'assembly-service/<hashid:service_id>/new-date-request/',
        AssemblyServiceNewDateRequestView.as_view(),
        name='assembly_service_new_date_request',
    ),
    path(
        'complaint-service/date-proposals/<hashid:date_proposal_id>/accept/',
        ComplaintServiceAcceptDateProposalView.as_view(),
        name='complaint_service_accept_date_proposal',
    ),
    path(
        'complaint-service/<hashid:service_id>/new-date-request/',
        ComplaintServiceNewDateRequestView.as_view(),
        name='complaint_service_new_date_request',
    ),
    re_path(
        r'^delivery-time-frames/(?P<dtf_id>[0-9]+)/',
        DeliveryTimeFrameView.as_view(),
        name='dtf-proposal',
    ),
    re_path(
        r'^email24',
        Email24View.as_view(),
        name='email24',
    ),
]
