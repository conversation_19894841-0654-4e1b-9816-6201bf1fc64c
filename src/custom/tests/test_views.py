from django.urls import reverse

import pytest
import requests_mock

from rest_framework import status

from custom.enums import LanguageEnum
from events.models import Event


@pytest.mark.django_db
class TestGoogleRecaptcha:
    url = reverse('google-recaptcha-verify')

    def test_response_missing_token(self, api_client, user):
        api_client.force_authenticate(user)
        response = api_client.post(self.url)

        assert response.status_code == status.HTTP_400_BAD_REQUEST

    def test_response_success(self, api_client, user):
        api_client.force_authenticate(user)
        data = {'token': 'tylko'}
        response_data = {'success': True}

        with requests_mock.Mocker() as m:
            m.post(requests_mock.ANY, json=response_data)
            response = api_client.post(self.url, data=data, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.json() == response_data


@pytest.mark.django_db
def test_set_language_emits_language_update_event_with_new_language(
    api_client,
    user,
):
    url = reverse('set_language')
    api_client.force_login(user)
    api_client.get(url, {'language': LanguageEnum.FR})

    assert Event.objects.count() == 1
    event = Event.objects.last()

    assert event.event_name == 'LanguageUpdateEvent'
    assert event.properties['language'] == LanguageEnum.FR


@pytest.mark.django_db
def test_set_language_redirect(api_client, user):
    url = reverse('set_language')
    api_client.force_login(user)
    response = api_client.get(
        url, {'language': LanguageEnum.DE, 'next': '/en/product-lines/storage/'}
    )

    assert response.status_code == 302
    assert response.url == '/de/produktlinien/aufbewahrung/'
