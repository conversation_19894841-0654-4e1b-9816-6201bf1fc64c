import datetime

import pytest

from custom.helpers import add_business_days


@pytest.mark.parametrize(
    ('from_date', 'days', 'expected_date'),
    [
        (datetime.date(2021, 6, 1), 3, datetime.date(2021, 6, 7)),
        (datetime.date(2021, 7, 16), 5, datetime.date(2021, 7, 23)),
        (datetime.date(2021, 10, 5), 1, datetime.date(2021, 10, 6)),
        (datetime.date(2021, 10, 8), 1, datetime.date(2021, 10, 11)),
        (datetime.date(2021, 10, 6), 3, datetime.date(2021, 10, 11)),
        (datetime.date(2021, 10, 7), 3, datetime.date(2021, 10, 12)),
        (datetime.date(2021, 10, 8), 5, datetime.date(2021, 10, 15)),
        (datetime.date(2021, 10, 9), 5, datetime.date(2021, 10, 15)),
        (datetime.date(2021, 10, 10), 5, datetime.date(2021, 10, 15)),
        (datetime.date(2021, 10, 5), 10, datetime.date(2021, 10, 19)),
        (datetime.date(2021, 10, 8), 10, datetime.date(2021, 10, 22)),
        (datetime.date(2021, 10, 9), 10, datetime.date(2021, 10, 22)),
    ],
)
def test_add_business_days(from_date, days, expected_date):
    test_date = add_business_days(from_date, days, check_holidays=True)
    assert test_date == expected_date
