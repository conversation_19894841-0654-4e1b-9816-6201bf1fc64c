import datetime

from decimal import Decimal

from invoice.symfonia import (
    revert_storno_adjustments,
    storno_adjustments,
)


class TestSymfoniaExport:
    def test_storno_adjustments_should_adjust_row_when_ok(self):
        symfonia_row = {
            'numer_faktury': '00000/03/2025/123/UK',
            'nettopln': Decimal('10.0'),
        }

        adjusted_row = storno_adjustments(symfonia_row)

        assert adjusted_row['numer_faktury'] == '00000/03/2025/123/UK - STORNO'
        assert adjusted_row['nettopln'] == Decimal('-10.0')

    def test_revert_storno_adjustments_should_adjust_row_when_ok(self):
        symfonia_row = {
            'numer_faktury': '00000/03/2025/123/UK',
            'okres sprawozdawczy': '',
        }

        adjusted_row = revert_storno_adjustments(
            symfonia_row, datetime.date(2025, 10, 10)
        )

        assert adjusted_row['numer_faktury'] == '00000/03/2025/123/UK - OK'
        assert adjusted_row['okres sprawozdawczy'] == '2025-10-10'
