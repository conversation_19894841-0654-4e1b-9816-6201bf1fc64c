import urllib.parse

from unittest.mock import patch

from django.conf import settings

from custom.api_clients.braze import BrazeClient


class TestBrazeClient:
    def test_send_user_tracking_data_returns_failed_events_ids(
        self,
        requests_mock,
        settings,
        braze_event_object_factory,
        braze_purchase_object_factory,
    ):
        event_object = braze_event_object_factory(event_id=1)
        purchase_object = braze_purchase_object_factory(event_id=2)

        url = urllib.parse.urljoin(settings.BRAZE_API_URL, 'users/track')
        requests_mock.register_uri(
            'POST',
            url,
            json={
                'attributes_processed': 1,
                'events_processed': 1,
                'message': 'success',
                'errors': [
                    {'type': 'app_id is invalid', 'input_array': 'events', 'index': 0},
                ],
            },
        )

        failed_events_ids = BrazeClient().send_user_tracking_data(
            attribute_objects=[],
            event_objects=[event_object],
            purchase_objects=[purchase_object],
        )

        assert event_object.event_id in failed_events_ids
        assert purchase_object.event_id not in failed_events_ids

    def test_send_user_tracking_data_calls_expected_url_with_expected_data(
        self,
        braze_user_attributes_object,
        braze_event_object,
        braze_purchase_object,
    ):
        with patch(
            'custom.api_clients.braze.BrazeClient._send_request',
        ) as send_request_mock:
            url = urllib.parse.urljoin(settings.BRAZE_API_URL, 'users/track')

            BrazeClient.send_user_tracking_data(
                attribute_objects=[braze_user_attributes_object],
                event_objects=[braze_event_object],
                purchase_objects=[braze_purchase_object],
            )

            assert send_request_mock.call_count == 1
            assert send_request_mock.call_args[0][0] == url
            assert send_request_mock.call_args[0][1] == {
                'events': [braze_event_object.to_dict()],
                'attributes': [braze_user_attributes_object.to_dict()],
                'purchases': [braze_purchase_object.to_dict()],
            }

    def test_update_subscriptions_calls_expected_url_with_expected_data(
        self,
        braze_subscription_object_factory,
        settings,
    ):
        braze_subscription_object = braze_subscription_object_factory(
            subscription_group_id='abc',
        )
        settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter'] = 'abc'

        with patch(
            'custom.api_clients.braze.BrazeClient._send_request',
        ) as send_request_mock:
            url = urllib.parse.urljoin(
                settings.BRAZE_API_URL, 'subscription/status/set'
            )

            BrazeClient.update_subscriptions(
                subscription_group=braze_subscription_object.subscription_group_id,
                subscription_state=braze_subscription_object.subscription_state,
                subscriptions_objects=[braze_subscription_object],
            )

            assert send_request_mock.call_count == 1
            assert send_request_mock.call_args[0][0] == url
            assert send_request_mock.call_args[0][1] == {
                'subscription_group_id': braze_subscription_object.subscription_group_id,
                'subscription_state': braze_subscription_object.subscription_state,
                'email': [braze_subscription_object.email],
            }

    def test_trigger_canvas_calls_expected_url_with_expected_data(
        self,
        braze_recipient_object,
    ):
        with patch(
            'custom.api_clients.braze.BrazeClient._send_request',
        ) as send_request_mock:
            url = urllib.parse.urljoin(settings.BRAZE_API_URL, '/canvas/trigger/send')

            BrazeClient.trigger_canvas(
                canvas_id=braze_recipient_object.canvas_id,
                braze_objects=[braze_recipient_object],
            )

            assert send_request_mock.call_count == 1
            assert send_request_mock.call_args[0][0] == url
            assert send_request_mock.call_args[0][1] == {
                'canvas_id': braze_recipient_object.canvas_id,
                'recipients': [braze_recipient_object.recipient],
            }
