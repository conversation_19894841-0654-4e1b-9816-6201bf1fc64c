import datetime

import pytest
import requests

from requests import exceptions
from requests_mock.response import create_response
from rest_framework import status

from custom.api_clients import nbp
from custom.api_clients.nbp import NBPApiClientException
from custom.tests.utils.fake_nbp_exchange_table import fake_nbp_get_exchange_table


@pytest.fixture
def nbp_api_client():
    return nbp.NBPAPIClient()


def throw_api_exception(path: str):
    raise exceptions.ConnectionError()


def empty_response(path: str):
    return create_response(
        requests.Request('GET', url='http://test.api.nbp.pl/api/'),
        json=[],
        status_code=status.HTTP_200_OK,
    )


@pytest.mark.nbp
class TestNBPAPIClient:
    @pytest.mark.parametrize(
        ('base_url', 'expected_base_url'),
        [
            ('http://api/nbp.pl/api/',) * 2,
            ('http://api/nbp.pl/api', 'http://api/nbp.pl/api/'),
        ],
    )
    def test_base_url(self, base_url, expected_base_url):
        """Ensure ``base_url`` has trailing slash added."""
        nbp_api_client = nbp.NBPAPIClient(base_url=base_url)
        assert nbp_api_client.base_url == expected_base_url

    @pytest.mark.parametrize('table_type', ['', 'z', 'Z', 'AB'])
    def test_get_exchange_table_when_table_type_invalid(
        self,
        nbp_api_client,
        table_type,
    ):
        """Ensure ``nbp.UnsupportedNBPTableType`` is raised."""
        assert table_type not in nbp.SUPPORTED_TABLE_TYPES

        with pytest.raises(nbp.UnsupportedNBPTableType):
            nbp_api_client.get_exchange_table(table_type=table_type)

    def test_get_exchange_table_when_no_date_passed(
        self,
        mocker,
        nbp_api_client,
    ):
        """Ensure path for active exchange rates table is requested."""
        get_mock = mocker.patch.object(
            nbp_api_client, 'get', side_effect=fake_nbp_get_exchange_table
        )
        nbp_api_client.get_exchange_table(table_type='A')
        get_mock.assert_called_once_with('exchangerates/tables/A/')

    @pytest.mark.parametrize(
        'date',
        [
            datetime.date(2020, 7, 14),
            datetime.datetime(2020, 7, 14, 13, 2, 7, tzinfo=datetime.timezone.utc),
        ],
    )
    def test_get_exchange_table_when_only_date_passed(
        self,
        mocker,
        nbp_api_client,
        date,
    ):
        """Ensure path for exchange rates table on ``date`` is requested."""
        get_mock = mocker.patch.object(
            nbp_api_client, 'get', side_effect=fake_nbp_get_exchange_table
        )
        nbp_api_client.get_exchange_table(date, table_type='B')
        get_mock.assert_called_once_with('exchangerates/tables/B/2020-07-14/')

    def test_get_exchange_table_when_both_dates_passed(
        self,
        mocker,
        nbp_api_client,
    ):
        """Ensure path for exchange rates table for date range is requested."""
        get_mock = mocker.patch.object(
            nbp_api_client, 'get', side_effect=fake_nbp_get_exchange_table
        )
        nbp_api_client.get_exchange_table(
            datetime.date(2020, 7, 10),
            end_date=datetime.date(2020, 7, 14),
            table_type='C',
        )
        get_mock.assert_called_once_with(
            'exchangerates/tables/C/2020-07-10/2020-07-14/',
        )

    def test_get_exchange_table_when_only_end_date_passed(
        self,
        nbp_api_client,
    ):
        """Ensure ``end_date`` must be passed together with ``date``."""
        with pytest.raises(ValueError):
            nbp_api_client.get_exchange_table(
                end_date=datetime.date(2020, 7, 14),
                table_type='B',
            )

    def test_get_exchange_table_with_api_call_error(self, mocker, nbp_api_client):
        mocker.patch.object(nbp_api_client, 'get', side_effect=throw_api_exception)
        with pytest.raises(NBPApiClientException) as exc:
            nbp_api_client.get_exchange_table()

            assert exc.message == 'NBP API call error'

    def test_get_exchange_table_with_empty_rates_response_throws_error(
        self, mocker, nbp_api_client
    ):
        mocker.patch.object(nbp_api_client, 'get', side_effect=empty_response)
        with pytest.raises(NBPApiClientException) as exc:
            nbp_api_client.get_exchange_table()

            assert exc.message == 'NBP API call error'
