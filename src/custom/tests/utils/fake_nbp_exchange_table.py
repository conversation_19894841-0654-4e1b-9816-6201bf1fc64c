import json

import requests

from pytest_django.lazy_django import skip_if_no_django
from requests_mock.response import create_response
from rest_framework import status


def fake_nbp_get_exchange_table(path: str, **kwargs):
    skip_if_no_django()

    from django.conf import settings

    fixture_path = str(
        settings.APPS_DIR.path('custom/fixtures/nbp_table_a_response.json'),
    )
    with open(fixture_path) as f:
        payload = json.load(f)
    payload[0]['table'] = kwargs.get('table_type', 'A')

    path_components = path.split('/')

    if len(path_components) > 3:
        # path should be in format 'exchangerates/tables/{TABLE_TYPE}/{DATE}/'
        payload[0]['effectiveDate'] = path_components[3]
    return create_response(
        requests.Request('GET', url='http://test.api.nbp.pl/api/'),
        json=payload,
        status_code=status.HTTP_200_OK,
    )
