from unittest import mock

from django.utils import timezone

import pytest

from custom.serializers import ShelfStateSerializer
from custom.shelf_states_interactor import She<PERSON><PERSON>tatesInteractor
from gallery.serializers import JettyBigQuerySerializer


class FakeRedis:
    def __init__(self):
        self.queue = []

    def rpush(self, name, values):
        self.queue.append(values)

    def delete(self, name):
        self.queue.clear()

    def lrange(self, name, start, end):
        return self.queue


@pytest.mark.django_db
class TestShelfStatesInteractor:
    @mock.patch('custom.shelf_states_interactor.get_redis_connection')
    def test_add_to_queue_from_data_should_not_add_when_validation_errors(
        self,
        mocked_get_redis_connection,
        jetty,
        user,
    ):
        mocked_get_redis_connection.return_value = FakeRedis()
        interactor = ShelfStatesInteractor()
        interactor.add_to_queue_from_data(
            data={
                'shelf_id': jetty.id,
                'source': 'invalid source',
                'log_created_at': str(timezone.now()),
                'pagepath': '',
                'user_id': user.id,
            }
        )
        states = interactor.get_all_states_on_queue()
        assert states == []

    @mock.patch('custom.shelf_states_interactor.get_redis_connection')
    def test_add_to_queue_from_data_should_add_but_return_empty_when_jetty_no_exists(
        self,
        mocked_get_redis_connection,
        user,
    ):
        mocked_get_redis_connection.return_value = FakeRedis()
        interactor = ShelfStatesInteractor()
        interactor.add_to_queue_from_data(
            data={
                'shelf_id': 1,
                'source': 'save_for_later',
                'log_created_at': str(timezone.now()),
                'pagepath': '',
                'user_id': user.id,
            }
        )
        states = interactor.get_all_states_on_queue()
        assert states == []
        assert interactor.lrange_all() == []

    @mock.patch('custom.shelf_states_interactor.get_redis_connection')
    def test_add_to_queue_from_data_should_add_when_valid_jetty(
        self,
        mocked_get_redis_connection,
        jetty,
        user,
    ):
        mocked_get_redis_connection.return_value = FakeRedis()
        interactor = ShelfStatesInteractor()
        payload = {
            'shelf_id': jetty.id,
            'source': 'save_for_later',
            'log_created_at': str(timezone.now()),
            'pagepath': '',
            'user_id': user.id,
        }
        interactor.add_to_queue_from_data(data=payload)
        states = interactor.get_all_states_on_queue()
        serialized_data = ShelfStateSerializer(data=payload)
        serialized_data.is_valid(raise_exception=True)

        validated_data = serialized_data.validated_data
        validated_data.update(JettyBigQuerySerializer(jetty).data)

        assert states == [validated_data]
        assert interactor.lrange_all() == []
