import json

from datetime import (
    date,
    timedelta,
)
from decimal import Decimal
from typing import (
    Dict,
    List,
)
from unittest.mock import patch

import pytest

from requests import exceptions

from custom.models import (
    ExchangeRate,
    GlobalSettings,
    SingletonManager,
)
from custom.tests.factories import ExchangeRateFactory
from custom.tests.utils.fake_nbp_exchange_table import fake_nbp_get_exchange_table


def throw_api_exception(_):
    raise exceptions.ConnectionError()


@pytest.fixture
def mock_nbp_api_client_with_api_errors(mocker):
    mocker.patch(
        'custom.api_clients.nbp.NBPAPIClient.get',
        side_effect=throw_api_exception,
    )


@pytest.mark.django_db
class TestEnvironmentSettings:
    @pytest.fixture(autouse=True)
    def setup_test(self):
        GlobalSettings.objects.all().delete()

    def test_first_returns_first_objects_if_exists(self):
        GlobalSettings.objects.create(is_live_payment=True)

        environment_settings = GlobalSettings.objects.first()
        assert environment_settings.is_live_payment

    def test_first_creates_an_object_if_doesnt_exist(self):
        environment_settings = GlobalSettings.objects.first()
        assert not environment_settings.is_live_payment

    @patch('custom.models.managers.super')
    def test_first_calls_its_super(self, mock_super):
        GlobalSettings.objects.first()
        mock_super.assert_called_once()

    @patch.object(SingletonManager, 'get_queryset')
    def test_get_queryset_uses_custom_manager(self, mock_get_queryset):
        GlobalSettings.objects.all()
        mock_get_queryset.assert_called()

    def test_live_payment_switch_is_false_if_is_live_payment_is_false(self):
        GlobalSettings.objects.create(is_live_payment=False)
        assert GlobalSettings.live_payment_switch() is False

    def test_live_payment_switch_is_true_if_is_live_payment_is_true(self):
        GlobalSettings.objects.create(is_live_payment=True)
        assert GlobalSettings.live_payment_switch() is True

    def test_live_payment_switch_is_true_if_is_production_and_is_live_payment_is_false(
        self, settings
    ):
        settings.IS_PRODUCTION = True
        GlobalSettings.objects.create(is_live_payment=False)
        assert GlobalSettings.live_payment_switch() is True

    def test_live_payment_switch_is_true_if_is_production_and_is_live_payment_is_true(
        self, settings
    ):
        settings.IS_PRODUCTION = True
        GlobalSettings.objects.create(is_live_payment=True)
        assert GlobalSettings.live_payment_switch() is True

    def test_live_payment_switch_creates_object_if_didnt_exist(self):
        assert GlobalSettings.objects.count() == 0
        assert GlobalSettings().live_payment_switch() is False
        assert GlobalSettings.objects.count() == 1

    @patch('custom.models.models.logger')
    def test_set_payment_sandbox_logs_error_if_is_production(
        self, mock_logger, settings
    ):
        settings.IS_PRODUCTION = True
        GlobalSettings.set_payment_sandbox()
        mock_logger.error.assert_called_with(
            'Trying to set payments to sandbox on production.' ' Nothing will happen.'
        )

    @patch('custom.models.models.logger')
    def test_set_payment_sandbox_doesnt_log_error_if_not_production(
        self, mock_logger, settings
    ):
        settings.IS_PRODUCTION = False
        GlobalSettings.set_payment_sandbox()
        mock_logger.error.assert_not_called()

    def test_set_payment_sandbox_changes_is_live_payment_to_false(self):
        GlobalSettings.objects.create(is_live_payment=True)
        GlobalSettings.set_payment_sandbox()
        assert not GlobalSettings.objects.first().is_live_payment

    def test_set_payment_sandbox_creates_object_if_didnt_exist(self):
        assert GlobalSettings.objects.count() == 0
        GlobalSettings.set_payment_sandbox()
        assert not GlobalSettings.objects.first().is_live_payment
        assert GlobalSettings.objects.count() == 1

    def test_set_payment_live_changes_is_live_payment_to_true(self):
        GlobalSettings.objects.create(is_live_payment=False)
        GlobalSettings.set_payment_live()
        assert GlobalSettings.objects.first().is_live_payment

    def test_set_payment_live_creates_object_if_didnt_exist(self):
        assert GlobalSettings.objects.count() == 0
        GlobalSettings.set_payment_live()
        assert GlobalSettings.objects.first().is_live_payment
        assert GlobalSettings.objects.count() == 1


@pytest.mark.django_db
class TestExchangeRate:
    @pytest.mark.nbp
    def test_get_exchange_creates_object_if_does_not_exist(self, nbp_rates: List[Dict]):
        today = date.today()

        ExchangeRate._get_exchange_rates(today.year, today.month, today.day)

        assert ExchangeRate.objects.filter(exchange_date=today).count() == 1

    @pytest.mark.nbp
    def test_should_return_fixed_exchange_rate_for_pln(
        self, nbp_rates: Dict[str, Decimal]
    ):
        today = date.today()

        ExchangeRate._get_exchange_rates(today.year, today.month, today.day)

        rate = ExchangeRate.objects.get(exchange_date=today)

        assert rate.rates['PLN'] == Decimal('1.0')
        for currency in nbp_rates:
            assert rate.rates[currency['code']] == currency['mid']

    @pytest.mark.nbp
    def test_should_return_rates_for_nbp_currencies_and_pln(
        self, nbp_rates: Dict[str, Decimal]
    ) -> None:
        today = date.today()

        ExchangeRate._get_exchange_rates(today.year, today.month, today.day)

        rate = ExchangeRate.objects.get(exchange_date=today)

        assert len(rate.rates) == len(nbp_rates) + 1

    @pytest.mark.nbp
    def test_should_return_rates_for_nbp_currencies_from_api(
        self, nbp_rates: Dict[str, Decimal]
    ) -> None:
        today = date.today()

        ExchangeRate._get_exchange_rates(today.year, today.month, today.day)

        rate = ExchangeRate.objects.get(exchange_date=today)

        # rates are stored in `custom/fixtures/nbp_table_a_response.json`
        # PLN is added as a fixed rate
        for currency in nbp_rates:
            assert rate.rates[currency['code']] == currency['mid']

    @pytest.mark.nbp
    def test_should_return_currency_rates(self, nbp_rates: List[Dict]):
        today = date.today()

        currency_rates = ExchangeRate._get_exchange_rates(
            today.year, today.month, today.day
        )

        # rates are stored in `custom/fixtures/nbp_table_a_response.json`
        # PLN is added as a fixed rate
        assert len(currency_rates.keys()) == len(nbp_rates) + 1
        assert currency_rates['PLN'] == Decimal('1.0')
        for currency in nbp_rates:
            assert currency_rates[currency['code']] == currency['mid']

    @pytest.mark.nbp
    def test_should_use_existing_data_if_object_for_date_exists(self):
        today = date.today()
        exchange_rate = ExchangeRateFactory(
            exchange_date=today, rates={'PLN': Decimal('3.0')}
        )
        assert ExchangeRate.objects.count() == 1

        currency_rates = ExchangeRate._get_exchange_rates(
            today.year, today.month, today.day
        )

        assert ExchangeRate.objects.count() == 1
        assert currency_rates == exchange_rate.rates

    @pytest.mark.nbp(mock_requests=False)
    @pytest.mark.usefixtures('mock_nbp_api_client_with_api_errors')
    def test_should_not_create_object_if_request_fails_for_today(self):
        today = date.today()

        rates = ExchangeRate._get_exchange_rates(today.year, today.month, today.day)

        assert ExchangeRate.objects.count() == 0
        assert rates == {}

    @pytest.mark.nbp(mock_requests=False)
    @pytest.mark.usefixtures('mock_nbp_api_client_with_api_errors')
    def test_get_safe_exchange_should_return_values_from_past_when_request_fails(self):
        today = date.today()
        last_known_exchange_rate = today - timedelta(days=1)
        exchange_rate = ExchangeRateFactory(
            exchange_date=last_known_exchange_rate, rates={'PLN': Decimal('3.0')}
        )

        rates = ExchangeRate.get_safe_exchange(today.year, today.month, today.day)

        assert rates == exchange_rate.rates

    @pytest.mark.nbp(mock_requests=False)
    @pytest.mark.usefixtures('mock_nbp_api_client_with_api_errors')
    def test_should_create_object_with_empty_rates_if_request_fails_for_past_date(self):
        exchange_date = date.today() - timedelta(days=1)

        rates = ExchangeRate._get_exchange_rates(
            exchange_date.year, exchange_date.month, exchange_date.day
        )

        assert ExchangeRate.objects.count() == 1
        assert rates == dict()

    @pytest.fixture
    def nbp_rates(self) -> List[Dict]:
        nbp_response_text = fake_nbp_get_exchange_table('').text
        nbp_response_json = json.loads(nbp_response_text)
        nbp_rates = nbp_response_json[0]['rates']
        return nbp_rates
