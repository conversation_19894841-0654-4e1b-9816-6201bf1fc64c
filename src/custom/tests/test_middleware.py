from unittest import mock
from unittest.mock import Mock

import pytest

from custom.enums import LanguageEnum
from custom.middleware import LocaleURLMiddleware


class TestLocaleURLMiddleware:
    @pytest.mark.parametrize(
        ('referer', 'language_code'),
        [
            (language.url_prefix, language)
            for language in LanguageEnum
            if language != LanguageEnum.EN
        ],
    )
    def test_sets_correct_language_code_to_requests(
        self,
        referer,
        language_code,
    ):
        request = Mock()
        request.META = {'HTTP_REFERER': referer}
        request.path = '/api/'
        get_response = mock.MagicMock()
        LocaleURLMiddleware(get_response).process_request(request)
        assert getattr(request, 'LANGUAGE_CODE') == language_code
