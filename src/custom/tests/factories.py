import datetime

import factory

from factory import fuzzy

from custom.internal_api.dto import (
    LogisticOrderDTO,
    ShipperDTO,
)
from custom.logistic_enums import Shipper


class ExchangeRateFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'custom.ExchangeRate'


class ShipperDTOFactory(factory.Factory):
    name = factory.Iterator([Shipper.TNT, Shipper.FEDEX])

    class Meta:
        model = ShipperDTO


class LogisticOrderDTOFactory(factory.Factory):
    id = factory.Sequence(int)
    order_type = 'jetty'
    total_brutto_weight = 100
    total_netto_weight = 100
    cost_in_pln = 0
    is_split_candidate = False
    to_be_shipped = False
    mailing_disabled = False

    delivered_date = fuzzy.FuzzyDate(
        start_date=datetime.date(2020, 8, 3),
    )

    get_packages = [{'batch_id': 19, 'product_id': '?_17', 'packages': []}]

    class Meta:
        model = LogisticOrderDTO
