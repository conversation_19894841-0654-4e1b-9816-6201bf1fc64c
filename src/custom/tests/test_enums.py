import pytest

from custom import enums
from custom.enums import Sofa01Color


class _TestingColor(enums.ColorEnum):
    RED = 1
    GREEN = 2
    BLUE = 3
    SUPER_YELLOWISH = 4
    BEST_COLOR = BLUE


class TestColorEnum:
    enum_class = _TestingColor

    def test_choices(self):
        expected_choices = [
            (self.enum_class.RED.value, 'Red'),
            (self.enum_class.GREEN.value, 'Green'),
            (self.enum_class.BLUE.value, 'Blue'),
            (self.enum_class.SUPER_YELLOWISH.value, 'Super Yellowish'),
        ]
        assert self.enum_class.choices() == expected_choices

    @pytest.mark.parametrize(
        ('text', 'expected_color'),
        [
            ('RED', enum_class.RED),
            ('Red', enum_class.RED),
            ('blue', enum_class.BLUE),
            ('best color', enum_class.BLUE),
        ],
    )
    def test_get_returns_proper_member_when_there_is_member_with_given_name(
        self,
        text,
        expected_color,
    ):
        assert self.enum_class.get(text) == expected_color

    def test_get_returns_fallback_value_when_its_passed_and_there_is_no_member_with_given_name(  # noqa E501
        self,
    ):
        assert self.enum_class.get('yellow', None) is None

    def test_get_raises_KeyError_when_there_is_no_member_with_given_name_and_fallback_not_passed(  # noqa E501
        self,
    ):
        with pytest.raises(KeyError):
            self.enum_class.get('yellow')

    @pytest.mark.parametrize(
        ('color', 'capacity_key'),
        [
            (enum_class.BLUE, 'capacity_color_3'),
            (enum_class.BEST_COLOR, 'capacity_color_3'),
        ],
    )
    def test_capacity_key(self, color, capacity_key):
        assert color.capacity_key == capacity_key

    @pytest.mark.parametrize(
        ('color', 'slug'),
        [
            (enum_class.BLUE, 'color_3'),
            (enum_class.BEST_COLOR, 'color_3'),
        ],
    )
    def test_slug(self, color, slug):
        assert color.slug == slug

    @pytest.mark.parametrize(
        ('color', 'display_name'),
        [
            (enum_class.RED, 'Red'),
            (enum_class.BEST_COLOR, 'Blue'),
            (enum_class.SUPER_YELLOWISH, 'Super Yellowish'),
        ],
    )
    def test_display_name(self, color, display_name):
        assert color.display_name == display_name

    def test_hidden_colors(self):
        multicolor_value = Sofa01Color.MULTICOLOR.value
        multicolor_name = Sofa01Color.MULTICOLOR.name

        # can be called explicitly
        assert Sofa01Color.MULTICOLOR
        assert multicolor_value
        assert multicolor_name

        # not present in choices, values etc.
        assert (multicolor_value, multicolor_name) not in Sofa01Color.choices()
        assert (multicolor_value, multicolor_name) not in Sofa01Color.choices_active()
        assert (
            multicolor_value,
            multicolor_name,
        ) not in Sofa01Color.get_active_colors()
        assert multicolor_value not in Sofa01Color.values()
