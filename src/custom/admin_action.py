from django.contrib.admin.helpers import ACTION_CHECKBOX_NAME
from django.shortcuts import render


def admin_action_with_form(
    modeladmin,
    request,
    queryset,
    form_class,
    success_function,
    success_function_kwargs,
    form_initial=None,
    instance=None,
    show_queryset=False,
    template_name='admin/admin_form.html',
    extra_context=None,
):
    opts = modeladmin.model._meta
    app_label = opts.app_label
    action = request.POST.get('action')
    form = None

    if 'apply' in request.POST:
        if instance:
            form = form_class(request.POST, request.FILES, instance=instance)
        else:
            form = form_class(request.POST, request.FILES)
        if form.is_valid():
            return success_function(
                modeladmin, request, queryset, form=form, **success_function_kwargs
            )
    if not form:
        _selected_action = request.POST.getlist(ACTION_CHECKBOX_NAME)
        initial = {'_selected_action': _selected_action}
        if form_initial:
            initial.update(form_initial)
        form = form_class(initial=initial)

    context = {
        'form': form,
        'opts': opts,
        'queryset': queryset,
        'show_queryset': show_queryset,
        'app_label': app_label,
        'action': action,
        'media': modeladmin.media,
    }
    if extra_context:
        context.update(extra_context)
    return render(request, template_name, context)
