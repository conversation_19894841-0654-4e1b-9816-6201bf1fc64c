from django.urls import path

from custom.views import (
    ForceCloudflarePurgeView,
    GoogleRecaptcha,
    MailchimpUnsubscribeHandle,
    set_language,
    switch_ab_test,
)

urlpatterns = [
    path(
        'api/v1/mailchimp/unsubscribe/',
        MailchimpUnsubscribeHandle.as_view(),
        name='mailchimp_unsubscribe_handle',
    ),
    path(
        'api/v1/recaptcha/',
        GoogleRecaptcha.as_view(),
        name='google-recaptcha-verify',
    ),
    path('i18n/setlang/', set_language, name='set_language'),
    path('switch_abtest/', switch_ab_test, name='switch_abtest'),
    path(
        'purge-cloudflare-cache/',
        ForceCloudflarePurgeView.as_view(),
        name='force-cloudflare-purge',
    ),
]
