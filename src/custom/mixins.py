from django.http import HttpResponse
from django.middleware.gzip import GZipMiddleware

from rest_framework import status


class GZipMixin:
    """Compress the response.
    For security reasons, use it only with views that allow only safe http methods.
    """

    def dispatch(self, request, *args, **kwargs) -> HttpResponse:
        response = super().dispatch(request, *args, **kwargs)
        if response.status_code == status.HTTP_200_OK:
            # before compressing, content needs to be rendered
            rendered_response = response.render()
            return GZipMiddleware(self).process_response(request, rendered_response)
        return response
