import base64
import re
import time
import urllib.parse
import urllib.request

from io import BytesIO

from django.conf import settings
from django.template.loader import render_to_string
from django.utils.encoding import force_str

import pychrome

from cstm_be.media_storage import private_media_storage
from custom.utils.temporary_files import get_temp_file_name


class ChromeHeadless(object):
    def __init__(self, chrome_url=''):
        self.url = chrome_url or settings.GOOGLE_CHROME_URL

    def make_absolute_paths(self, content):
        """Stolen method from wkhtmltopdf"""
        overrides = [
            settings.MEDIA_URL,
            settings.STATIC_URL,
        ]
        has_scheme = re.compile(r'^[^:/]+://')

        for url in overrides:
            if not url or has_scheme.match(url):
                continue

            occur_pattern = '''["|']({0}.*?)["|']'''
            occurrences = re.findall(occur_pattern.format(url), content)
            occurrences = list(set(occurrences))  # Remove dups
            for occur in occurrences:
                content = content.replace(
                    occur,
                    urllib.parse.urljoin(
                        settings.SITE_URL,
                        occur,
                    ),
                )

        return content

    def get_pdf_from_template(
        self,
        template,
        template_context,
        paper_width=None,
        paper_height=None,
        margins=False,
    ):
        html_body = self.make_absolute_paths(
            render_to_string(template, context=template_context),
        )
        encoded_html_body = force_str(html_body).encode()
        html_filename = get_temp_file_name(prefix='CHROME_', suffix='.html')
        html_filename = private_media_storage.save(
            html_filename, BytesIO(encoded_html_body)
        )

        try:
            return self.get_pdf_from_url(
                url=private_media_storage.url(html_filename),
                paper_width=paper_width,
                paper_height=paper_height,
                margins=margins,
            )
        finally:
            private_media_storage.delete(html_filename)

    def get_pdf_from_url(
        self,
        url,
        custom_eveluates=None,
        paper_width=None,
        paper_height=None,
        margins=False,
        custom_evaluate_sleep=0,
        set_user_agent=False,
    ):
        class WaitingHandler(object):
            def __init__(self):
                self.is_loaded = False

            def dom_content_event_fired(self, **kwargs):
                self.is_loaded = True

        # create a browser instance
        browser = pychrome.Browser(url=self.url)
        # create a tab
        tab = browser.new_tab()
        # start the tab
        tab.start()
        if set_user_agent:
            MAC_OS_USER_AGENT = (
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36'
                ' (KHTML, like Gecko) Chrome/94.0.4600.0 Safari/537.36'
            )
            tab.Network.setUserAgentOverride(
                userAgent=MAC_OS_USER_AGENT,
                acceptLanguage='pl-Pl',
                platform='macOS',
            )
        tab.Network.enable()

        # wait for loading
        tab.Page.enable()
        tab.Page.navigate(url=url, _timeout=20)

        wh = WaitingHandler()
        tab.set_listener(
            'Page.domContentEventFired',
            wh.dom_content_event_fired,
        )

        # sometimes page loads to fast and dom ready is fired before
        # we create listener.
        # TODO: we should find different method to check if page is ready
        # as a temp solution we break using counter after 1 minute
        counter = 0
        while wh.is_loaded is False:
            time.sleep(3)
            counter += 1
            if counter > 20:  # 4 is bigger then tab timeout
                break

        if custom_eveluates:
            for e in custom_eveluates:
                time.sleep(custom_evaluate_sleep)
                tab.call_method(
                    'Runtime.evaluate',
                    expression=e,
                )

        options = {}
        if paper_width is not None:
            options['paperWidth'] = paper_width * 0.0393700787
        if paper_height is not None:
            options['paperHeight'] = paper_height * 0.0393700787
        if not margins:
            options['marginTop'] = 0
            options['marginBottom'] = 0
            options['marginLeft'] = 0
            options['marginRight'] = 0
        data = tab.Page.printToPDF(**options)
        # stop the tab (stop handle events and stop recv message from chrome)
        tab.stop()
        # close tab
        browser.close_tab(tab)
        return base64.b64decode(data['data'])
