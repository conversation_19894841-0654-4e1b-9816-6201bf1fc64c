import logging
import time

from threading import local
from urllib.parse import urlparse

from django.conf import settings
from django.core.cache import cache
from django.utils import translation
from django.utils.deprecation import MiddlewareMixin
from django.utils.translation import get_language_from_path

from django_requestlogging.middleware import LogSetupMiddleware

from carts.services.cart_service import CartService
from custom.filters import MoreVerboseFilter
from custom.metrics import metrics_client
from vouchers.models import Voucher

logger = logging.getLogger('cstm')


class PricingExcludePathsMiddlewareMixin(MiddlewareMixin):
    """Exclude pricing paths from the middleware."""

    EXCLUDE_PATHS = [
        '/api/v1/pricing/jetty/calculate_price/',
        '/api/v1/pricing/watty/calculate_price/',
    ]


class DisableCSRF(MiddlewareMixin):
    DISABLED_PATHS = ['/api', '/pages/api', '/internal-api']

    def process_request(self, request):
        if any(request.path.startswith(path) for path in self.DISABLED_PATHS):
            setattr(request, '_dont_enforce_csrf_checks', True)


class SaveUTMInSession(MiddlewareMixin):
    def process_request(self, request):
        if not request.user.is_authenticated:
            if request.GET.get('utm_source', None) is not None:
                request.session['user_utm_source'] = request.GET.get('utm_source')
            if 'contact_ts' not in request.session:
                request.session['contact_ts'] = str(time.time())


class SaveRefererInSession(MiddlewareMixin):
    def process_request(self, request):
        if 'HTTP_REFERER' in request.META and not (
            request.user.is_authenticated or 'HTTP_REFERER' in request.session
        ):
            request.session['HTTP_REFERER'] = request.META['HTTP_REFERER']


class AdminLocaleURLMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if request.path.startswith('/admin'):
            request.LANG = getattr(
                settings, 'ADMIN_LANGUAGE_CODE', settings.LANGUAGE_CODE
            )
            translation.activate(request.LANG)
            request.LANGUAGE_CODE = request.LANG


class LocaleURLMiddleware(PricingExcludePathsMiddlewareMixin, MiddlewareMixin):
    def process_request(self, request):
        if request.path in self.EXCLUDE_PATHS:
            return

        if request.path.startswith('/api'):
            referer = request.META.get('HTTP_REFERER', '')
            referer_path = urlparse(referer).path
            language_from_path = get_language_from_path(referer_path)
            if language_from_path:
                translation.activate(language_from_path)
                request.LANGUAGE_CODE = translation.get_language()
            else:
                default_lang = settings.LANGUAGE_CODE
                translation.activate(default_lang)
                request.LANGUAGE_CODE = default_lang


class PromoRegisterMiddleware(MiddlewareMixin):
    def process_request(self, request):
        if 'promo_code' in request.GET:
            promo_code = request.GET['promo_code']
            if request.user.is_authenticated:
                cart = CartService.get_cart(request.user)
                if not cart.used_promo:
                    try:
                        voucher = Voucher.objects.get(
                            active=True, code=promo_code, quantity_left__gt=0
                        )
                        CartService(cart=cart).add_voucher(voucher)

                    except Voucher.DoesNotExist:
                        logger.error('Given voucher does not exist <- %s' % promo_code)
            else:
                request.session['promo_code'] = promo_code


class MarkForMinificationMiddleware(object):
    MINIFIED_URLS = ['/de/', '/en/', '/product/', '/shelf/', '/table/']

    def __init__(self, get_response=None):
        self.get_response = get_response

    def __call__(self, request):
        self.process_request(request)
        response = self.get_response(request)
        return response

    def process_request(self, request):
        minify = False
        if request.path == '/':
            minify = True
        if not minify:
            for minified_url in MarkForMinificationMiddleware.MINIFIED_URLS:
                if minified_url in request.path or request.path == minified_url:
                    minify = True
                    break
        if minify:
            request._hit_htmlmin = True


class LogRequestInfoMiddleware(LogSetupMiddleware):
    """look at django_requestlogging middleware and filter"""

    def process_request(self, request):
        request.logging_filter = MoreVerboseFilter(request)
        self.add_filter(request.logging_filter)


_thread_locals = local()


def get_current_request():
    """returns the request object for this thread"""
    return getattr(_thread_locals, 'request', None)


def get_current_user():
    """returns the current user, if exist, otherwise returns None"""
    request = get_current_request()
    if request:
        return getattr(request, 'user', None)


class ThreadLocalMiddleware(MiddlewareMixin):
    """Simple middleware that adds the request object in thread local storage."""

    def process_request(self, request):
        _thread_locals.request = request

    def process_response(self, request, response):
        if hasattr(_thread_locals, 'request'):
            del _thread_locals.request
        return response

    def process_exception(self, request, exception):
        if hasattr(_thread_locals, 'request'):
            del _thread_locals.request


class ViewMetricsMiddleware(PricingExcludePathsMiddlewareMixin, MiddlewareMixin):
    def process_view(self, request, view_func, view_args, view_kwargs):
        start = time.perf_counter()
        response = view_func(request, *view_args, **view_kwargs)
        end = time.perf_counter()
        if request.path in self.EXCLUDE_PATHS:
            return response

        view_name = self._get_view_name(view_func)

        # self._send_metrics_to_datadog(
        #     end - start,
        #     view_name,
        #     request.path,
        # )

        self._update_view_usage_counter(view_name)
        return response

    def _get_view_name(self, view_func):
        if hasattr(view_func, 'view_class'):
            view_name = view_func.view_class.__name__
        else:
            view_name = view_func.__qualname__
        if hasattr(view_func, 'model_admin'):
            model_name = str(view_func.model_admin)
            view_name = f'{view_name}:{model_name}'
        return view_name

    def _send_metrics_to_datadog(self, duration, view_name, request_path):
        try:
            metrics_client().timing(
                'django_view_timer',
                duration,
                tags=[
                    f'view:{view_name}',
                    f'path:{request_path}',
                ],
            )
        except Exception:
            pass

    def _update_view_usage_counter(self, view_name):
        view_name = f'django_view_counter:{view_name}'
        try:
            cache.incr(view_name, delta=1)
        except ValueError:
            cache.set(view_name, 1, timeout=None)
