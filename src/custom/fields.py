import math

from rest_framework import serializers


class CurrencyNumberField(serializers.DecimalField):
    def __init__(
        self,
        decimal_places=2,
        max_digits=12,
        coerce_to_string=False,
        **kwargs,
    ):
        super().__init__(
            decimal_places=decimal_places,
            max_digits=max_digits,
            coerce_to_string=coerce_to_string,
            **kwargs,
        )


class AttributeNotNullBooleanField(serializers.BooleanField):
    def get_attribute(self, instance):
        return instance

    def to_representation(self, instance):
        value = getattr(instance, self.source, None)
        return bool(value)


class ShortTimeField(serializers.TimeField):
    def to_representation(self, value):
        if value is None:
            return None
        return value.strftime('%H:%M')


class MillimeterToCentimeterField(serializers.IntegerField):
    def to_representation(self, value):
        """Convert from mm (stored in DB) to cm and round up"""
        return int(math.ceil(value / 10.0))
