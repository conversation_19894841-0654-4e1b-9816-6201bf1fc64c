import json
import logging

from typing import (
    Any,
    Optional,
)
from urllib.parse import urljoin

from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder

import requests

from events.braze.objects import (
    BrazeEventObject,
    BrazePurchaseObject,
    BrazeRecipientObject,
    BrazeSubscriptionObject,
    BrazeUserAttributesObject,
)
from events.choices import (
    BrazeSubscriptionGroupStatus,
    BrazeSubscriptionTypes,
)

logger = logging.getLogger('cstm')


class BrazeClient:
    BRAZE_REQUEST_TIMEOUT = 15

    @classmethod
    def send_user_tracking_data(
        cls,
        attribute_objects: list[BrazeUserAttributesObject],
        event_objects: list[BrazeEventObject],
        purchase_objects: list[BrazePurchaseObject],
    ) -> list[int]:
        """
        Basic endpoint for sending user related events

        Sends CustomEvents, UserAttributeEvents, PurchaseEvents to Braze.
        Creates a new user profile if needed.
        """
        url = urljoin(settings.BRAZE_API_URL, 'users/track')
        data = {
            'events': [braze_object.to_dict() for braze_object in event_objects],
            'attributes': [
                braze_object.to_dict() for braze_object in attribute_objects
            ],
            'purchases': [braze_object.to_dict() for braze_object in purchase_objects],
        }
        response = cls._send_request(url, data)
        response_data = response.json()

        return cls._get_user_track_failed_events_ids(
            response_data,
            event_objects,
            attribute_objects,
            purchase_objects,
        )

    @classmethod
    def update_subscriptions(
        cls,
        subscription_group: str,
        subscription_state: str,
        subscriptions_objects: list[BrazeSubscriptionObject],
    ) -> None:
        """
        Endpoint for subscribing users to appropriate subscription groups in Braze.
        """
        url = urljoin(settings.BRAZE_API_URL, 'subscription/status/set')
        subscription_type = (
            BrazeSubscriptionTypes.EMAIL
            if subscription_group in settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS.values()
            else BrazeSubscriptionTypes.PHONE
        )
        data = {
            'subscription_group_id': subscription_group,
            'subscription_state': subscription_state,
            subscription_type: [
                subscription.get_subscription_value(subscription_type)
                for subscription in subscriptions_objects
            ],
        }
        cls._send_request(url, data)

    @classmethod
    def trigger_canvas(
        cls,
        canvas_id: str,
        braze_objects: list[BrazeRecipientObject],
    ) -> None:
        url = urljoin(settings.BRAZE_API_URL, '/canvas/trigger/send')
        data = {
            'canvas_id': canvas_id,
            'recipients': [braze_object.recipient for braze_object in braze_objects],
        }
        cls._send_request(url, data)

    @classmethod
    def get_email_subscription_group_status(
        cls,
        subscription_group_id: str,
        email: str,
    ) -> BrazeSubscriptionGroupStatus:
        if not email:
            return BrazeSubscriptionGroupStatus.UNKNOWN

        return cls._get_subscription_group_status(
            subscription_group_id,
            BrazeSubscriptionTypes.EMAIL,
            email,
        )

    @classmethod
    def get_sms_subscription_group_status(
        cls,
        subscription_group_id: str,
        phone: str,
    ) -> BrazeSubscriptionGroupStatus:
        phone = phone.replace('+', '')
        return cls._get_subscription_group_status(
            subscription_group_id,
            BrazeSubscriptionTypes.PHONE,
            phone,
        )

    @classmethod
    def _send_request(
        cls,
        url: str,
        data: Optional[dict[str, Any]] = None,
        params: Optional[dict[str, Any]] = None,
        http_method: str = 'POST',
    ) -> requests.Response:
        response = requests.request(
            method=http_method,
            url=url,
            data=json.dumps(data, cls=DjangoJSONEncoder),
            params=params,
            headers={
                'Authorization': f'Bearer {settings.BRAZE_API_KEY}',
                'Content-Type': 'application/json',
                'X-Braze-Bulk': 'true',
            },
            timeout=cls.BRAZE_REQUEST_TIMEOUT,
        )
        response.raise_for_status()

        return response

    @staticmethod
    def _get_user_track_failed_events_ids(
        response_data: dict,
        event_objects: list[BrazeEventObject],
        attribute_objects: list[BrazeUserAttributesObject],
        purchase_objects: list[BrazePurchaseObject],
    ) -> list[int]:
        """Map failed objects returned by Braze to events ids"""
        braze_objects = {
            'events': event_objects,
            'attributes': attribute_objects,
            'purchases': purchase_objects,
        }
        failed_events_ids = []
        for error in response_data.get('errors', []):
            braze_object = braze_objects[error['input_array']][error['index']]
            failed_events_ids.append(braze_object.event_id)

        return failed_events_ids

    @classmethod
    def _get_subscription_group_status(
        cls,
        subscription_group_id: str,
        subscription_method: BrazeSubscriptionTypes,
        value: str,
    ) -> BrazeSubscriptionGroupStatus:
        """Check if given email/phone is subscribed to a given subscription group."""
        if not settings.BRAZE_API_KEY:
            return BrazeSubscriptionGroupStatus.UNKNOWN

        url = urljoin(settings.BRAZE_API_URL, 'subscription/status/get')
        params = {
            'subscription_group_id': subscription_group_id,
            subscription_method: value,
        }
        response = cls._send_request(url, params=params, http_method='GET')
        subscription_status = response.json().get('status', {}).get(value, None)
        return BrazeSubscriptionGroupStatus(subscription_status)
