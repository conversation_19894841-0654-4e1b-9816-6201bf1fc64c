import datetime
import json
import logging

from dataclasses import dataclass
from decimal import Decimal
from json import JSONDecodeError
from typing import (
    Any,
    Dict,
    Final,
    List,
    Optional,
)

import requests

from rest_framework import status

from custom.constants import POLISH_HOLIDAYS

SUPPORTED_TABLE_TYPES: Final = frozenset(('A', 'B', 'C'))

logger = logging.getLogger('cstm')


@dataclass
class NbpCurrencyRate:
    currency: str
    code: str
    mid: Decimal


@dataclass
class NbpDailyRates:
    effective_date: datetime.date
    currency_rates: List[NbpCurrencyRate]


@dataclass
class NbpExchangeTable:
    daily_rates: List[NbpDailyRates]


class NBPApiClientException(Exception):
    def __init__(self, message: str) -> None:
        self.message = message


class UnsupportedNBPTableType(NBPApiClientException):
    _supported_table_types_slug: str = ', '.join(
        '"{0}"'.format(type_) for type_ in sorted(SUPPORTED_TABLE_TYPES)
    )

    def __init__(self, table_type: str) -> None:
        self.table_type = table_type
        self.message = (
            '"{0}" table type is not supported by NBP API. Use one of '
            + 'following values as table type: {1}'
        ).format(
            table_type,
            self._supported_table_types_slug,
        )


class NBPAPIClient:
    """NBP REST API client."""

    default_headers: Dict[str, str] = {'Accept': 'application/json'}
    date_format: str = '%Y-%m-%d'

    def __init__(
        self,
        *,
        base_url: str = 'http://api.nbp.pl/api/',
        timeout: int = 5,
        verify_ssl: bool = True,
    ) -> None:
        self.base_url = '{0}/'.format(base_url.strip('/'))
        self.timeout = timeout
        self.verify_ssl = verify_ssl
        self._session = requests.Session()
        self._session.headers = self.default_headers

    def get_exchange_table(
        self,
        query_date: Optional[datetime.date] = None,
        *,
        table_type: str = 'A',
        end_date: Optional[datetime.date] = None,
        **kwargs: Any,
    ) -> NbpExchangeTable:
        """Request exchange table for given input data.

        Implements requsts described in list items 1, 4 and 5 from NBP API
        section "Zapytania o kompletne tabele" at http://api.nbp.pl/

        ``date`` is used as `startDate` when ``end_date`` present.

        """
        table_type = table_type.upper()
        if table_type not in SUPPORTED_TABLE_TYPES:
            raise UnsupportedNBPTableType(table_type)

        path = 'exchangerates/tables/{0}/'.format(table_type)
        if query_date:
            path = '{0}{1}/'.format(path, query_date.strftime(self.date_format))

        if end_date:
            if not query_date:
                raise ValueError(
                    '`date` has to be provided when `end_date` passed',
                )
            path = '{0}{1}/'.format(path, end_date.strftime(self.date_format))

        try:
            response = self.get(path, **kwargs)
        except requests.RequestException as ex:
            raise NBPApiClientException('NBP API call error') from ex

        return self._parse_nbp_response(response, query_date or datetime.date.today())

    def get(self, path: str, **kwargs: Any) -> requests.Response:
        """Perform HTTP GET request to NBP REST API."""
        return self.request('GET', path=path, **kwargs)

    def request(
        self,
        method: str,
        path: str,
        **kwargs: Any,
    ) -> requests.Response:
        """Perform HTTP request to NBP REST API."""
        url = '{0}{1}'.format(self.base_url, path.strip('/'))
        kwargs.setdefault('timeout', self.timeout)
        kwargs.setdefault('verify', self.verify_ssl)

        return self._session.request(method=method, url=url, **kwargs)

    def _parse_nbp_response(
        self,
        req: requests.Response,
        query_date: datetime.date,
    ) -> NbpExchangeTable:
        if req.status_code == status.HTTP_200_OK:
            body: str = req.text
            try:
                currency_rates_json = json.loads(body)
                exchange_table = self._map_nbp_response(currency_rates_json)
            except JSONDecodeError as ex:
                logger.exception('NBP API response is not a valid JSON')
                raise NBPApiClientException('Response is not a valid json') from ex
            except KeyError as ex:
                logger.exception('Response schema different than expected')
                raise NBPApiClientException(
                    'Response schema different than expected'
                ) from ex

            self._throw_if_no_rates(exchange_table)
            return exchange_table
        else:
            now = datetime.datetime.now()
            hour, minute = now.hour, now.minute
            if (
                query_date.weekday() not in [6, 5]
                and not self._is_free_day(query_date)
                and (hour >= 12 and minute >= 15)
            ):
                logger.error(
                    'NBP returned response == %s for date %s and its '
                    'not a weekend/free day Piu Piu Piu',
                    req.status_code,
                    query_date,
                )

        raise NBPApiClientException('Problem with exchange rates')

    def _throw_if_no_rates(self, exchange_table: NbpExchangeTable) -> None:
        if (
            len(exchange_table.daily_rates) != 1
            or len(exchange_table.daily_rates[0].currency_rates) == 0
        ):
            raise NBPApiClientException('NBP return empty list or no rates')

    def _is_free_day(self, check_date: datetime.date) -> bool:
        return check_date in POLISH_HOLIDAYS

    def _map_nbp_response(self, response_json: List[Dict]) -> NbpExchangeTable:
        return NbpExchangeTable(
            daily_rates=[
                self._map_nbp_exchange_entry(entry_dict) for entry_dict in response_json
            ]
        )

    def _map_nbp_exchange_entry(self, entry_dict: Dict) -> NbpDailyRates:
        currency_rates = [
            NbpCurrencyRate(
                currency_dict.get('currency'),
                currency_dict.get('code'),
                currency_dict.get('mid'),
            )
            for currency_dict in entry_dict.get('rates')
        ]
        return NbpDailyRates(
            effective_date=entry_dict.get('effectiveDate'),
            currency_rates=currency_rates,
        )
