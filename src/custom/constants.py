import datetime
import os.path
import re

from typing import (
    Dict,
    Final,
)

from django.conf import settings

import geoip2.database
import holidays

from geoip2.errors import AddressNotFoundError

from custom.utils.mixins import Singleton

# VAT

VAT_NORMAL = 0
VAT_EU = 1  # vat_type 0% WDT sent do european countries B2B
VAT_SWITZERLAND = 2  # vat_type never set on delivered orders
VAT_GERMANY = 3  # not used except internal orders by mistake
VAT_EXPORT = 4  # user for all countries outside eu

VAT_CHOICES = (
    ('__default', 0.23),
    (VAT_NORMAL, 0.23),
    (VAT_EU, 0),
    (VAT_GERMANY, 0.19),
    (VAT_EXPORT, 0),
    (VAT_SWITZERLAND, 0),
)

VAT_FACTORS = dict(VAT_CHOICES)

# FEATURES

FEATURE_WITH_DOORS = 1
FEATURE_WITH_DRAWERS = 2
FEATURE_EXTENDED_WIDTH = 3

FEATURE_CHOICE = (
    (FEATURE_WITH_DOORS, 'doors'),
    (FEATURE_WITH_DRAWERS, 'drawers'),
    (FEATURE_EXTENDED_WIDTH, 'extended width'),
)

# COLOR
# TODO probably unused

COLOR_WHITE = 1
COLOR_BLACK = 2
COLOR_GRAY = 4
COLOR_OBERGINE = 5
COLOR_NATURAL = 6

COLOR_CHOICE = (
    (COLOR_WHITE, 'white'),
    (COLOR_BLACK, 'black'),
    (COLOR_GRAY, 'gray'),
    (COLOR_OBERGINE, 'obergine'),
    (COLOR_NATURAL, 'natural/fornir'),
)

# EMAIL

simple_email_re = re.compile(r'^\S+@\S+\.\S+$')

# GEO_IP


class GeoIPTylko(object, metaclass=Singleton):
    def __init__(self):
        self._reader = geoip2.database.Reader(
            os.path.join(settings.ROOT_PATH, 'GeoLite2-Country.mmdb')
        )

    def continent_name_by_addr(self, addr):
        try:
            response = self._reader.country(addr)
            if response.country.name in [
                'Albania',
                'Andorra',
                'Belarus',
                'Bosnia and Herzegovina',
                'Cyprus',
                'Iceland',
                'Isle of Man',
                'Jersey',
                'Kosovo',
                'Liechtenstein',
                'Macedonia',
                'Malta',
                'Monaco',
                'Montenegro',
                'Republic of Moldova',
                'Russia',
                'Serbia',
                'Ukraine',
            ]:
                continent_code = '--'
            else:
                continent_code = response.continent.code
        except AddressNotFoundError:
            continent_code = '--'
        return continent_code

    def country_code_by_addr(self, addr):
        try:
            response = self._reader.country(addr)
            return response.country.iso_code
        except AddressNotFoundError:
            return None

    def country_name_by_addr(self, addr):
        try:
            response = self._reader.country(addr)
            country_name = response.country.name
            if country_name == 'Czechia':
                country_name = 'Czech'
            if country_name == 'Republic of Lithuania':
                country_name = 'Lithuania'
        except AddressNotFoundError:
            country_name = None
        return country_name


GEOIP_OBJECT = GeoIPTylko()

POLISH_HOLIDAYS: Dict[datetime.date, str] = holidays.Poland()
QUARTER: Final[int] = 60 * 15
