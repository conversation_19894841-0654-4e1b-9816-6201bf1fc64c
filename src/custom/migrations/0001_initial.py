# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.utils.timezone

from django.db import (
    migrations,
    models,
)

import jsonfield.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='APILock',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('version', models.CharField(max_length=8)),
                ('platform', models.CharField(max_length=16)),
                ('lock_header_key', models.CharField(max_length=512)),
                ('lock_description_key', models.CharField(max_length=512)),
                ('lock_button_key', models.Char<PERSON>ield(max_length=512)),
            ],
        ),
        migrations.CreateModel(
            name='ApplicationConfiguration',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('settings', django.contrib.postgres.fields.jsonb.JSONField()),
                ('active', models.BooleanField(default=False)),
                (
                    'created_at',
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='Created at',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Application Configuration',
                'ordering': ('created_at',),
            },
        ),
        migrations.CreateModel(
            name='ExchangeRate',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('exchange_date', models.DateField(unique=True)),
                ('rates', jsonfield.fields.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('set_with', models.TextField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='FrontRemoteLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('version', models.TextField(blank=True, null=True)),
                ('event', models.TextField(blank=True, null=True)),
                ('content', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'verbose_name': 'Remote logs',
            },
        ),
        migrations.CreateModel(
            name='PriceConfiguration',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('active', models.BooleanField(default=False)),
                ('factor_meters', models.FloatField(default=110.5)),
                ('factor_meters_solid', models.FloatField(default=159)),
                ('factor_meters_black', models.FloatField(default=110.5)),
                ('factor_horizontals', models.FloatField(default=1)),
                ('factor_verticals', models.FloatField(default=1)),
                ('factor_supports', models.FloatField(default=1)),
                ('factor_margin', models.FloatField(default=4)),
                (
                    'factor_oiling',
                    models.FloatField(default=1, verbose_name='olejowanie'),
                ),
                (
                    'factor_varnishing',
                    models.FloatField(default=1, verbose_name='lakierowanie'),
                ),
                ('factor_euro', models.FloatField(default=4, verbose_name='euro')),
                (
                    'factor_material_mass',
                    models.FloatField(default=13.5, verbose_name='Material mass'),
                ),
                ('factor_mass_per_package', models.FloatField(default=70)),
                ('factor_ivy_phantom_a', models.FloatField(default=1)),
                ('factor_ivy_phantom_b', models.FloatField(default=0)),
                ('factor_grinder_price_brutto', models.FloatField(default=49)),
                ('factor_grinder_deliver_poland', models.FloatField(default=8.34)),
                ('factor_grinder_deliver_germany', models.FloatField(default=12.78)),
                ('factor_grinder_deliver_austria', models.FloatField(default=12.78)),
                ('factor_grinder_deliver_uk', models.FloatField(default=15.35)),
                ('factor_table_start_wood_wood', models.FloatField(default=805)),
                ('factor_table_end_wood_wood', models.FloatField(default=1815)),
                ('factor_table_start_wood_color', models.FloatField(default=795)),
                ('factor_table_end_wood_color', models.FloatField(default=1760)),
                ('factor_table_start_color_color', models.FloatField(default=840)),
                ('factor_table_end_color_color', models.FloatField(default=2020)),
                ('factor_table_start_color_wood', models.FloatField(default=850)),
                ('factor_table_end_color_wood', models.FloatField(default=2075)),
                (
                    'factor_price_per_kg_factor_pl',
                    models.FloatField(
                        default=0.90525,
                        verbose_name='price factor for kg in pln, divided by Euro rate',
                    ),
                ),
                (
                    'factor_price_per_kg_const_pl',
                    models.FloatField(
                        default=223.65,
                        verbose_name='price const for kg in pln, divided by Euro rate',
                    ),
                ),
                ('factor_price_per_kg_factor_de', models.FloatField(default=0.4)),
                ('factor_price_per_kg_const_de', models.FloatField(default=59)),
                ('factor_price_per_kg_factor_au', models.FloatField(default=0.65)),
                ('factor_price_per_kg_const_au', models.FloatField(default=82)),
                ('factor_price_per_kg_0_factor_uk', models.FloatField(default=1.24)),
                ('factor_price_per_kg_0_const_uk', models.FloatField(default=46)),
                ('factor_price_per_kg_71_factor_uk', models.FloatField(default=2.2)),
                ('factor_price_per_kg_71_const_uk', models.FloatField(default=-13)),
                ('factor_price_per_kg_100_factor_uk', models.FloatField(default=1.788)),
                ('factor_price_per_kg_100_const_uk', models.FloatField(default=0)),
                ('factor_minutes_per_horizontal', models.FloatField(default=1)),
                ('factor_minutes_per_vertical', models.FloatField(default=1)),
                ('factor_minutes_per_support', models.FloatField(default=1)),
                ('factor_assembly_per_minute_pl', models.FloatField(default=16.9)),
                ('factor_assembly_per_minute_de', models.FloatField(default=16.9)),
                ('factor_assembly_per_minute_au', models.FloatField(default=16.9)),
                ('factor_assembly_minute_window', models.FloatField(default=10)),
                ('exchange_gbp', models.FloatField(default=0.77)),
                ('exchange_chf', models.FloatField(default=1.1)),
                (
                    'live_payment_switch',
                    models.BooleanField(
                        default=False, verbose_name='Checked = live adyen env'
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='Created at',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Price Configuration',
                'ordering': ('created_at',),
            },
        ),
        migrations.AlterUniqueTogether(
            name='apilock',
            unique_together=set([('version', 'platform')]),
        ),
    ]
