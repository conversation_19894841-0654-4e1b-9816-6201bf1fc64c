# Generated by Django 3.2.16 on 2022-12-23 10:59

import django.core.files.storage
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('custom', '0009_globalsettings_is_deployment_in_progress'),
    ]

    operations = [
        migrations.CreateModel(
            name='DocumentRequest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('FAILURE', 'FAILURE'),
                            ('PENDING', 'PENDING'),
                            ('RECEIVED', 'RECEIVED'),
                            ('RETRY', 'RETRY'),
                            ('REVOKED', 'REVOKED'),
                            ('STARTED', 'STARTED'),
                            ('SUCCESS', 'SUCCESS'),
                        ],
                        default='PENDING',
                        max_length=50,
                        verbose_name='Task State',
                    ),
                ),
                (
                    'file',
                    models.FileField(
                        blank=True,
                        max_length=150,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='document_requests/',
                    ),
                ),
                ('action_name', models.CharField(blank=True, max_length=150)),
                ('exception_meta', models.JSONField(blank=True, default=dict)),
                (
                    'requested_by',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
    ]
