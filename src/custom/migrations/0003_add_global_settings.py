# Generated by Django 1.11.24 on 2020-04-30 13:30
from __future__ import unicode_literals

from django.db import (
    migrations,
    models,
)


def create_global_settings(apps, schema_editor):
    GlobalSettings = apps.get_model('custom', 'GlobalSettings')
    GlobalSettings.objects.create(
        is_live_payment=True,
        this_month_target=1950,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('custom', '0002_added_target_to_custom_model'),
    ]

    operations = [
        migrations.CreateModel(
            name='GlobalSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('is_live_payment', models.BooleanField(default=False)),
                (
                    'this_month_target',
                    models.IntegerField(
                        default=1800, verbose_name='Actual month target in k euros'
                    ),
                ),
                (
                    'exchange_pln',
                    models.DecimalField(decimal_places=2, default=4.3, max_digits=4),
                ),
                (
                    'exchange_gbp',
                    models.DecimalField(decimal_places=2, default=0.77, max_digits=4),
                ),
                (
                    'exchange_chf',
                    models.DecimalField(decimal_places=2, default=1.1, max_digits=4),
                ),
                (
                    'shipping_cost_kg',
                    models.DecimalField(decimal_places=2, default=3.66, max_digits=4),
                ),
            ],
            options={
                'verbose_name_plural': 'Global Settings',
            },
        ),
        migrations.DeleteModel(
            name='PriceConfiguration',
        ),
        migrations.RunPython(
            create_global_settings,
            migrations.RunPython.noop,
            elidable=True,
        ),
    ]
