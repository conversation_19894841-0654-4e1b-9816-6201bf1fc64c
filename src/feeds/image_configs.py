from django.db import models

from feeds.enums import (
    CameraSettings,
    DoorOpening,
    FeedImageType,
)
from render_tasks.enums import InteriorType


class ImageConfigOption(models.IntegerChoices):
    BLENDER_FRONT_DOOR_CLOSED = 101, '<PERSON>lender, front, door closed'
    BLENDER_FRONT_DOOR_HALF_OPEN = 102, '<PERSON>len<PERSON>, front, door half open'
    BLENDER_FRONT_DOOR_OPEN = 103, '<PERSON>len<PERSON>, front, door open'

    BLENDER_LEFT30_DOOR_CLOSED = 104, 'Blen<PERSON>, left 30, door closed'
    BLENDER_LEFT30_DOOR_HALF_OPEN = 105, '<PERSON>len<PERSON>, left 30, door half open'
    BLENDER_LEFT30_DOOR_OPEN = 106, 'Blen<PERSON>, left 30, door open'

    BLENDER_RIGHT30_DOOR_CLOSED = 107, 'Blender, right 30, door closed'
    BLENDER_RIGHT30_DOOR_HALF_OPEN = 108, '<PERSON><PERSON><PERSON>, right 30, door half open'
    <PERSON><PERSON><PERSON>ER_RIGHT30_DOOR_OPEN = 109, '<PERSON>len<PERSON>, right 30, door open'

    WEBGL_FRONT = 201, 'WebGL front'
    WEBGL_LEFT30 = 204, 'WebGL left'
    WEBGL_RIGHT30 = 207, 'WebGL right'

    UNREAL_FRONT_STUDIO = 301, 'Unreal front studio'
    UNREAL_LEFT30_STUDIO = 302, 'Unreal left studio'
    UNREAL_SCENE = 303, 'Unreal scene'

    REAL_PHOTO = 401, 'Real photo'

    @property
    def camera_setting(self):
        if 'left' in self.label:
            return CameraSettings.LEFT_30
        elif 'right' in self.label:
            return CameraSettings.RIGHT_30
        elif 'front' in self.label:
            return CameraSettings.FRONT
        raise ValueError(f'Unknown camera setting for {self.label}')

    @property
    def door_opening(self):
        if 'half' in self.label:
            return DoorOpening.LEFT_HALF_OPEN
        elif 'open' in self.label:
            return DoorOpening.ALL_OPEN
        elif 'closed' in self.label:
            return DoorOpening.ALL_CLOSED
        raise ValueError(f'Unknown door opening for {self.label}')

    @property
    def image_type(self):
        if 'Blender' in self.label:
            return FeedImageType.BLENDER
        elif 'WebGL' in self.label:
            return FeedImageType.WEBGL
        elif 'Unreal' in self.label:
            return FeedImageType.UNREAL
        elif 'Real photo' in self.label:
            return FeedImageType.REAL_PHOTO
        raise ValueError(f'Unknown image type for {self.label}')

    @property
    def extended_image_type(self) -> str:
        if self == self.UNREAL_SCENE:
            return 'unreal_scene'
        return self.image_type.value

    @property
    def unreal_interior(self) -> InteriorType:
        if 'studio' in self.label:
            return InteriorType.STUDIO
        elif 'scene' in self.label:
            return InteriorType.SCENE
        raise ValueError(f'Unknown interior for {self.label}')

    @property
    def is_real_photo(self) -> bool:
        return self == self.REAL_PHOTO

    @property
    def is_blender(self) -> bool:
        return self.image_type == FeedImageType.BLENDER

    @property
    def is_webgl(self) -> bool:
        return self.image_type == FeedImageType.WEBGL

    @property
    def is_unreal(self) -> bool:
        return self.image_type == FeedImageType.UNREAL

    @classmethod
    def webgl_configs(cls) -> list['ImageConfigOption']:
        return [
            cls.WEBGL_FRONT,
            cls.WEBGL_LEFT30,
            cls.WEBGL_RIGHT30,
        ]

    @classmethod
    def unreal_configs(cls) -> list[int]:
        return [
            cls.UNREAL_FRONT_STUDIO,
            cls.UNREAL_LEFT30_STUDIO,
            cls.UNREAL_SCENE,
        ]

    @classmethod
    def webgl_config_choices(cls) -> list[tuple[int, str]]:
        return [(config.value, config.name) for config in cls.webgl_configs()]

    @classmethod
    def blender_configs(cls) -> list['ImageConfigOption']:
        return [
            cls.BLENDER_FRONT_DOOR_CLOSED,
            cls.BLENDER_FRONT_DOOR_HALF_OPEN,
            cls.BLENDER_FRONT_DOOR_OPEN,
            cls.BLENDER_LEFT30_DOOR_CLOSED,
            cls.BLENDER_LEFT30_DOOR_HALF_OPEN,
            cls.BLENDER_LEFT30_DOOR_OPEN,
            cls.BLENDER_RIGHT30_DOOR_CLOSED,
            cls.BLENDER_RIGHT30_DOOR_HALF_OPEN,
            cls.BLENDER_RIGHT30_DOOR_OPEN,
        ]

    @classmethod
    def image_configs(cls, image_type: FeedImageType) -> list[int]:
        if image_type == FeedImageType.BLENDER:
            return cls.blender_configs()
        elif image_type == FeedImageType.WEBGL:
            return cls.webgl_configs()
        elif image_type == FeedImageType.UNREAL:
            return cls.unreal_configs()
        raise ValueError(f'Unknown image type: {image_type}')
