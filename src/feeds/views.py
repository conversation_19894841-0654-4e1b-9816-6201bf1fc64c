from django.http import HttpResponse
from django.views.generic.base import View

from rest_framework import (
    mixins,
    status,
)
from rest_framework.generics import get_object_or_404
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView
from rest_framework.viewsets import GenericViewSet

from feeds.models import (
    Feed,
    FeedImage,
)
from feeds.serializers import FeedImageSerializer
from feeds.tasks import order_lacking_webgl_images_as_task
from gallery.permissions import WriteOnlyToNotPresetAndNotReadonly


class FeedItemImageViewSet(mixins.CreateModelMixin, GenericViewSet):
    permission_classes = api_settings.DEFAULT_PERMISSION_CLASSES + [
        WriteOnlyToNotPresetAndNotReadonly,
    ]
    queryset = FeedImage.objects.all()
    serializer_class = FeedImageSerializer

    def perform_create(self, serializer):
        serializer.save()


class FeedFileView(View):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, *args, **kwargs):
        instance = get_object_or_404(Feed, id=self.kwargs['id'])
        response = HttpResponse(
            instance.file,
            content_type='application/csv',
        )
        response['Content-Disposition'] = 'attachment; filename="{0}"'.format(
            instance.file.name,
        )
        return response


class FeedItemsRegenerationView(APIView):
    def post(self, request, *args, **kwargs):
        order_lacking_webgl_images_as_task.delay()
        return Response(status=status.HTTP_200_OK)
