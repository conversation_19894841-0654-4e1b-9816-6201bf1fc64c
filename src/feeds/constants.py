from typing import Final

PRICE_RANGES: Final[tuple[tuple[int, int, str], ...]] = (
    (0, 500, '0-500'),
    (500, 1_000, '500-1000'),
    (1_000, 1_500, '1000-1500'),
    (1_500, 2_000, '1500-2000'),
    (2_000, 2_500, '2000-2500'),
    (2_500, 3_000, '2500-3000'),
    (3_000, 3_500, '3000-3500'),
    (3_500, 4_000, '3500-4000'),
    (4_000, 4_500, '4000-4500'),
    (4_500, 5_000, '4500-5000'),
    (5_000, 5_500, '5000-5500'),
    (5_500, 6_000, '5500-6000'),
    (6_000, 6_500, '6000-6500'),
    (6_500, 7_000, '6500-7000'),
    (7_000, 7_500, '7000-7500'),
    (7_500, 8_000, '7500-8000'),
    (8_000, float('inf'), '8000+'),
)
