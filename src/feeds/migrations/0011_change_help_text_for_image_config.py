# Generated by Django 4.1.9 on 2023-09-13 21:11

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0010_alter_feedimage_image'),
    ]

    operations = [
        migrations.AlterField(
            model_name='feedimageconfig',
            name='background_color',
            field=models.CharField(
                blank=True,
                choices=[('1', 'White'), ('2', 'Gray'), ('3', 'Transparent')],
                help_text='(Only WebGl images)',
                max_length=20,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='feedimageconfig',
            name='has_items',
            field=models.BooleanField(
                default=False,
                help_text='items enabled on screenshots (Only WebGl images)',
            ),
        ),
        migrations.AlterField(
            model_name='feedimageconfig',
            name='has_zoom_enabled',
            field=models.BooleanField(
                default=False,
                help_text='enabled = larger shelves in screenshots (Only WebGl images)',
            ),
        ),
    ]
