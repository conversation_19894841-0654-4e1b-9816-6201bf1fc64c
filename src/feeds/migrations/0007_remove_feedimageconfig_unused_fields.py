# Generated by Django 4.1.9 on 2023-08-03 23:14

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0006_alter_feeditem_furniture_category_id'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='feedimageconfig',
            name='external_renderer_options',
        ),
        migrations.RemoveField(
            model_name='feedimageconfig',
            name='resolution',
        ),
        migrations.RemoveField(
            model_name='feedimageconfig',
            name='square_crop',
        ),
        migrations.RemoveField(
            model_name='feedimageconfig',
            name='uses_external_renderer',
        ),
        migrations.AddField(
            model_name='feedimageconfig',
            name='use_blender_render',
            field=models.BooleanField(
                default=False, help_text='Uses external ShelfView renderer'
            ),
        ),
    ]
