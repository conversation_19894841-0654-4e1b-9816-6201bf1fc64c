# Generated by Django 4.1.8 on 2023-07-19 21:57

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0003_create_feeds_image_models'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='feeditem',
            name='image_config',
        ),
        migrations.AddField(
            model_name='feed',
            name='image_config',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='feeds.feedimageconfig',
            ),
        ),
        migrations.AddField(
            model_name='feedcategory',
            name='image_config',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='feeds.feedimageconfig',
            ),
        ),
        migrations.AlterField(
            model_name='feeditem',
            name='category',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='items',
                to='feeds.feedcategory',
            ),
        ),
    ]
