from django.db import migrations

from feeds.image_configs import ImageConfigOption

image_config_mappings = {
    2: ImageConfigOption.WEBGL_LEFT30,
    3: ImageConfigOption.BLENDER_FRONT_DOOR_CLOSED,
    6: ImageConfigOption.REAL_PHOTO,
    7: ImageConfigOption.BLENDER_LEFT30_DOOR_HALF_OPEN,
    8: ImageConfigOption.REAL_PHOTO,
    9: ImageConfigOption.WEBGL_RIGHT30,
    10: ImageConfigOption.REAL_PHOTO,
    11: ImageConfigOption.REAL_PHOTO,
    12: ImageConfigOption.REAL_PHOTO,
    13: ImageConfigOption.REAL_PHOTO,
    14: ImageConfigOption.REAL_PHOTO,
    15: ImageConfigOption.REAL_PHOTO,
    16: ImageConfigOption.REAL_PHOTO,
    17: ImageConfigOption.REAL_PHOTO,
    18: ImageConfigOption.REAL_PHOTO,
    19: ImageConfigOption.REAL_PHOTO,
    20: ImageConfigOption.REAL_PHOTO,
    21: ImageConfigOption.REAL_PHOTO,
    22: ImageConfigOption.REAL_PHOTO,
    23: ImageConfigOption.WEBGL_LEFT30,
    24: ImageConfigOption.WEBGL_LEFT30,
    25: ImageConfigOption.WEBGL_FRONT,
    27: ImageConfigOption.REAL_PHOTO,
    28: ImageConfigOption.UNREAL_SCENE,
    29: ImageConfigOption.BLENDER_LEFT30_DOOR_CLOSED,
    30: ImageConfigOption.WEBGL_FRONT,
    31: ImageConfigOption.BLENDER_FRONT_DOOR_OPEN,
}


def reassign_image_configs(apps, schema_editor):
    FeedImageConfig = apps.get_model('feeds', 'FeedImageConfig')

    for feed_image_config in FeedImageConfig.objects.all():
        image_config = image_config_mappings.get(
            feed_image_config.id, ImageConfigOption.WEBGL_FRONT
        )

        for feed in feed_image_config.feed_set.all():
            feed.image_config_new = image_config
            feed.save(update_fields=['image_config_new'])

        for feed_category in feed_image_config.feedcategory_set.all():
            feed_category.image_config_new = image_config
            feed_category.save(update_fields=['image_config_new'])

        for feed_image in feed_image_config.images.all():
            feed_image.config_new = image_config
            feed_image.save(update_fields=['config_new'])

        feed_image_config.delete()


class Migration(migrations.Migration):
    dependencies = [
        ('feeds', '0020_feed_image_config_new_feedcategory_image_config_new_and_more'),
    ]

    operations = [
        migrations.RunPython(
            code=reassign_image_configs,
            reverse_code=migrations.RunPython.noop,
            elidable=True,
        ),
    ]
