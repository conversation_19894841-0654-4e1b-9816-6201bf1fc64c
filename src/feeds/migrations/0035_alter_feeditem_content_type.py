# Generated by Django 4.1.13 on 2025-05-12 15:14

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
        ('feeds', '0034_alter_feedcategory_furniture_category'),
    ]

    operations = [
        migrations.AlterField(
            model_name='feeditem',
            name='content_type',
            field=models.ForeignKey(
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                    models.Q(('app_label', 'gallery'), ('model', 'sotty')),
                    models.Q(('app_label', 'gallery'), ('model', 'watty')),
                    _connector='OR',
                ),
                on_delete=django.db.models.deletion.CASCADE,
                to='contenttypes.contenttype',
            ),
        ),
    ]
