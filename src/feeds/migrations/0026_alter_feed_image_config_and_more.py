# Generated by Django 4.1.9 on 2024-04-11 16:32

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0025_add_swedish_language'),
    ]

    operations = [
        migrations.AlterField(
            model_name='feed',
            name='image_config',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, '<PERSON>lender, left 30, door open'),
                    (107, '<PERSON>lender, right 30, door closed'),
                    (108, 'Blender, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front studio'),
                    (302, 'Unreal left studio'),
                    (303, 'Unreal scene'),
                    (401, 'Real photo'),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='feedcategory',
            name='image_config',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, 'Blender, left 30, door open'),
                    (107, 'Blender, right 30, door closed'),
                    (108, 'Blender, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front studio'),
                    (302, 'Unreal left studio'),
                    (303, 'Unreal scene'),
                    (401, 'Real photo'),
                ],
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='feedimage',
            name='config',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, 'Blender, left 30, door open'),
                    (107, 'Blender, right 30, door closed'),
                    (108, 'Blender, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front studio'),
                    (302, 'Unreal left studio'),
                    (303, 'Unreal scene'),
                    (401, 'Real photo'),
                ]
            ),
        ),
    ]
