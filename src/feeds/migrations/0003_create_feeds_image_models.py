# Generated by Django 4.1.8 on 2023-05-25 09:41

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0002_remove_max_length_on_file_status'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedImageConfig',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('square_crop', models.BooleanField(default=False)),
                (
                    'resolution',
                    models.CharField(
                        choices=[('800x600', 'Low'), ('1600x1200', 'High')],
                        max_length=20,
                    ),
                ),
                ('has_scene', models.BooleanField(default=False)),
                (
                    'camera_setting',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('front', 'Front'),
                            ('left_30', 'Left 30'),
                            ('right_30', 'Right 30'),
                        ],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'background_color',
                    models.CharField(
                        blank=True,
                        choices=[('1', 'White'), ('2', 'Gray'), ('3', 'Transparent')],
                        max_length=20,
                        null=True,
                    ),
                ),
                (
                    'has_zoom_enabled',
                    models.BooleanField(
                        default=False,
                        help_text='enabled = larger shelves in screenshots',
                    ),
                ),
                (
                    'has_items',
                    models.BooleanField(
                        default=False, help_text='items enabled on screenshots'
                    ),
                ),
                (
                    'uses_external_renderer',
                    models.BooleanField(
                        default=False,
                        help_text='(Experimental) Uses external ShelfView renderer',
                    ),
                ),
                (
                    'external_renderer_options',
                    models.TextField(
                        blank=True,
                        help_text='Additional setup for external renderer',
                        null=True,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='FeedImage',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('image', models.ImageField(upload_to='feeds/feed_image/%Y/%m')),
                (
                    'config',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='images',
                        to='feeds.feedimageconfig',
                    ),
                ),
                (
                    'item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='images',
                        to='feeds.feeditem',
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='feeditem',
            name='image_config',
            field=models.ManyToManyField(blank=True, to='feeds.feedimageconfig'),
        ),
    ]
