# Generated by Django 4.1.9 on 2023-09-06 14:20

from django.db import (
    migrations,
    models,
)

import taggit.managers


class Migration(migrations.Migration):

    dependencies = [
        ('taggit', '0005_auto_20220424_2025'),
        ('feeds', '0008_feedimageconfig_is_real_photo_alter_feedimage_image'),
    ]

    operations = [
        migrations.AddField(
            model_name='feedcategory',
            name='auto_furniture_sync',
            field=models.BooleanField(
                default=False,
                help_text='If True, furniture for this category will be created and '
                'synchronized automatically.',
            ),
        ),
        migrations.AddField(
            model_name='feedcategory',
            name='furniture_category',
            field=models.CharField(
                blank=True,
                choices=[
                    ('vinyl_storage', 'Vinyl Storage'),
                    ('vinylstorage', 'Vinyl Storage Fe'),
                    ('chest', 'Chest'),
                    ('shoerack', 'Shoerack'),
                    ('tvstand', 'Tv Stand'),
                    ('sideboard', 'Sideboard'),
                    ('bookcase', 'Bookcase'),
                    ('wallstorage', 'Wall Storage'),
                    ('wardrobe', 'Wardrobe'),
                    ('bedside_table', 'Bedside Table'),
                    ('desk', 'Desk'),
                ],
                help_text='Select the relevant furniture category.',
                max_length=255,
            ),
        ),
        migrations.AddField(
            model_name='feedcategory',
            name='furniture_tag',
            field=taggit.managers.TaggableManager(
                help_text='A comma-separated list of tags.',
                through='taggit.TaggedItem',
                to='taggit.Tag',
                verbose_name='Tags',
            ),
        ),
    ]
