# Generated by Django 3.2.18 on 2023-04-11 13:01

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('regions', '0006_change_chf_currency_rate'),
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='FeedCategory',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                ('is_moved_from_board', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='FeedItem',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('object_id', models.PositiveIntegerField()),
                (
                    'category',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='feeds.feedcategory',
                    ),
                ),
                (
                    'content_type',
                    models.ForeignKey(
                        limit_choices_to=models.Q(
                            models.Q(('app_label', 'gallery'), ('model', 'jetty')),
                            models.Q(('app_label', 'gallery'), ('model', 'watty')),
                            _connector='OR',
                        ),
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.contenttype',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Feed',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'language',
                    models.CharField(
                        choices=[
                            ('en', 'English'),
                            ('de', 'Deutsch'),
                            ('fr', 'Français'),
                            ('es', 'Español'),
                            ('nl', 'Nederlands'),
                        ],
                        max_length=2,
                    ),
                ),
                (
                    'commerce_system',
                    models.CharField(
                        choices=[
                            ('google', 'Google'),
                            ('fb', 'Fb'),
                            ('criteo', 'Criteo'),
                            ('amazon', 'Amazon'),
                        ],
                        max_length=6,
                    ),
                ),
                ('name', models.CharField(max_length=255)),
                (
                    'file_format',
                    models.CharField(
                        choices=[('csv', 'Csv'), ('tsv', 'Tsv')], max_length=3
                    ),
                ),
                ('file', models.FileField(blank=True, null=True, upload_to='feeds/')),
                (
                    'file_status',
                    models.IntegerField(
                        choices=[
                            (1, 'Empty'),
                            (2, 'Generating'),
                            (3, 'Done'),
                            (4, 'Error'),
                        ],
                        default=1,
                        max_length=11,
                    ),
                ),
                ('file_created_at', models.DateTimeField(blank=True, null=True)),
                ('file_processing_time', models.DurationField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                (
                    'categories',
                    models.ManyToManyField(
                        blank=True, related_name='feeds', to='feeds.FeedCategory'
                    ),
                ),
                (
                    'region',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='regions.region'
                    ),
                ),
            ],
        ),
    ]
