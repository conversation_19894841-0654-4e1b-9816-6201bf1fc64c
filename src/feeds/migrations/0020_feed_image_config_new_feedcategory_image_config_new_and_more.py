# Generated by Django 4.1.9 on 2024-02-01 11:46

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0019_feedimageconfig_is_unreal'),
    ]

    operations = [
        migrations.AddField(
            model_name='feed',
            name='image_config_new',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, '<PERSON>len<PERSON>, left 30, door open'),
                    (107, '<PERSON>lender, right 30, door closed'),
                    (108, '<PERSON>len<PERSON>, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front'),
                    (304, 'Unreal left'),
                    (307, 'Unreal right'),
                    (401, 'Real photo'),
                ],
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='feedcategory',
            name='image_config_new',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, 'Blender, left 30, door open'),
                    (107, 'Blender, right 30, door closed'),
                    (108, 'Blender, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front'),
                    (304, 'Unreal left'),
                    (307, 'Unreal right'),
                    (401, 'Real photo'),
                ],
                null=True,
            ),
        ),
        migrations.AddField(
            model_name='feedimage',
            name='config_new',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (101, 'Blender, front, door closed'),
                    (102, 'Blender, front, door half open'),
                    (103, 'Blender, front, door open'),
                    (104, 'Blender, left 30, door closed'),
                    (105, 'Blender, left 30, door half open'),
                    (106, 'Blender, left 30, door open'),
                    (107, 'Blender, right 30, door closed'),
                    (108, 'Blender, right 30, door half open'),
                    (109, 'Blender, right 30, door open'),
                    (201, 'WebGL front'),
                    (204, 'WebGL left'),
                    (207, 'WebGL right'),
                    (301, 'Unreal front'),
                    (304, 'Unreal left'),
                    (307, 'Unreal right'),
                    (401, 'Real photo'),
                ],
                default=1,
            ),
            preserve_default=False,
        ),
    ]
