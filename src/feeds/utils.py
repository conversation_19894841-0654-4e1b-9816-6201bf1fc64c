import re

from django.utils.translation import gettext_lazy as _

from slugify import slugify

from custom.utils.in_memory_cache import expiring_lru_cache
from feeds.models import (
    Feed,
    FeedCategory,
)
from gallery.enums import FurnitureCategory
from gallery.types import FurnitureType


def truncate_sentence(sentence, max_characters):
    """
    Truncates a given sentence to fit within a specified number of characters,
    without cutting off individual words.

    Args:
        sentence (str): The input sentence to truncate.
        max_characters (int): The maximum number of characters for the
            truncated sentence.

    Returns:
        str: A truncated version of the input sentence.
    """
    if len(sentence) <= max_characters:
        return sentence

    result = ''
    for word in sentence.split():
        if len(result) + len(word) > max_characters:
            break

        if result:
            result += f' {word}'
        else:
            result = word
    return result


def feed_file_slug(instance, filename) -> str:
    return f'feeds/{slugify(instance.name)}.csv'


def get_items_with_proper_images_count_for_feed(
    feed: 'Feed',
) -> int:
    count = 0
    for category in feed.categories.filter(image_config__isnull=False):
        count += get_items_with_proper_images_count_for_feed_category(category)
    return count


@expiring_lru_cache(ttl=60)
def get_items_with_proper_images_count_for_feed_category(
    feed_category: 'FeedCategory',
) -> int:
    return feed_category.items.filter(
        images__isnull=False, images__config=feed_category.image_config
    ).count()


GOOGLE_CATEGORY_MAPPING = {
    FurnitureCategory.SIDEBOARD: 'Furniture > Cabinets & Storage > Buffets & '
    'Sideboards',
    FurnitureCategory.BOOKCASE: 'Furniture > Shelving > Bookcases & Standing Shelves',
    FurnitureCategory.DESK: 'Furniture > Office Furniture > Desks',
    FurnitureCategory.TV_STAND: 'Furniture > Entertainment Centers & TV Stands',
    FurnitureCategory.SHOERACK: 'Home & Garden > Household Supplies > Storage & '
    'Organization > Clothing & Closet Storage > Shoe Racks'
    ' & Organizers',
    FurnitureCategory.WALL_STORAGE: 'Furniture > Shelving > Wall Shelves & Ledges',
    FurnitureCategory.BEDSIDE_TABLE: 'Furniture > Tables > Nightstands',
    FurnitureCategory.CHEST: 'Furniture > Cabinets & Storage > Dressers',
    FurnitureCategory.VINYL_STORAGE: 'Furniture > Cabinets & Storage > Media Storage '
    'Cabinets & Racks',
    FurnitureCategory.WARDROBE: 'Furniture > Cabinets & Storage > Armoires & Wardrobes',
    FurnitureCategory.TWO_SEATER: 'Furniture > Sofas',
    FurnitureCategory.THREE_SEATER: 'Furniture > Sofas',
    FurnitureCategory.FOUR_PLUS_SEATER: 'Furniture > Sofas',
    FurnitureCategory.CORNER: 'Furniture > Sofas',
    FurnitureCategory.CHAISE_LONGUE: 'Furniture > Sofas',
    FurnitureCategory.ARMCHAIR: 'Furniture > Sofas',
    FurnitureCategory.FOOTREST_AND_MODULES: 'Furniture > Sofas',
    FurnitureCategory.COVER: 'Furniture > Sofas',
}


def add_title_prefix(furniture: FurnitureType, title) -> str:
    """
    Adds a localized prefix to a sofa title based on its furniture category.

    Examples:
    - Input: "2-seater sofa with Ottoman in Lilac Blue"
      → Output: "Modular 2-seater sofa with Ottoman in Lilac Blue"

    - Input: "4+-seater sofa Pale Yellow"
      → Output: "Large sofa in Pale Yellow"

    """
    if furniture.furniture_category in [
        FurnitureCategory.TWO_SEATER,
        FurnitureCategory.THREE_SEATER,
    ]:
        pattern = r'\b\d+-[\w-]+'
        prefix = _('feed_sofa_modular_prefix')
        return re.sub(pattern, lambda m: f'{prefix} {m.group(0)}', title)
    elif furniture.furniture_category in [FurnitureCategory.FOUR_PLUS_SEATER]:
        pattern = r'\b4\+-([^\s-]+)[\s-]([^\s-]+)'
        prefix = _('feed_sofa_large_prefix')
        return re.sub(pattern, lambda m: f'{prefix} {m.group(2)}', title)
    return title
