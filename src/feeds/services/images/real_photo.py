from django.db.models import (
    Prefetch,
    Q,
    QuerySet,
)

from catalogue.models import CatalogueEntry
from feeds.image_configs import ImageConfigOption
from feeds.models import (
    Feed,
    FeedCategory,
    FeedImage,
    FeedItem,
)


class RealPhotoImagesService:
    def __init__(self, feed_ids: list[int]) -> None:
        self.feed_ids = feed_ids

    def _get_queryset(self) -> QuerySet:
        real_photo_feeds = Feed.objects.filter(
            categories__image_config=ImageConfigOption.REAL_PHOTO,
            id__in=self.feed_ids,
        ).prefetch_related(
            'categories',
            'categories__items',
            'categories__items__images',
            Prefetch(
                'categories',
                FeedCategory.objects.filter(image_config=ImageConfigOption.REAL_PHOTO),
                to_attr='real_photo',
            ),
        )

        feed_items_ids = []
        for feed in real_photo_feeds:
            for category in feed.real_photo:
                config = ImageConfigOption(category.image_config)
                category_items = category.items.filter(~Q(images__config=config))
                feed_items_ids.extend(list(category_items.values_list('id', flat=True)))
        return FeedItem.objects.filter(id__in=feed_items_ids)

    @staticmethod
    def _get_catalogue_entry_with_real_photo(
        feed_item: FeedItem,
    ) -> CatalogueEntry | None:
        return CatalogueEntry.objects.filter(
            content_type=feed_item.content_type,
            object_id=feed_item.object_id,
            real_lifestyle_image__isnull=False,
        ).first()

    @staticmethod
    def _create_feed_image(
        feed_item: FeedItem,
        catalogue_entry: CatalogueEntry,
    ) -> None:
        if not catalogue_entry.real_lifestyle_image.name:
            return None

        feed_image = FeedImage.objects.filter(
            config=ImageConfigOption.REAL_PHOTO,
            items__content_type=feed_item.content_type,
            items__object_id=feed_item.object_id,
        ).first()
        if not feed_image:
            feed_image = FeedImage.objects.create(config=ImageConfigOption.REAL_PHOTO)
        feed_image.items.add(feed_item)
        if not feed_image.image.name:
            feed_image.image.name = catalogue_entry.real_lifestyle_image.name
            feed_image.save()

    def update_image_with_real_photo(self) -> None:
        feed_items = self._get_queryset()
        for feed_item in feed_items:
            catalogue_entry = self._get_catalogue_entry_with_real_photo(feed_item)
            if not catalogue_entry:
                continue
            self._create_feed_image(feed_item, catalogue_entry)
