from django import forms
from django.contrib import messages
from django.contrib.contenttypes.models import ContentType
from django.db.models import QuerySet
from django.http import (
    HttpRequest,
    HttpResponseRedirect,
)
from django.urls import reverse

from custom.enums import Furniture
from feeds.models import (
    FeedCategory,
    FeedItem,
)
from product_feeds.utils import get_ids_from_txt


class FeedItemsGenerator:
    def __init__(
        self,
        ids: list[int],
        furniture_type: str,
        feed_categories: QuerySet[FeedCategory],
    ):
        self.ids = ids
        self.furniture_type = furniture_type
        self.feed_categories = feed_categories
        self.created_ids_count = 0
        self.not_existing_ids = []
        self.already_in_category_ids = {}

    def run(self) -> None:
        for feed_category in self.feed_categories:
            generator = FeedItemsForCategoryGenerator(
                feed_category,
                self.ids,
                self.furniture_type,
            )
            generator.run()
            self.created_ids_count += len(generator.created_ids)
            if generator.not_existing_ids:
                self.not_existing_ids.extend(generator.not_existing_ids)
            if generator.already_in_category_ids:
                self.already_in_category_ids[
                    feed_category.name
                ] = generator.already_in_category_ids

    def get_message(self) -> str:
        message = f'Created {self.created_ids_count} feed items.'
        if self.not_existing_ids:
            message += (
                f' Not existing {self.furniture_type} ids are: '
                f'{", ".join(self.not_existing_ids)}.'
            )
        if self.already_in_category_ids:
            for category_name, ids in self.already_in_category_ids.items():
                message += (
                    f' Already in category "{category_name}" are ids: '
                    f'{", ".join(ids)}'
                )
        return message


class FeedItemsForCategoryGenerator:
    def __init__(
        self,
        feed_category: FeedCategory,
        ids: list[int],
        furniture_type: str,
    ):
        self.feed_category = feed_category
        self.ids = ids
        self.furniture_model = Furniture(furniture_type).model
        self.content_type = ContentType.objects.get(
            app_label='gallery',
            model=furniture_type,
        )
        self.created_ids = []
        self.not_existing_ids = []
        self.already_in_category_ids = []

    def run(self) -> None:
        feed_items_to_create = []
        for furniture_id in self.ids:
            try:
                furniture = self.furniture_model.objects.get(id=furniture_id)
            except self.furniture_model.DoesNotExist:
                self.not_existing_ids.append(furniture_id)
                continue
            if FeedItem.objects.filter(
                category=self.feed_category,
                object_id=furniture.id,
                content_type=self.content_type,
            ).exists():
                self.already_in_category_ids.append(furniture_id)
                continue
            furniture_category_id = FeedItem.get_feed_furniture_id(
                furniture,
                self.feed_category,
            )
            feed_item = FeedItem(
                category=self.feed_category,
                object_id=furniture.id,
                content_type=self.content_type,
                furniture_category_id=furniture_category_id,
            )
            self.created_ids.append(furniture_id)
            feed_items_to_create.append(feed_item)
        FeedItem.objects.bulk_create(feed_items_to_create, batch_size=200)


def generate_feed_items(
    modeladmin,
    request: HttpRequest,
    queryset: QuerySet[FeedCategory],
    form: forms.Form,
) -> HttpResponseRedirect:
    ids = get_ids_from_txt(form.cleaned_data.get('ids'))
    furniture_type = form.cleaned_data.get('furniture_type')
    generator = FeedItemsGenerator(ids, furniture_type, queryset)
    generator.run()
    messages.info(request, generator.get_message())
    return HttpResponseRedirect(reverse('admin:feeds_feedcategory_changelist'))
