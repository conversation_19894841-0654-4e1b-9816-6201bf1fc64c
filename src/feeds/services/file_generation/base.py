import csv
import gc

from abc import (
    ABC,
    abstractmethod,
)
from tempfile import NamedTemporaryFile
from typing import Iterator

from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from django.db.models import (
    Prefetch,
    QuerySet,
)
from django.utils import timezone

from slugify import slugify

from custom.utils.parameter_mappers import map_keys_in_dict
from feeds.enums import FileStatus
from feeds.models import (
    Feed,
    FeedCategory,
    FeedImage,
    FeedItem,
    FeedItemQuerySet,
)
from promotions.models import Promotion
from promotions.utils import strikethrough_promo


class BaseExportService(ABC):
    def __init__(self, feed: Feed) -> None:
        self.feed = feed

    @property
    def db_query_chunk_size(self):
        return 1000

    @property
    @abstractmethod
    def serializer_class(self):
        pass

    @property
    def promotion(self) -> Promotion | None:
        return strikethrough_promo(self.feed.region)

    def __call__(self):
        self.feed.file_status = FileStatus.GENERATING
        self.feed.save(update_fields=['file_status'])
        try:
            self._create_feed_file()
        except Exception as e:
            self.feed.file_status = FileStatus.ERROR
            self.feed.save(update_fields=['file_status'])
            raise e
        self.feed.file_status = FileStatus.DONE
        self.feed.save(update_fields=['file_status'])
        gc.collect()  # For test, delete if memory leak still persists

    def filter_feed_items(self, category: FeedCategory) -> QuerySet[FeedItem]:
        items: FeedItemQuerySet = category.items.all()
        return (
            items.all_for_region(region=self.feed.region)
            .with_images()
            .select_related('content_type')
            .prefetch_related(
                'furniture',
                Prefetch(
                    'images',
                    queryset=FeedImage.objects.filter(config=category.image_config),
                    to_attr='prefetch_images',
                ),
            )
            .distinct()
            .order_by('id')
        )

    def get_feed_items(self, category: FeedCategory) -> Iterator:
        paginated_items = Paginator(
            self.filter_feed_items(category=category), self.db_query_chunk_size
        )
        context = {
            'region': self.feed.region,
            'feed': self.feed,
            'category': category,
            'promotion': self.promotion,
        }
        for page in paginated_items.page_range:
            yield self.serializer_class(
                paginated_items.page(page).object_list,
                context=context,
                many=True,
            ).data

    def _create_feed_file(self) -> None:
        time_before_write_csv = timezone.now()
        with NamedTemporaryFile(
            mode='w+', delete=True, newline='', suffix='.csv'
        ) as temp_file:
            serializer = self.serializer_class()
            headers_field = map_keys_in_dict(
                serializer.fields,
                serializer.keys_to_representation,
            ).keys()

            writer = csv.DictWriter(temp_file, fieldnames=headers_field)
            writer.writeheader()

            for category in self.feed.categories.all():
                for chunk in self.get_feed_items(category):
                    writer.writerows(chunk)

            temp_file.flush()
            temp_file.seek(0)

            with open(temp_file.name, 'rb') as f:
                content_file = ContentFile(f.read())
                self.feed.file.save(
                    name=f'{slugify(self.feed.name)}.csv',
                    content=content_file,
                )

        self.feed.file_processing_time = timezone.now() - time_before_write_csv
        self.feed.file_created_at = timezone.now()
        self.feed.save(update_fields=['file_processing_time', 'file_created_at'])
