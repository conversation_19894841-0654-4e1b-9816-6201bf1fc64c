import pytest

from feeds.image_configs import ImageConfigOption
from feeds.services.images.webgl import WebglImageChooserService
from gallery.models import Jetty


@pytest.mark.django_db
class TestWebglImageChooserService:
    def test_no_images_to_generate(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.BLENDER_RIGHT30_DOOR_OPEN
        )
        feeds_item_factory(category=feed_category, furniture=jetty)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        queryset = WebglImageChooserService(Jetty).get_queryset()
        assert queryset.count() == 0

    def test_all_images_generated_from_category(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.WEBGL_FRONT
        )
        feed_item = feeds_item_factory(category=feed_category, furniture=jetty)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        feed_image = feeds_image_factory(config=ImageConfigOption.WEBGL_FRONT)
        feed_image.items.add(feed_item)
        queryset = WebglImageChooserService(Jetty).get_queryset()
        assert queryset.count() == 0

    def test_lacking_image_category_config(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.WEBGL_FRONT
        )
        feed_item = feeds_item_factory(category=feed_category, furniture=jetty)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        queryset = WebglImageChooserService(Jetty).get_queryset()
        assert queryset.count() == 1
        assert queryset.first() == feed_item

    def test_other_furniture_class(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        watty,
    ):
        feed_category = feeds_category_factory(image_config=None)
        feeds_item_factory(category=feed_category, furniture=watty)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        queryset = WebglImageChooserService(Jetty).get_queryset()
        assert queryset.count() == 0

    def test_two_images_already_created_for_category_one_with_different_config(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.WEBGL_FRONT
        )
        feed_item = feeds_item_factory(category=feed_category, furniture=jetty)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        feed_image_1 = feeds_image_factory(config=ImageConfigOption.WEBGL_FRONT)
        feed_image_2 = feeds_image_factory(
            config=ImageConfigOption.BLENDER_FRONT_DOOR_OPEN
        )
        feed_image_1.items.add(feed_item)
        feed_image_2.items.add(feed_item)
        queryset = WebglImageChooserService(Jetty).get_queryset()
        assert queryset.count() == 0
