import pytest

from feeds.image_configs import ImageConfigOption
from feeds.utils import get_items_with_proper_images_count_for_feed


@pytest.mark.django_db
class TestItemsWithProperImagesCount:
    def test_no_image_from_feed(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(image_config=None)
        feed_item = feeds_item_factory(furniture=jetty, category=feed_category)
        feed = feeds_factory()
        feed.categories.add(feed_category)

        image = feeds_image_factory(
            config=ImageConfigOption.UNREAL_LEFT30_STUDIO,
        )
        image.items.add(feed_item)
        assert get_items_with_proper_images_count_for_feed(feed) == 0

    def test_one_image_from_feed_category(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_LEFT30_STUDIO
        )
        feed_item = feeds_item_factory(furniture=jetty, category=feed_category)
        feed = feeds_factory()
        feed.categories.add(feed_category)

        image = feeds_image_factory(
            config=ImageConfigOption.UNREAL_LEFT30_STUDIO,
        )
        image.items.add(feed_item)
        assert get_items_with_proper_images_count_for_feed(feed) == 1

    def test_two_images_from_category(
        self,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category_1 = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_LEFT30_STUDIO
        )
        feed_category_2 = feeds_category_factory(
            image_config=ImageConfigOption.WEBGL_FRONT
        )
        feed_item_1 = feeds_item_factory(furniture=jetty, category=feed_category_1)
        feed_item_2 = feeds_item_factory(furniture=jetty, category=feed_category_2)
        feed = feeds_factory()
        feed.categories.add(feed_category_1, feed_category_2)

        image_1 = feeds_image_factory(
            config=ImageConfigOption.UNREAL_LEFT30_STUDIO,
        )
        image_2 = feeds_image_factory(
            config=ImageConfigOption.WEBGL_FRONT,
        )
        image_1.items.add(feed_item_1)
        image_2.items.add(feed_item_2)
        assert get_items_with_proper_images_count_for_feed(feed) == 2
