import pytest

from vouchers.enums import VoucherType


class GetFeedItemsCases:

    @pytest.mark.parametrize(
        'strikethrough_pricing, expected_sale_price',
        [
            (True, '100.00 EUR'),
            (False, ''),
        ],
    )
    def case_with_promotions(
        self,
        strikethrough_pricing,
        expected_sale_price,
        country_factory,
        promotion_factory,
        voucher_factory,
        promotion_config_factory,
    ):
        country = country_factory(germany=True)
        voucher = voucher_factory(
            kind_of=VoucherType.PERCENTAGE
        )
        promotion = promotion_factory(
            promo_code=voucher,
            strikethrough_pricing=strikethrough_pricing
        )
        promotion_config_factory(
            promotion=promotion,
        )
        return country.region, expected_sale_price

    def case_without_promotions(self, country_factory):
        country = country_factory(germany=True)
        return country.region, ""
