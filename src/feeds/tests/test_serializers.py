from decimal import Decimal

from django.utils import (
    timezone,
    translation,
)

import pytest

from freezegun import freeze_time

from feeds.serializers import (
    BaseFeedItemSerializer,
    FacebookSerializer,
    GoogleSerializer,
)
from feeds.utils import GOOGLE_CATEGORY_MAPPING
from gallery.slugs import (
    get_title_for_furniture,
    get_title_for_grid,
)

ITEM_ID_PREFIX_MAPPER = {
    'watty': 'w',
    'sotty': 's',
}


def get_translated_title_color_and_description(feed_item, feed):
    lang = feed.language.lower()
    with translation.override(lang):
        title = get_title_for_grid(
            feed_item.furniture,
            lang,
        )
        description = get_title_for_furniture(
            feed_item.furniture,
            lang,
        )
        color = feed_item.furniture.color.translated_simple_color.title()
    return title, color, description


@pytest.mark.django_db
class TestFeedItemSerializer:
    @pytest.mark.parametrize(
        ('feed_package',),
        [
            ('jetty_feed',),
            ('watty_feed',),
            ('sotty_feed',),
        ],
    )
    @freeze_time(timezone.now())
    def test_base_serialize_feed_item(
        self,
        mocker,
        feed_package,
        request,
        country,
    ):
        mocker.patch('regions.models.Region.country', return_value=country)
        feed, feed_category, feed_item = request.getfixturevalue(feed_package)
        prefix = ITEM_ID_PREFIX_MAPPER.get(feed_item.content_type.name, '')
        object_item_id = f'{prefix}{feed_item.furniture.id}C{feed_item.category.id}'

        serializer = BaseFeedItemSerializer(
            feed_item,
            context={'region': feed.region, 'feed': feed, 'category': feed_category},
        )
        title, color, description = get_translated_title_color_and_description(
            feed_item, feed
        )
        add_title_prefix_mock = mocker.patch(
            'feeds.serializers.add_title_prefix', return_value=title
        )

        assert serializer.data['item_id'] == object_item_id
        assert serializer.data['title'] == title
        assert serializer.data['description'] == description
        assert serializer.data['color'] == color
        delivery_price = Decimal(
            feed_item.furniture.get_delivery_price(region=feed.region)
        ).quantize(Decimal('0.00'))
        assert (
            serializer.data['shipping(price)']
            == f'{delivery_price} {feed.region.currency.code}'
        )
        assert serializer.data['availability'] == 'in stock'
        assert serializer.data['brand'] == 'Tylko'
        assert serializer.data['condition'] == 'new'
        assert serializer.data['google_product_category'] == '464'
        assert serializer.data['custom_label_0'] in [
            'Original Classic',
            'Original Modern',
            'Tone',
            'Edge',
            'Sofa',
        ]
        assert serializer.data['custom_label_1'] in ['auto', 'manual']
        assert serializer.data['custom_label_4'] in ['', 'blender', 'webgl2']
        assert serializer.data['size'] == feed_item.furniture.get_size()
        assert add_title_prefix_mock.call_count == 1

    @pytest.mark.parametrize(
        ('feed_package',),
        [
            ('jetty_feed',),
            ('watty_feed',),
            ('sotty_feed',),
        ],
    )
    @freeze_time(timezone.now())
    def test_google_serialize_feed_item(
        self,
        mocker,
        feed_package,
        request,
        country,
    ):
        mocker.patch('regions.models.Region.country', return_value=country)

        feed, feed_category, feed_item = request.getfixturevalue(feed_package)

        prefix = ITEM_ID_PREFIX_MAPPER.get(feed_item.content_type.name, '')
        object_item_id = f'{prefix}{feed_item.furniture.id}C{feed_item.category.id}'

        serializer = GoogleSerializer(
            feed_item,
            context={'region': feed.region, 'feed': feed, 'category': feed_category},
        )

        title, color, description = get_translated_title_color_and_description(
            feed_item, feed
        )
        add_title_prefix_mock = mocker.patch(
            'feeds.serializers.add_title_prefix', return_value=title
        )

        assert serializer.data['id'] == object_item_id
        assert serializer.data['title'] == title
        assert serializer.data['description'] == description
        assert serializer.data['color'] == color
        delivery_price = Decimal(
            feed_item.furniture.get_delivery_price(region=feed.region)
        ).quantize(Decimal('0.00'))
        assert (
            serializer.data['shipping(price)']
            == f'{delivery_price} {feed.region.currency.code}'
        )
        assert serializer.data['availability'] == 'in stock'
        assert serializer.data['brand'] == 'Tylko'
        assert serializer.data['condition'] == 'new'
        assert country.code.lower() in serializer.data['link']
        assert serializer.data[
            'google_product_category'
        ] == GOOGLE_CATEGORY_MAPPING.get(feed_item.furniture.furniture_category, '464')
        assert serializer.data['custom_label_0'] in [
            'Original Classic',
            'Original Modern',
            'Tone',
            'Edge',
            'Sofa',
        ]
        assert serializer.data['custom_label_1'] in ['auto', 'manual']
        assert serializer.data['custom_label_4'] in ['', 'blender', 'webgl2']
        assert serializer.data['size'] == feed_item.furniture.get_size()
        assert add_title_prefix_mock.call_count == 1

    @pytest.mark.parametrize(
        ('feed_package',),
        [
            ('jetty_feed',),
            ('watty_feed',),
            ('sotty_feed',),
        ],
    )
    @freeze_time(timezone.now())
    def test_facebook_serialize_feed_item(
        self,
        mocker,
        feed_package,
        request,
        country,
    ):
        mocker.patch('regions.models.Region.country', return_value=country)

        feed, feed_category, feed_item = request.getfixturevalue(feed_package)

        prefix = ITEM_ID_PREFIX_MAPPER.get(feed_item.content_type.name, '')
        object_item_id = f'{prefix}{feed_item.furniture.id}C{feed_item.category.id}'

        serializer = FacebookSerializer(
            feed_item,
            context={'region': feed.region, 'feed': feed, 'category': feed_category},
        )

        title, color, description = get_translated_title_color_and_description(
            feed_item, feed
        )
        add_title_prefix_mock = mocker.patch(
            'feeds.serializers.add_title_prefix', return_value=title
        )

        assert serializer.data['id'] == f'{feed_item.object_id}{prefix}'
        assert serializer.data['content_id'] == object_item_id
        assert serializer.data['title'] == title
        assert serializer.data['description'] == description
        assert serializer.data['color'] == color
        delivery_price = Decimal(
            feed_item.furniture.get_delivery_price(region=feed.region)
        ).quantize(Decimal('0.00'))
        assert (
            serializer.data['shipping(price)']
            == f'{delivery_price} {feed.region.currency.code}'
        )
        assert serializer.data['availability'] == 'in stock'
        assert serializer.data['brand'] == 'Tylko'
        assert serializer.data['condition'] == 'new'
        assert serializer.data['google_product_category'] == '464'
        assert serializer.data['custom_label_0'] in [
            'Original Classic',
            'Original Modern',
            'Tone',
            'Edge',
            'Sofa',
        ]
        assert serializer.data['custom_label_1'] in ['auto', 'manual']
        assert serializer.data['custom_label_4'] in ['', 'blender', 'webgl2']
        assert serializer.data['size'] == feed_item.furniture.get_size()
        assert serializer.data['ios_url'] == feed_item.furniture.get_app_deeplink()
        assert serializer.data['ios_app_store_id'] == 991055398
        assert serializer.data['ios_app_name'] == 'Tylko'
        assert add_title_prefix_mock.call_count == 1
