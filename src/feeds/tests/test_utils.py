import re

from django.utils import translation
from django.utils.translation import gettext_lazy as _

import pytest

from pytest_cases import parametrize_with_cases

from feeds.utils import (
    add_title_prefix,
    truncate_sentence,
)
from gallery.enums import FurnitureCategory
from gallery.slugs import get_title_for_grid


@pytest.mark.parametrize(
    'input_sentence, max_characters, expected_output',
    [
        ('This is a test sentence', 10, 'This is a'),
        ('This is a test sentence', 25, 'This is a test sentence'),
        ('', 10, ''),
        ('Short', 10, 'Short'),
        ('Short sentence here', 6, 'Short'),
        ('Several words here', 7, 'Several'),
        ('Several words here', 8, 'Several'),
    ],
)
def test_truncate_sentence(input_sentence, max_characters, expected_output):
    assert truncate_sentence(input_sentence, max_characters) == expected_output


class TestAddTitleCases:
    @pytest.mark.parametrize('language', [('en'), ('de')])
    def case_jetty(self, jetty_factory, language):
        jetty = jetty_factory()
        with translation.override(language):
            expected_title = get_title_for_grid(jetty, language)
        return jetty, expected_title, language

    @pytest.mark.parametrize('language', [('en'), ('de')])
    def case_sotty(self, sotty_factory, language):
        sotty = sotty_factory(one_module=True)
        with translation.override(language):
            expected_title = get_title_for_grid(sotty, language)
        return sotty, expected_title, language

    @pytest.mark.parametrize('language', [('en'), ('de')])
    def case_two_seater_sotty(self, mocker, sotty_factory, language):
        mocker.patch(
            'gallery.models.models.determine_sofa_category',
            return_value=FurnitureCategory.TWO_SEATER,
        )
        with translation.override(language):
            sotty = sotty_factory()
            prefix = _('feed_sofa_modular_prefix')
            title = get_title_for_grid(sotty, language)
            expected_title = f'{prefix} {title}'
        return sotty, expected_title, language

    @pytest.mark.parametrize('language', [('en'), ('de')])
    def case_three_seater_sotty(self, mocker, sotty_factory, language):
        mocker.patch(
            'gallery.models.models.determine_sofa_category',
            return_value=FurnitureCategory.TWO_SEATER,
        )
        with translation.override(language):
            sotty = sotty_factory()
            prefix = _('feed_sofa_modular_prefix')
            title = get_title_for_grid(sotty, language)
            expected_title = f'{prefix} {title}'
        return sotty, expected_title, language

    @pytest.mark.parametrize('language', [('en'), ('de')])
    def case_four_seater_sotty(self, mocker, sotty_factory, language):
        mocker.patch(
            'gallery.models.models.determine_sofa_category',
            return_value=FurnitureCategory.FOUR_PLUS_SEATER,
        )
        sotty = sotty_factory()
        pattern = r'\b4\+-([^\s-]+)[\s-]([^\s-]+)'
        with translation.override(language):
            prefix = _('feed_sofa_large_prefix')
            title = get_title_for_grid(sotty, language)
            expected_title = re.sub(pattern, lambda m: f'{prefix} {m.group(2)}', title)
        return sotty, expected_title, language


@parametrize_with_cases(
    ('furniture', 'expected_title', 'language'),
    cases=TestAddTitleCases,
)
def test_add_title_prefix(furniture, expected_title, language):
    with translation.override(language):
        title = get_title_for_grid(furniture, language)
        title = add_title_prefix(furniture=furniture, title=title)
    assert title == expected_title
