from decimal import Decimal
from unittest.mock import patch

from django.test import override_settings

import pytest

from pytest_cases import parametrize_with_cases

from custom.utils.parameter_mappers import map_keys_in_dict
from feeds.image_configs import ImageConfigOption
from feeds.serializers import BaseFeedItemSerializer
from feeds.services.file_generation.google import GoogleExportService
from feeds.tests.cases import GetFeedItemsCases


@pytest.mark.django_db
class TestService:
    @pytest.mark.parametrize(
        ('feed_package', 'service_class'),
        [
            ('jetty_feed', GoogleExportService),
            ('watty_feed', GoogleExportService),
        ],
    )
    def test_get_feed_items(
        self,
        feed_package,
        service_class,
        request,
        country,
    ):
        feed, feed_category, feed_item = request.getfixturevalue(feed_package)
        service = service_class(feed)
        with patch('regions.models.Region.country', return_value=country):
            assert len(next(service.get_feed_items(feed_category))[0]) == 33

    @pytest.mark.parametrize(
        ('feed_package', 'service_class'),
        [
            ('jetty_feed', GoogleExportService),
            ('watty_feed', GoogleExportService),
        ],
    )
    def test_get_headers_field(
        self,
        feed_package,
        service_class,
        request,
        country,
    ):
        feed, feed_category, feed_item = request.getfixturevalue(feed_package)
        service = service_class(feed)
        serializer = service.serializer_class()
        with patch('regions.models.Region.country', return_value=country):
            headers_keys = map_keys_in_dict(
                serializer.fields,
                serializer.keys_to_representation,
            )
            for key in serializer.keys_to_representation.keys():
                assert key in headers_keys


@pytest.mark.django_db
class TestExportServiceQueryset:
    @pytest.mark.parametrize(
        'image_config',
        [
            ImageConfigOption.WEBGL_FRONT,
            ImageConfigOption.BLENDER_FRONT_DOOR_OPEN,
            ImageConfigOption.REAL_PHOTO,
        ],
    )
    def test_should_get_feed_items(
        self,
        country_factory,
        feeds_category_factory,
        feeds_factory,
        feeds_item_factory,
        image_config,
    ):
        country_factory(germany=True)
        feed_category = feeds_category_factory(image_config=image_config)
        feed = feeds_factory()
        feeds_item_factory(category=feed_category, images__create_image=True)
        service = GoogleExportService(feed)

        feed_items = [
            item for items in service.get_feed_items(feed_category) for item in items
        ]

        assert len(feed_items) == 1

    def test_get_feed_items_images_cases(
        self, feeds_category_factory, feeds_image_factory, feeds_item_factory
    ):
        config = ImageConfigOption.UNREAL_SCENE
        feed_category = feeds_category_factory(image_config=config)
        # item without feed image
        feeds_item_factory()
        # item with feed image without image
        image = feeds_image_factory(config=config, image=None)
        feeds_item_factory(images=[image], category=feed_category)
        item = feeds_item_factory(images__create_image=True, category=feed_category)
        service = GoogleExportService(feed=feed_category.feeds.first())

        filtered_items = service.filter_feed_items(category=feed_category)

        assert filtered_items.count() == 1
        assert filtered_items.first().id == item.id

    @pytest.mark.parametrize(
        ('add_region', 'corduroy_in_results'), [(True, False), (False, True)]
    )
    def test_all_for_region_corduroy_cases(
        self,
        region_de,
        feeds_category_factory,
        feeds_item_factory,
        add_region,
        corduroy_in_results,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_SCENE
        )
        experimental_feed_item = feeds_item_factory(
            is_sofa_corduroy=True, category=feed_category, images__create_image=True
        )
        control_feed_item = feeds_item_factory(
            is_sofa_wool=True, category=feed_category, images__create_image=True
        )
        service = GoogleExportService(feed=feed_category.feeds.first())
        restricted_regions = {region_de.name} if add_region else {}

        with override_settings(CORDUROY_RESTRICTED_REGIONS=restricted_regions):
            filtered_feed_items = service.filter_feed_items(category=feed_category)

            assert (
                filtered_feed_items.filter(id=experimental_feed_item.id).exists()
            ) is corduroy_in_results
            assert (
                filtered_feed_items.filter(id=control_feed_item.id).exists()
            ) is True

    @pytest.mark.parametrize(
        ('add_region', 's01_in_results'), [(False, False), (True, True)]
    )
    def test_all_for_region_s01_cases(
        self,
        region_de,
        feeds_category_factory,
        feeds_item_factory,
        add_region,
        s01_in_results,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_SCENE
        )
        experimental_feed_item = feeds_item_factory(
            is_sotty=True, category=feed_category, images__create_image=True
        )
        control_feed_item = feeds_item_factory(
            is_jetty=True, category=feed_category, images__create_image=True
        )
        service = GoogleExportService(feed=feed_category.feeds.first())
        restricted_regions = {region_de.name} if add_region else {}

        with override_settings(S01_REGION_KEYS=restricted_regions):
            filtered_feed_items = service.filter_feed_items(category=feed_category)

            assert (
                filtered_feed_items.filter(id=experimental_feed_item.id).exists()
            ) is s01_in_results
            assert (
                filtered_feed_items.filter(id=control_feed_item.id).exists()
            ) is True

    @pytest.mark.parametrize(
        ('add_region', 't03_in_results'),
        [(False, False), (True, True)],
    )
    def test_all_for_region_t03_cases(
        self,
        region_de,
        feeds_category_factory,
        feeds_item_factory,
        add_region,
        t03_in_results,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_SCENE
        )
        experimental_feed_item = feeds_item_factory(
            is_tone_wardrobe=True, category=feed_category, images__create_image=True
        )
        control_feed_item = feeds_item_factory(
            is_edge_wardrobe=True, category=feed_category, images__create_image=True
        )
        service = GoogleExportService(feed=feed_category.feeds.first())
        available_regions = {region_de.name} if add_region else {}

        with override_settings(T03_REGION_KEYS=available_regions):
            filtered_feed_items = service.filter_feed_items(category=feed_category)

            assert (
                filtered_feed_items.filter(id=experimental_feed_item.id).exists()
            ) is t03_in_results
            assert (
                filtered_feed_items.filter(id=control_feed_item.id).exists()
            ) is True

    @parametrize_with_cases('region, expected_sale_price', cases=GetFeedItemsCases)
    def test_get_feed_items_with_promotion(
        self,
        region,
        expected_sale_price,
        feeds_factory,
        feeds_category_factory,
        feeds_image_factory,
        feeds_item_factory,
    ):
        config = ImageConfigOption.UNREAL_SCENE
        feed = feeds_factory(region=region)
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_SCENE, feeds=[feed]
        )
        image = feeds_image_factory(config=config, image='test')
        feeds_item_factory(images=[image], category=feed_category)
        service = GoogleExportService(feed=feed)

        with patch.object(
            BaseFeedItemSerializer,
            '_get_sale_price',
            return_value=Decimal('100.00'),
        ):
            filtered_items = list(next(service.get_feed_items(category=feed_category)))

        assert len(filtered_items) == 1
        assert all(
            [item['sale_price'] == expected_sale_price for item in filtered_items]
        )
