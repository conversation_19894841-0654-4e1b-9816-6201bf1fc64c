from unittest.mock import patch

import pytest

from feeds.image_configs import ImageConfigOption
from feeds.services.images.unreal import create_unreal_render_tasks_for_feed


@pytest.mark.django_db
class TestCreateUnrealRenderTasksForFeed:
    @patch('feeds.services.images.unreal.generate_unreal_render_task_for_feed_item')
    def test_image_exists(
        self,
        create_render_task_mock,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_LEFT30_STUDIO
        )
        feed_item = feeds_item_factory(furniture=jetty, category=feed_category)
        feed = feeds_factory()
        feed.categories.add(feed_category)
        image = feeds_image_factory(
            config=ImageConfigOption.UNREAL_LEFT30_STUDIO,
        )
        image.items.add(feed_item)
        create_unreal_render_tasks_for_feed(feed)
        assert create_render_task_mock.call_count == 0

    @patch('feeds.services.images.unreal.generate_unreal_render_task_for_feed_item')
    def test_create_lacking_image(
        self,
        create_render_task_mock,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_LEFT30_STUDIO
        )
        feeds_item_factory(furniture=jetty, category=feed_category)
        feed = feeds_factory()
        feed.categories.add(feed_category)

        create_unreal_render_tasks_for_feed(feed)
        assert create_render_task_mock.call_count == 1

    @patch('feeds.services.images.unreal.generate_unreal_render_task_for_feed_item')
    def test_image_exists_but_for_different_config(
        self,
        create_render_task_mock,
        feeds_category_factory,
        feeds_item_factory,
        feeds_factory,
        feeds_image_factory,
        jetty,
    ):
        feed_category = feeds_category_factory(
            image_config=ImageConfigOption.UNREAL_LEFT30_STUDIO
        )
        feed_item = feeds_item_factory(furniture=jetty, category=feed_category)
        feed = feeds_factory()
        feed.categories.add(feed_category)

        image = feeds_image_factory(
            config=ImageConfigOption.WEBGL_FRONT,
        )
        image.items.add(feed_item)
        create_unreal_render_tasks_for_feed(feed)
        assert create_render_task_mock.call_count == 1
