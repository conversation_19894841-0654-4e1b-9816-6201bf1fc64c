from django import forms
from django.db.models import QuerySet
from django.http import HttpRequest

from taggit.models import Tag

from custom.enums import (
    Furniture,
    LanguageEnum,
)
from feeds.models import (
    Feed,
    FeedCategory,
)
from feeds.tasks import (
    add_catalogue_entries_into_feed_category,
    add_furniture_into_feed_category,
    add_unreal_render_tasks_into_feed_category,
)
from regions.constants import OTHER_REGION_NAME
from regions.models import Region


class FeedCategoryAdminForm(forms.ModelForm):
    furniture_tag = forms.MultipleChoiceField(
        required=False,
        label='Furniture Tags',
        help_text='Select one or more tags related to the furniture.',
    )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['furniture_tag'].choices = [
            (tag.name, tag.name)
            for tag in Tag.objects.filter(
                catalogueentry__attributes__isnull=False
            ).distinct()
        ]

    class Meta:
        model = FeedCategory
        fields = [
            'image_config',
            'name',
            'auto_furniture_sync',
            'furniture_category',
            'furniture_tag',
        ]


class FeedItemsForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    feed_category = forms.ModelMultipleChoiceField(
        queryset=FeedCategory.objects.all(),
        widget=forms.SelectMultiple(attrs={'class': 'select2-multiple'}),
    )

    @staticmethod
    def create_feed_items_for_catalogue_entry(
        modeladmin,
        request: HttpRequest,
        queryset: QuerySet,
        form: forms.ModelForm,
    ):
        category_ids = [
            feed_category.id for feed_category in form.cleaned_data['feed_category']
        ]
        object_ids = list(queryset.values_list('id', flat=True))
        add_catalogue_entries_into_feed_category.delay(category_ids, object_ids)

    @staticmethod
    def create_feed_items_for_furniture(
        modeladmin,
        request: HttpRequest,
        queryset: QuerySet,
        form: forms.ModelForm,
    ):
        category_ids = [
            feed_category.id for feed_category in form.cleaned_data['feed_category']
        ]
        object_ids = list(queryset.values_list('id', flat=True))
        add_furniture_into_feed_category.delay(
            category_ids,
            object_ids,
            queryset.model.__name__,
        )

    @staticmethod
    def create_feed_items_for_unreal_render_task(
        modeladmin,
        request: HttpRequest,
        queryset: QuerySet,
        form: forms.ModelForm,
    ):
        category_ids = list(
            form.cleaned_data['feed_category'].values_list('id', flat=True)
        )
        task_ids = list(queryset.values_list('id', flat=True))
        add_unreal_render_tasks_into_feed_category.delay(
            feed_category_ids=category_ids,
            render_task_ids=task_ids,
        )


class FeedForm(forms.ModelForm):
    language = forms.ChoiceField(
        choices=[
            (lang, label) for lang, label in LanguageEnum.map_english_labels().items()
        ]
    )

    class Meta:
        model = Feed
        fields = [
            'name',
            'region',
            'language',
            'commerce_system',
            'categories',
            'regenerate_automatically',
        ]

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.fields['region'].queryset = Region.objects.exclude(
            name=OTHER_REGION_NAME
        ).order_by('name')


class AddFurnitureByIdsForm(forms.Form):
    _selected_action = forms.CharField(widget=forms.MultipleHiddenInput)
    furniture_type = forms.ChoiceField(
        choices=Furniture.furniture_types_choices(),
        required=True,
        label='Choose furniture type',
    )
    ids = forms.CharField(widget=forms.Textarea)
