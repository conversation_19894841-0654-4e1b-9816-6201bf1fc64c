import logging

from decimal import Decimal
from typing import Optional

from celery import shared_task

from catalogue.models import CatalogueEntry
from catalogue.services.automatic_merchandising.profit_updater import ProfitCalculator
from custom.enums import Furniture
from custom.models import GlobalSettings
from feeds.enums import ExternalProvider
from feeds.models import (
    Feed,
    FeedCategory,
    FeedImage,
    FeedItem,
)
from feeds.services.file_generation import (
    CriteoExportService,
    FbExportService,
    GoogleExportService,
)
from feeds.services.images.real_photo import RealPhotoImagesService
from feeds.services.images.unreal import create_unreal_render_tasks_for_feed
from feeds.services.images.unreal import (
    save_unreal_image as create_feed_image_from_unreal_render_task,
)
from feeds.services.images.webgl import create_tasks_for_lacking_feed_images
from gallery.types import FurnitureType
from render_tasks.models import UnrealRenderTask

logger = logging.getLogger('cstm')


@shared_task
def generate_feed_file(feed_id: int) -> None:
    feed = Feed.objects.select_related('region').get(pk=feed_id)
    services_map = {
        ExternalProvider.GOOGLE: GoogleExportService,
        ExternalProvider.FB: FbExportService,
        ExternalProvider.CRITEO: CriteoExportService,
    }
    services_map[feed.commerce_system](feed=feed)()


@shared_task
def sync_real_photo_images(feed_ids: [list[int]]):
    RealPhotoImagesService(feed_ids=feed_ids).update_image_with_real_photo()


@shared_task
def regenerate_all_feeds_files():
    for feed in Feed.objects.filter(regenerate_automatically=True):
        generate_feed_file.delay(feed.id)


@shared_task
def generate_furniture_for_categories(category_id: Optional[int] = None) -> None:
    """
    Generate feed items for furniture categories.

    This function fetches categories marked as auto-generated and retrieves
    associated furniture items from the CatalogueEntry model. For each furniture
    item, a new feed item is created in the FeedItem model.

    Args:
        category_id (int): An optional ID to specify a particular category. If provided,
                           the function processes only this category. Otherwise,
                           it processes all auto-generated categories.

    Notes:
        - The furniture_category_id for FeedItem is constructed using a prefix based on
          the furniture type, the furniture's ID, and the category's ID.

    """
    feed_categories = FeedCategory.objects.filter(auto_furniture_sync=True)
    if category_id:
        feed_categories = feed_categories.filter(id=category_id)

    for feed_category in feed_categories:
        generate_furnitures_for_category(feed_category=feed_category)


def generate_furnitures_for_category(feed_category: FeedCategory) -> None:
    list_of_all_furniture_category_id = feed_category.items.values_list(
        'furniture_category_id',
        flat=True,
    )
    entries = CatalogueEntry.objects.filter_by_feed_category(category=feed_category)

    items_batch = []
    for catalogue_entry in entries:
        feed_furniture_id = FeedItem.get_feed_furniture_id(
            catalogue_entry.furniture,
            feed_category,
        )

        if feed_furniture_id in list_of_all_furniture_category_id:
            continue
        items_batch.append(
            FeedItem(
                category=feed_category,
                furniture=catalogue_entry.furniture,
                furniture_category_id=feed_furniture_id,
            )
        )
    FeedItem.objects.bulk_create(items_batch, batch_size=200)


@shared_task
def sync_furniture_with_categories(category_id: Optional[int] = None) -> None:
    """
    Synchronize feed items in furniture categories with actual furniture entries.

    This function ensures that the feed items in each furniture category match the
    actual furniture entries in the CatalogueEntry model. If there are discrepancies
    (i.e., feed items that do not correspond to any actual furniture entry),
    such items are deleted from the category.

    Args:
        category_id (int): An optional ID to specify a particular category to sync.
                           If provided, the function processes only this category.
                           Otherwise, it processes all auto-generated categories.
    """
    feed_categories = FeedCategory.objects.filter(auto_furniture_sync=True)
    if category_id:
        feed_categories = feed_categories.filter(id=category_id)

    for feed_category in feed_categories:
        sync_furniture_with_category(feed_category=feed_category)


def sync_furniture_with_category(feed_category: FeedCategory) -> None:
    furniture_category_ids_in_feed_category = set(
        feed_category.items.values_list('furniture_category_id', flat=True)
    )
    entries = CatalogueEntry.objects.filter_by_feed_category(category=feed_category)
    furniture_category_ids_in_catalogue_entry = set(
        FeedItem.get_feed_furniture_id(entry.furniture, feed_category)
        for entry in entries
    )

    ids_to_delete = (
        furniture_category_ids_in_feed_category
        - furniture_category_ids_in_catalogue_entry
    )
    feed_category.items.filter(furniture_category_id__in=ids_to_delete).delete()
    generate_furniture_for_categories.delay(feed_category.id)


@shared_task
def populate_images_for_category(
    category_id: int, items_ids: list[int] | None = None
) -> None:
    category = FeedCategory.objects.get(pk=category_id)
    if not (config := category.image_config):
        return

    if items_ids:
        items_qs = category.items.filter(id__in=items_ids)
    else:
        items_qs = category.items.all()
    for item in items_qs.select_related('content_type'):
        if image := FeedImage.objects.filter(
            config=config,
            items__object_id=item.object_id,
            items__content_type=item.content_type,
        ).first():
            item.images.add(image)


def _create_feed_item(category: FeedCategory, furniture: FurnitureType) -> FeedItem:
    return FeedItem(
        category=category,
        furniture=furniture,
        furniture_category_id=FeedItem.get_feed_furniture_id(
            furniture=furniture, feed_category=category
        ),
    )


@shared_task
def add_catalogue_entries_into_feed_category(
    feed_category_ids: list[int],
    catalogue_entry_ids: list[int],
) -> None:
    feed_categories = FeedCategory.objects.filter(id__in=feed_category_ids)
    feed_items = [
        _create_feed_item(category=feed_category, furniture=catalogue_entry.furniture)
        for catalogue_entry in CatalogueEntry.objects.filter(id__in=catalogue_entry_ids)
        for feed_category in feed_categories
    ]
    FeedItem.objects.bulk_create(feed_items, batch_size=200, ignore_conflicts=True)

    for feed_category in feed_categories:
        populate_images_for_category.delay(
            feed_category.id,
            [obj.id for obj in feed_items],
        )


@shared_task
def add_furniture_into_feed_category(
    feed_category_ids: list[int],
    furniture_ids: list[int],
    furniture_model_name: str,
) -> None:
    furniture_model_class = Furniture(furniture_model_name).model

    feed_categories = FeedCategory.objects.filter(id__in=feed_category_ids)
    feed_items = [
        _create_feed_item(category=feed_category, furniture=furniture)
        for furniture in furniture_model_class.objects.filter(id__in=furniture_ids)
        for feed_category in feed_categories
    ]
    FeedItem.objects.bulk_create(feed_items, batch_size=200, ignore_conflicts=True)

    for feed_category in feed_categories:
        populate_images_for_category.delay(
            feed_category.id,
            [obj.id for obj in feed_items],
        )


@shared_task
def add_unreal_render_tasks_into_feed_category(
    feed_category_ids: list[int],
    render_task_ids: list[int],
) -> None:
    feed_categories = FeedCategory.objects.filter(id__in=feed_category_ids)
    unique_configs = feed_categories.values_list('image_config', flat=True).distinct()
    unreal_render_tasks = UnrealRenderTask.objects.filter(id__in=render_task_ids)
    feed_items = [
        _create_feed_item(category=feed_category, furniture=render_task.furniture)
        for render_task in unreal_render_tasks
        for feed_category in feed_categories
    ]

    FeedItem.objects.bulk_create(feed_items, batch_size=200, ignore_conflicts=True)
    for render_task in unreal_render_tasks:
        for image_config in unique_configs:
            create_feed_image_from_unreal_render_task(
                render_task=render_task, image_config=image_config
            )

    for feed_category in feed_categories:
        populate_images_for_category.delay(
            feed_category.id,
            [obj.id for obj in feed_items],
        )


@shared_task
def generate_lacking_unreal_render_tasks_for_feed_by_ids(feed_ids: list[int]) -> None:
    feeds = Feed.objects.filter(id__in=feed_ids)
    for feed in feeds:
        create_unreal_render_tasks_for_feed(feed)


@shared_task
def order_lacking_webgl_images_as_task():
    create_tasks_for_lacking_feed_images()


@shared_task
def update_feed_items_margin():
    # margin is individual for jetty/watty, no need to duplicate for each feed item
    queryset = FeedItem.objects.order_by('content_type', 'object_id').distinct(
        'content_type', 'object_id'
    )
    exchange = GlobalSettings.objects.first().exchange_pln
    for feed_item_id in queryset.values_list('id', flat=True):
        update_one_product_margin_task.delay(feed_item_id, exchange)


@shared_task
def update_one_product_margin_task(feed_item_id: int, exchange_rate: Decimal) -> None:
    feed_item = FeedItem.objects.get(id=feed_item_id)
    # check if we don't have this data on catalogue entry
    if catalogue_entry := CatalogueEntry.objects.filter(
        content_type=feed_item.content_type,
        object_id=feed_item.object_id,
        profit__isnull=False,
    ).first():
        product_margin = catalogue_entry.profit
    else:
        product_margin = ProfitCalculator(
            feed_item.furniture,
            exchange_rate,
        ).calculate_profit()
    FeedItem.objects.filter(
        content_type=feed_item.content_type,
        object_id=feed_item.object_id,
    ).update(product_margin=product_margin)
