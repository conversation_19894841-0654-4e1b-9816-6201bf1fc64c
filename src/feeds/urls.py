from django.urls import (
    include,
    path,
)

from rest_framework import routers

from feeds.views import (
    FeedFileView,
    FeedItemImageViewSet,
    FeedItemsRegenerationView,
)

router = routers.SimpleRouter()
router.register('api/v2/feeds/feed_image', FeedItemImageViewSet)

urlpatterns = [
    path(
        'feed/feed_file/<int:id>/',
        FeedFileView.as_view(),
        name='feed_file',
    ),
    path(
        'api/v1/feeds/regenerate_images/',
        FeedItemsRegenerationView.as_view(),
        name='feed_items_regeneration',
    ),
    path('', include(router.urls)),
]
