import enum

from django.db import models


class ExternalProvider(models.TextChoices):
    GOOGLE = 'google'
    FB = 'fb'
    CRITEO = 'criteo'
    AMAZON = 'amazon'


class FileStatus(models.IntegerChoices):
    EMPTY = 1
    GENERATING = 2
    DONE = 3
    ERROR = 4


class FeedItemGeneratedMethod(models.TextChoices):
    AUTO = 'auto'
    MANUAL = 'manual'


class FeedImageType(str, enum.Enum):
    REAL_PHOTO = 'real photos'
    BLENDER = 'blender'
    WEBGL = 'webgl2'
    UNREAL = 'unreal'


class CameraSettings(str, enum.Enum):
    FRONT = 'front'
    LEFT_30 = 'left_30'
    RIGHT_30 = 'right_30'


class DoorOpening(str, enum.Enum):
    ALL_OPEN = 'allDoorsOpen'
    ALL_CLOSED = 'allClosed'
    LEFT_HALF_OPEN = 'leftHalfOpen'
