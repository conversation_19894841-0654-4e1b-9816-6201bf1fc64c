import enum
import uuid

from django.db import models

from cstm_be.media_storage import (
    private_media_storage,
    whatsapp_files_private_media_storage,
)
from custom.enums import ChoicesMixin
from custom.models import Timestampable
from producers.utils import RandomizedUploadTo


class DixaWebHookEvent(Timestampable):
    event_id = models.CharField(unique=True, max_length=256)
    event_type = models.CharField(max_length=256)
    conversation_id = models.CharField(max_length=256)
    requester_email = models.EmailField()
    processed = models.DateTimeField(null=True)


class MessageSource(ChoicesMixin, enum.Enum):
    SLACK = 'slack'
    DIXA = 'dixa'


class ChannelMapping(models.Model):
    slack_channel_id = models.CharField(max_length=100)
    slack_channel_name = models.CharField(max_length=100)
    dixa_queue_name = models.CharField(max_length=100)
    dixa_queue_id = models.CharField(max_length=100)


class Conversation(models.Model):
    dixa_id = models.Char<PERSON>ield(max_length=100, null=True)
    slack_timestamp = models.Char<PERSON>ield(max_length=100, null=True)
    original_source = models.CharField(max_length=100, choices=MessageSource.choices())
    channel_mapping = models.ForeignKey(
        ChannelMapping, on_delete=models.SET_NULL, null=True
    )


class Message(models.Model):
    dixa_id = models.CharField(max_length=100, null=True)
    slack_timestamp = models.CharField(max_length=100, null=True)
    original_source = models.CharField(max_length=100, choices=MessageSource.choices())
    conversation = models.ForeignKey(
        Conversation,
        related_name='messages',
        on_delete=models.SET_NULL,
        null=True,
    )
    text = models.TextField()


class AttachmentFile(models.Model):
    message = models.ForeignKey(
        Message,
        related_name='attachments',
        on_delete=models.CASCADE,
    )
    file = models.FileField(
        blank=True,
        null=True,
        max_length=400,
        storage=private_media_storage,
        upload_to=RandomizedUploadTo('producers/batch/nesting_zip/%Y/%m'),
    )
    dixa_url_path = models.CharField(max_length=1000, null=True)
    slack_id = models.CharField(max_length=1000, null=True)
    file_name = models.CharField(max_length=100)
    source = models.CharField(max_length=100, choices=MessageSource.choices())
    slack_permalink = models.CharField(max_length=1000, null=True)


class WhatsappFile(Timestampable):
    id = models.UUIDField(
        primary_key=True,
        default=uuid.uuid4,
        editable=False,
    )
    file = models.FileField(
        storage=whatsapp_files_private_media_storage,
    )
