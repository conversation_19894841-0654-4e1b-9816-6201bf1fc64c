"""
Create factories for Message, Conversation and ChannelMapping models.
Use factory boy to create test data.
"""
from factory import (
    Faker,
    SubFactory,
    django,
)
from factory.fuzzy import (
    FuzzyChoice,
    FuzzyText,
)

from dixa.models import (
    ChannelMapping,
    Conversation,
    Message,
)


class MessageFactory(django.DjangoModelFactory):
    class Meta:
        model = Message

    text = Faker('text')
    slack_timestamp = FuzzyText(length=10)
    dixa_id = FuzzyText(length=10)
    conversation = SubFactory('dixa.tests.factories.ConversationFactory')


class ConversationFactory(django.DjangoModelFactory):
    class Meta:
        model = Conversation

    slack_timestamp = FuzzyText(length=10)
    dixa_id = FuzzyText(length=10)
    original_source = FuzzyChoice([1, 2])
    channel_mapping = SubFactory('dixa.tests.factories.ChannelMappingFactory')


class ChannelMappingFactory(django.DjangoModelFactory):
    class Meta:
        model = ChannelMapping

    slack_channel_id = FuzzyText(length=10)
    dixa_queue_name = Faker('email')
    dixa_queue_id = FuzzyText(length=10)
