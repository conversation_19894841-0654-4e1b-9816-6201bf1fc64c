"""
Test classes DixaConversationHandler and SlackConversationHandler.
"""

from unittest.mock import patch

from django.urls import reverse

import pytest

from dixa.models import (
    Conversation,
    Message,
    MessageSource,
)
from dixa.services.conversation_handlers import DixaConversationHandler
from dixa.tests.factories import ConversationFactory
from dixa.tests.services.conftest import (
    QUEUE_NAME,
    SLACK_CHANNEL_ID,
)

CONVERSATION_ID = '28749'
MESSAGE_ID = '902b84df-7fe4-4d61-8911-f4f26b3d0138'
TEXT = 'Hello, world!'
SLACK_TIMESTAMP = '1234567890.123456'

DIXA_WEBHOOK_DATA = {
    'event_id': '902b84df-7fe4-4d61-8911-f4f26b3d0128',
    'event_fqn': 'CONVERSATION_MESSAGE_ADDED',
    'event_version': '1',
    'event_timestamp': '2022-03-22T09:54:28.104Z',
    'organization': {'id': '3c9d309b-1756-477b-82f3-35998b3dbdd3', 'name': 'tylko'},
    'data': {
        'conversation': {
            'csid': CONVERSATION_ID,
            'channel': 'EMAIL',
            'status': 'OPEN',
            'direction': 'OUTBOUND',
            'queue': {
                'id': 'bf9c9ca5-748a-48ca-8a7c-ab0cbcab0a63',
                'name': QUEUE_NAME,
            },
            'contact_point': '<EMAIL>',
            'requester': {
                'id': '19c47882-a78c-43bd-8ab2-5c03bf28b0e8',
                'name': None,
                'email': '<EMAIL>',
                'phone': None,
                'roles': ['Enduser'],
            },
            'assignee': {
                'id': 'd87eb158-fb1b-4d1e-aa07-b5971645432e',
                'name': 'aaa',
                'email': '<EMAIL>',
                'phone': None,
                'roles': ['Agent'],
            },
            'subject': 'Tylko 41',
            'tags': [
                {
                    'id': 'a556b3dc-d908-30a1-a552-7bf913c4fc06',
                    'name': 'postsale log courier issue',
                    'is_deactivated': False,
                }
            ],
            'created_at': '2023-03-22T08:50:18.422Z',
        },
        'author': {
            'id': '19c47882-a78c-43bd-8ab2-5c03bf28b0e8',
            'name': None,
            'email': '<EMAIL>',
            'phone': None,
            'roles': ['Enduser'],
        },
        'created_at': '2023-03-22T09:54:28.075Z',
        'message_id': MESSAGE_ID,
        'text': TEXT,
        'direction': 'inbound',
        'channel': 'EMAIL',
        'content': {
            'text': TEXT,
            'content_type': None,
            'original_content_url': '/private/3c9d309b-156-477b-82f3-35998b3dbdd3/email_original_data/4d6b87919e7c0da7cbaaf44284f2b5d3',
            'processed_content_url': None,
        },
        'attachments': [
            {
                'url': '/private/3c9d309b-1756-77b-82f3-35998b3dbdd3/attachment/e750c339ff42a56f1b4c3096dca93778',
                'pretty_name': 'IMG_6436.JPG',
            },
            {
                'url': '/private/3c9d309b-1756-477b-823-35998b3dbdd3/attachment/6b7bd82d829e215ec817510cc999010c',
                'pretty_name': 'IMG_6439.JPG',
            },
        ],
    },
}


class MockWebClientResponse:
    def __init__(self, status_code=200, data=None):
        self.status_code = status_code
        self.data = data or {'ts': SLACK_TIMESTAMP, 'channel': SLACK_CHANNEL_ID}

    def json(self):
        return self.data

    def raise_for_status(self):
        pass


@pytest.mark.django_db
def test_dixa_conversation_handler_create_conversation(channel_mapping):
    conversation, created = DixaConversationHandler.get_or_create_conversation(
        DIXA_WEBHOOK_DATA['data']
    )
    assert Conversation.objects.count() == 1

    assert conversation.dixa_id == str(CONVERSATION_ID)
    assert conversation.slack_timestamp is None
    assert conversation.channel_mapping == channel_mapping
    assert conversation.original_source == MessageSource.DIXA.value


@pytest.mark.django_db
def test_dixa_conversation_handler_create_message(channel_mapping):
    conversation = ConversationFactory(
        dixa_id=CONVERSATION_ID,
        channel_mapping=channel_mapping,
        original_source=MessageSource.DIXA.value,
    )
    message, _ = DixaConversationHandler.get_or_create_message(
        conversation, DIXA_WEBHOOK_DATA['data']
    )

    assert message.dixa_id == MESSAGE_ID
    assert message.slack_timestamp is None
    assert message.original_source == MessageSource.DIXA.value
    assert message.conversation == conversation
    assert message.text == TEXT


@pytest.mark.django_db
@patch(
    'dixa.api_client.manager.DixaManager.get_file',
    return_value=b'file_content',
)
@patch(
    'slack_sdk.WebClient.files_upload',
    return_value=MockWebClientResponse(data={'file': {'id': 1, 'permalink': 'link'}}),
)
@patch(
    'slack_sdk.WebClient.chat_postMessage',
    return_value=MockWebClientResponse(),
)
def test_dixa_conversation_handler(
    mock_web_client, mock_file_upload, mock_file_get, channel_mapping
):
    DixaConversationHandler.handle(DIXA_WEBHOOK_DATA['data'])

    conversation = Conversation.objects.first()
    message = Message.objects.first()

    assert mock_web_client.call_count == 1
    assert mock_file_get.call_count == 2
    assert mock_file_upload.call_count == 2
    assert conversation.dixa_id == CONVERSATION_ID
    assert message.dixa_id == MESSAGE_ID


@pytest.mark.django_db
def test_dixa_webhook_view_with_token(client, dixa_token):
    response = client.post(
        reverse('dixa_messages_listener'),
        DIXA_WEBHOOK_DATA,
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {dixa_token.key}',
    )
    assert response.status_code == 200
