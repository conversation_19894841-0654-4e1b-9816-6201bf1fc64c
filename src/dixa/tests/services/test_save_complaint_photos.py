from unittest.mock import patch

from django.urls import reverse

import pytest

from complaints.models import ComplaintPhoto
from dixa.services.save_complaint_photos import save_complaint_photos
from orders.enums import OrderStatus

CUSTOMER_EMAIL = '<EMAIL>'
CONVERSATION_ID = 123

DIXA_PAYLOAD = {
    'event_id': '111111111111111111111111111111111111',
    'event_fqn': 'CONVERSATION_MESSAGE_ADDED',
    'event_version': '1',
    'event_timestamp': '2024-04-24T05:44:52.450Z',
    'organization': {'id': '123123', 'name': 'tylko'},
    'data': {
        'conversation': {
            'csid': CONVERSATION_ID,
            'channel': 'WHATS_APP',
            'status': 'OPEN',
            'direction': 'INBOUND',
            'queue': {'id': '123123', 'name': 'L1 EN 🇬🇧'},
            'contact_point': '+111111111111',
            'requester': {
                'id': '111111111111111111111111111111111111',
                'name': 'Chiara ',
                'email': CUSTOMER_EMAIL,
                'phone': '+111111111111',
                'roles': ['Enduser'],
                'user_type': 'Contact',
            },
            'assignee': None,
            'subject': None,
            'tags': [],
            'created_at': '2024-04-24T05:44:52.307Z',
        },
        'author': {
            'id': '111111111111111111111111111111111111',
            'name': 'Chiara ',
            'email': CUSTOMER_EMAIL,
            'phone': '+111111111111',
            'roles': ['Enduser'],
            'user_type': 'Contact',
        },
        'created_at': '2024-04-24T05:44:52.450Z',
        'message_id': '902b84df-7fe4-4d61-8911-f4f26b3d0138',
        'text': 'Good morning everybody.',
        'direction': 'inbound',
        'channel': 'WHATS_APP',
        'content': {
            'text': 'Good morning everybody.',
            'content_type': None,
            'original_content_url': None,
            'processed_content_url': None,
        },
        'attachments': [{'url': 'http://attachment.url', 'pretty_name': 'Filename'}],
        'external_id': '111111111111111111111111',
    },
}


@patch('dixa.api_client.manager.DixaManager.get_file', return_value=b'file_content')
@pytest.mark.django_db(transaction=True)
def test_saving_photo(dixa_manager_download_file, order_factory):
    order = order_factory(email=CUSTOMER_EMAIL, status=OrderStatus.DELIVERED)
    save_complaint_photos(
        DIXA_PAYLOAD['data']['attachments'], CUSTOMER_EMAIL, CONVERSATION_ID
    )
    complaint_photo = ComplaintPhoto.objects.first()
    assert complaint_photo
    assert complaint_photo.orders.count() == 1
    assert complaint_photo.orders.first() == order


@patch('dixa.api_client.manager.DixaManager.get_file', return_value=b'file_content')
@pytest.mark.django_db
def test_dixa_webhook_view_with_token(dixa_manager_download_file, client, dixa_token):
    response = client.post(
        reverse('dixa_messages_listener'),
        DIXA_PAYLOAD,
        content_type='application/json',
        HTTP_AUTHORIZATION=f'Bearer {dixa_token.key}',
    )
    assert response.status_code == 200
    assert ComplaintPhoto.objects.count() == 1
