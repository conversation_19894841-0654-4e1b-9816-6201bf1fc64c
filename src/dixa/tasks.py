from datetime import (
    datetime,
    timedelta,
)

from celery import shared_task
from celery.utils.log import get_task_logger
from requests import HTTPError

from dixa.api_client.manager import DixaManager
from dixa.models import (
    ChannelMapping,
    Conversation,
    DixaWebHookEvent,
    WhatsappFile,
)
from dixa.services.conversation_handlers import (
    DixaConversationHandler,
    SlackConversationHandler,
)
from dixa.services.customer_contact import create_contact_form_conversation
from dixa.services.save_complaint_photos import save_complaint_photos
from dixa.utils import generate_order_ids_with_klarna_status
from orders.models import Order

logger = get_task_logger('celery_task')


@shared_task
def add_order_id_to_dixa_conversation(event_id):
    event = DixaWebHookEvent.objects.get(event_id=event_id)
    if event.processed:
        return
    conversation_id = event.conversation_id
    orders = Order.objects.filter_with_proxy_by_email(event.requester_email)
    orders_ids = generate_order_ids_with_klarna_status(orders)

    orders_ids = ', '.join(str(i) for i in orders_ids)
    if orders_ids:
        try:
            dixa_manager = DixaManager()
            dixa_manager.create_note(conversation_id, {'message': orders_ids})
        except HTTPError as e:
            logger.error(
                'Adding note with order id to conversation %s failed: %s',
                conversation_id,
                e.response.text,
            )
            return
    event.processed = datetime.now()
    event.save(update_fields=['processed'])


@shared_task
def create_conversation_from_contact_form(
    requester_email, first_name, subject, message, order_id=None, contact_id=None
):
    """Creates conversation from `ContactForm.cleaned_data`."""
    create_contact_form_conversation(
        requester_email,
        first_name,
        subject,
        message,
        order_id=order_id,
        contact_id=contact_id,
    )


@shared_task
def handle_slack_webhook(event_data: dict):
    try:
        SlackConversationHandler.handle(event_data)
    except (ChannelMapping.DoesNotExist, Conversation.DoesNotExist) as e:
        logger.exception(e)


@shared_task
def handle_dixa_webhook_for_slack(message_data: dict):
    try:
        DixaConversationHandler.handle(message_data)
    except (ChannelMapping.DoesNotExist, Conversation.DoesNotExist) as e:
        logger.exception(e)


@shared_task
def handle_dixa_webhook_for_complaint_photos(
    attachments: list[dict], user_email: str, conversation_id: int
):
    save_complaint_photos(attachments, user_email, conversation_id)


@shared_task
def remove_month_old_whatsapp_files():
    one_month_ago = datetime.now() - timedelta(days=30)
    old_files = WhatsappFile.objects.filter(created_at__lt=one_month_ago)
    old_files.delete()
