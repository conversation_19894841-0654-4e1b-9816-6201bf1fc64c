import hashlib
import hmac
import logging

from django.conf import settings

from rest_framework.permissions import BasePermission

logger = logging.getLogger('cstm')


class DixaPermission(BasePermission):
    def has_permission(self, request, view):
        return request.user.is_authenticated and request.user.username == 'dixa'


class SlackSecretVerificationPermission(BasePermission):
    def has_permission(self, request, view):
        payload = request.body.decode('utf-8')
        timestamp = request.META.get('HTTP_X_SLACK_REQUEST_TIMESTAMP', '')
        signature = request.META.get('HTTP_X_SLACK_SIGNATURE', '')
        version = 'v0'
        message = f'{version}:{timestamp}:{payload}'.encode('utf-8')
        signature_base = hmac.new(
            key=settings.SLACK_WEBHOOK_SECRET.encode('utf-8'),
            msg=message,
            digestmod=hashlib.sha256,
        ).hexdigest()
        return hmac.compare_digest(f'{version}={signature_base}', signature)
