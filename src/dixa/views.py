from dataclasses import (
    asdict,
    dataclass,
)

from django.db import transaction
from django.http import HttpResponseRedirect

from rest_framework import (
    mixins,
    permissions,
    status,
)
from rest_framework.authentication import TokenAuthentication
from rest_framework.mixins import RetrieveModelMixin
from rest_framework.response import Response
from rest_framework.viewsets import (
    GenericViewSet,
    ViewSet,
)

from dixa.authentication import DixaAuthentication
from dixa.models import (
    DixaWebHookEvent,
    WhatsappFile,
)
from dixa.permissions import DixaPermission
from dixa.serializers import (
    DixaWebHookEventSerializer,
    WhatsappFileSerializer,
)
from dixa.tasks import add_order_id_to_dixa_conversation
from orders.models import Order
from user_profile.models import UserProfile


class DixaWebHookCreateConversationViewSet(mixins.CreateModelMixin, GenericViewSet):
    authentication_classes = (DixaAuthentication,)
    permission_classes = (DixaPermission,)
    queryset = DixaWebHookEvent.objects.all()
    serializer_class = DixaWebHookEventSerializer

    def create(self, request, *args, **kwargs):
        try:
            data = self.map_payload_data_to_internal(request.data)
        except (KeyError, TypeError):
            return Response('Wrong data format', status=status.HTTP_400_BAD_REQUEST)
        serializer = self.get_serializer(data=data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        transaction.on_commit(
            lambda: add_order_id_to_dixa_conversation.delay(serializer.data['event_id'])
        )
        headers = self.get_success_headers(serializer.data)
        return Response(
            serializer.data, status=status.HTTP_201_CREATED, headers=headers
        )

    @staticmethod
    def map_payload_data_to_internal(payload):
        return {
            'event_id': payload['event_id'],
            'event_type': payload['event_fqn'],
            'conversation_id': payload['data']['conversation']['csid'],
            'requester_email': payload['data']['conversation']['requester']['email'],
        }


@dataclass
class CustomerUserInfo:
    orders_ids: list[int]
    email: str = ''
    phone: str = ''
    first_name: str = ''
    last_name: str = ''


class CustomerInfoDixaViewSet(ViewSet):
    authentication_classes = (TokenAuthentication,)
    permission_classes = (DixaPermission,)

    def list(self, request):
        search_email = self.request.query_params.get('email', '')
        if not search_email:
            return Response(asdict(CustomerUserInfo(email='empty', orders_ids=[])))
        customer_info = self.get_customer_user_info(search_email)
        return Response(data=asdict(customer_info), content_type='application/json')

    @staticmethod
    def get_customer_user_info(email):
        orders_ids = list(
            Order.objects.filter_with_proxy_by_email(email).values_list('id', flat=True)
        )
        user_profile = UserProfile.objects.filter_with_proxy_by_email(email).first()
        if user_profile:
            return CustomerUserInfo(
                email=email,
                phone=user_profile.phone or '',
                first_name=user_profile.first_name or '',
                last_name=user_profile.last_name or '',
                orders_ids=orders_ids,
            )
        return CustomerUserInfo(email=email, orders_ids=orders_ids)


class WhatsappFileViewSet(GenericViewSet, RetrieveModelMixin):
    permission_classes = (permissions.AllowAny,)
    queryset = WhatsappFile.objects.all()
    serializer_class = WhatsappFileSerializer

    def retrieve(self, request, *args, **kwargs):
        obj = self.get_object()
        return HttpResponseRedirect(obj.file.url)
