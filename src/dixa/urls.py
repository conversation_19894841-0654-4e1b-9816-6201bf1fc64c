from django.urls import (
    include,
    path,
)

from rest_framework import routers

from dixa.api.dixa import DixaWebhookView
from dixa.api.slack import SlackWebhookView

from .views import (
    CustomerInfoDixaViewSet,
    DixaWebHookCreateConversationViewSet,
    WhatsappFileViewSet,
)

router = routers.SimpleRouter()
router.register(
    'cs/customer_info',
    CustomerInfoDixaViewSet,
    basename='customer_info',
)
router.register(
    'cs/dixa_create_conversation',
    DixaWebHookCreateConversationViewSet,
    basename='dixa_web_hooks',
)

whatsapp_file_router = routers.SimpleRouter()
whatsapp_file_router.register(
    '',
    WhatsappFileViewSet,
    basename='whatsapp_files',
)


urlpatterns = [
    path('', include(router.urls)),
    path('dixa/messages', DixaWebhookView.as_view(), name='dixa_messages_listener'),
    path('slack/messages', SlackWebhookView.as_view(), name='slack_messages_listener'),
]
