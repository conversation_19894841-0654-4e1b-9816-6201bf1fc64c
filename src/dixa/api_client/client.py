import base64

from functools import wraps
from time import sleep
from typing import (
    Optional,
    Union,
)

from django.conf import settings

import requests

from rest_framework import status


def retry_request(
    max_retries: int = 3,
    delay_between_requests: int = 3,
    retry_status_codes: Optional[list] = None,
):
    def retry_request_func(func):
        @wraps(func)
        def recursive_retry_wrapper(self, *args, retry_attempt=0, **kwargs):
            status_codes = retry_status_codes or {
                status.HTTP_504_GATEWAY_TIMEOUT,
                status.HTTP_502_BAD_GATEWAY,
                status.HTTP_429_TOO_MANY_REQUESTS,
            }
            try:
                return func(self, *args, **kwargs)
            except requests.HTTPError as error:
                if (
                    error.response.status_code in status_codes
                    and retry_attempt < max_retries
                ):
                    sleep(delay_between_requests)
                    recursive_retry_wrapper(
                        self,
                        *args,
                        retry_attempt=retry_attempt + 1,
                        **kwargs,
                    )
                raise error

        return recursive_retry_wrapper

    return retry_request_func


class DixaApiClient:
    TOKEN = settings.DIXA_API_KEY
    BASE_URL = 'https://dev.dixa.io/v1/'
    ENDPOINT = None

    def get(self, **query_params):
        url = self.get_url(self.ENDPOINT)
        return self.process_get_request(url, **query_params)

    def post(self, payload: dict):
        url = self.get_url(self.ENDPOINT)
        return self.process_post_request(url, payload)

    def get_url(self, endpoint: str) -> str:
        return requests.compat.urljoin(self.BASE_URL, endpoint)

    @retry_request()
    def process_get_request(self, url: str, **query_params) -> Union[list, dict]:
        headers = {'Authorization': self.TOKEN}
        response = requests.get(url, params=query_params, headers=headers)
        response.raise_for_status()
        return response.json()['data']

    @retry_request()
    def process_post_request(self, url: str, payload: dict) -> dict:
        headers = {'Authorization': self.TOKEN, 'Content-Type': 'application/json'}
        response = requests.post(url, json=payload, headers=headers)
        response.raise_for_status()
        return response.json()

    @retry_request()
    def process_put_request(self, url: str, payload: Optional[dict] = None):
        headers = {'Authorization': self.TOKEN, 'Content-Type': 'application/json'}
        response = requests.put(url, json=payload, headers=headers)
        return response


class DixaConversationApi(DixaApiClient):
    ENDPOINT = 'conversations'

    def post(self, payload):
        return super().post(payload)['data']

    def add_message(self, detail_id: str, payload: dict):
        endpoint = f'{self.ENDPOINT}/{detail_id}/messages'
        url = self.get_url(endpoint)
        return self.process_post_request(url, payload)['data']

    def add_note(self, detail_id: str, payload: dict):
        endpoint = f'{self.ENDPOINT}/{detail_id}/notes'
        url = self.get_url(endpoint)
        return self.process_post_request(url, payload)['data']

    def get_messages(self, detail_id: str):
        endpoint = f'{self.ENDPOINT}/{detail_id}/messages'
        url = self.get_url(endpoint)
        return self.process_get_request(url)

    def get_notes(self, detail_id: str):
        endpoint = f'{self.ENDPOINT}/{detail_id}/notes'
        url = self.get_url(endpoint)
        return self.process_get_request(url)

    def close_conversation(self, detail_id: str):
        endpoint = f'{self.ENDPOINT}/{detail_id}/close'
        url = self.get_url(endpoint)
        return self.process_put_request(url)

    def transfer_to_queue(self, detail_id: str, payload: dict):
        endpoint = f'{self.ENDPOINT}/{detail_id}/transfer/queue'
        url = self.get_url(endpoint)
        return self.process_put_request(url, payload)


class DixaEndUserApi(DixaApiClient):
    ENDPOINT = 'endusers'

    def get_conversations(self, detail_id: str):
        endpoint = f'{self.ENDPOINT}/{detail_id}/conversations'
        url = self.get_url(endpoint)
        return self.process_get_request(url)


class DixaAgentApi(DixaApiClient):
    ENDPOINT = 'agents'


class FileApi(DixaApiClient):
    BASE_FILE_URL = 'https://files.dixa.io/'

    @retry_request()
    def get(self, attachment_path: str):
        url = requests.compat.urljoin(self.BASE_FILE_URL, attachment_path)
        headers = {'Authorization': self._get_authorization_header()}
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.content

    def _get_authorization_header(self):
        token = f'bearer:{self.TOKEN}'
        base64_token = base64.b64encode(token.encode('ascii')).decode('ascii')
        return f'Basic {base64_token}'


class DixaClient:
    @property
    def users(self):
        return DixaEndUserApi()

    @property
    def conversations(self):
        return DixaConversationApi()

    @property
    def agents(self):
        return DixaAgentApi()

    @property
    def files(self):
        return FileApi()
