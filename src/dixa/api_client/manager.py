import logging

from requests import HTT<PERSON><PERSON>rror
from rest_framework import status

from .client import (
    DixaClient,
    retry_request,
)

logger = logging.getLogger('cstm')


class DixaManager:
    def __init__(self):
        self.client = DixaClient()

    @retry_request(
        retry_status_codes=[
            status.HTTP_400_BAD_REQUEST,
            status.HTTP_500_INTERNAL_SERVER_ERROR,
        ]
    )
    def get_or_create_user(self, data: dict):
        if not data['email']:
            data = self.client.users.post(data)
            return data['data']
        try:
            data = self.client.users.get(email=data['email'])
            return data[0]
        except HTTPError as e:
            if '404' in str(e):
                data = self.client.users.post(data)
                return data['data']
            raise e

    def create_conversation(self, data):
        return self.client.conversations.post(data)

    def get_conversation_messages(self, conversation_id):
        return self.client.conversations.get_messages(conversation_id)

    def transfer_conversation_to_queue(self, conversation_id, data):
        return self.client.conversations.transfer_to_queue(conversation_id, data)

    def create_message(self, conversation_id, data):
        return self.client.conversations.add_message(conversation_id, data)

    def create_note(self, conversation_id, data):
        return self.client.conversations.add_note(conversation_id, data)

    def get_agents(self):
        return self.client.agents.get()

    def close_conversation(self, conversation_id):
        self.client.conversations.close_conversation(conversation_id)

    def get_file(self, url_path):
        return self.client.files.get(url_path)
