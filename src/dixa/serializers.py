from django.conf import settings

from rest_framework import serializers

from dixa.models import (
    Conversation,
    DixaWebHookEvent,
    Message,
    WhatsappFile,
)


class DixaWebHookEventSerializer(serializers.ModelSerializer):
    class Meta:
        model = DixaWebHookEvent
        fields = ('event_id', 'event_type', 'conversation_id', 'requester_email')


class DixaAttachmentSerializer(serializers.ModelSerializer):
    """Serialize model Attachment to Dixa Api format to add attachment."""

    prettyName = serializers.CharField(source='file_name')
    url = serializers.CharField(source='file.url')

    class Meta:
        model = Message
        fields = ('prettyName', 'url')


class DixaMessageSerializer(serializers.ModelSerializer):
    """Serialize model Message to Dixa Api format to add message."""

    content = serializers.SerializerMethodField()
    attachments = DixaAttachmentSerializer(many=True, read_only=True)
    _type = serializers.ReadOnlyField(default='Inbound')

    @classmethod
    def get_content(cls, instance):
        return {
            'value': instance.text,
            '_type': 'Html',
        }

    class Meta:
        model = Message
        fields = ('content', '_type', 'attachments')


class DixaConversationSerializer(serializers.ModelSerializer):
    requesterId = serializers.SerializerMethodField()
    message = serializers.SerializerMethodField()
    emailIntegrationId = serializers.ReadOnlyField(
        default=settings.DIXA_INTEGRATION_EMAIL
    )
    _type = serializers.ReadOnlyField(default='Email')
    language = serializers.ReadOnlyField(default='en')
    subject = serializers.ReadOnlyField(default='Slack conversation')

    @classmethod
    def get_message(cls, instance):
        message = instance.messages.first()
        return DixaMessageSerializer(message).data

    def get_requesterId(self, instance):
        return self.context.get('requester_id')

    class Meta:
        model = Conversation
        fields = (
            'emailIntegrationId',
            'language',
            'message',
            'subject',
            'requesterId',
            '_type',
        )


class DixaWebhookConversationSerializer(serializers.Serializer):
    csid = serializers.CharField()
    channel = serializers.CharField()
    status = serializers.CharField()
    direction = serializers.CharField()
    queue = serializers.DictField(child=serializers.CharField(), allow_null=True)
    created_at = serializers.DateTimeField()


class DixaWebhookAuthorSerializer(serializers.Serializer):
    id = serializers.CharField()
    name = serializers.CharField(required=False, allow_null=True)
    email = serializers.EmailField(allow_null=True)
    phone = serializers.CharField(required=False, allow_null=True)
    roles = serializers.ListField(
        child=serializers.CharField(),
    )


class DixaWebhookAttachmentSerializer(serializers.Serializer):
    url = serializers.CharField()
    pretty_name = serializers.CharField()


class DixaWebhookContentSerializer(serializers.Serializer):
    text = serializers.CharField(allow_null=True)
    content_type = serializers.CharField(required=False, allow_null=True)
    original_content_url = serializers.CharField(required=False, allow_null=True)
    processed_content_url = serializers.CharField(required=False, allow_null=True)


class DixaWebhookMessageSerializer(serializers.Serializer):
    conversation = DixaWebhookConversationSerializer()
    author = DixaWebhookAuthorSerializer()
    created_at = serializers.DateTimeField()
    message_id = serializers.UUIDField()
    text = serializers.CharField(allow_null=True)
    direction = serializers.CharField()
    channel = serializers.CharField()
    content = DixaWebhookContentSerializer()
    attachments = serializers.ListField(
        child=DixaWebhookAttachmentSerializer(), allow_null=True
    )


class SlackWebhookMessageSerializer(serializers.Serializer):
    channel = serializers.CharField()
    type = serializers.CharField()
    user = serializers.CharField()
    text = serializers.CharField()
    ts = serializers.CharField()
    event_ts = serializers.CharField()
    thread_ts = serializers.CharField(required=False)
    channel_type = serializers.CharField()


class WhatsappFileSerializer(serializers.ModelSerializer):
    class Meta:
        model = WhatsappFile
        fields = '__all__'
