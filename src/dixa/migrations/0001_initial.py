# Generated by Django 3.2.14 on 2022-08-12 17:12

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='DixaWebHookEvent',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('event_id', models.Char<PERSON>ield(max_length=256, unique=True)),
                ('event_type', models.Char<PERSON>ield(max_length=256)),
                ('conversation_id', models.CharField(max_length=256)),
                ('requester_email', models.EmailField(max_length=254)),
                ('processed', models.DateTime<PERSON>ield(null=True)),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
    ]
