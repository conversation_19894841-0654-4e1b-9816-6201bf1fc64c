# Generated by Django 4.1.13 on 2025-04-23 19:36

import uuid

from django.db import (
    migrations,
    models,
)

import cstm_be.media_storage


class Migration(migrations.Migration):

    dependencies = [
        ('dixa', '0002_attachmentfile_channelmapping_conversation_message'),
    ]

    operations = [
        migrations.CreateModel(
            name='WhatsappFile',
            fields=[
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'id',
                    models.UUIDField(
                        default=uuid.uuid4,
                        editable=False,
                        primary_key=True,
                        serialize=False,
                    ),
                ),
                (
                    'file',
                    models.FileField(
                        storage=cstm_be.media_storage.WhatsappFilesPrivateMediaS3Storage,
                        upload_to='',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
    ]
