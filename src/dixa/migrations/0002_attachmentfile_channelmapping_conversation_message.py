# Generated by Django 3.2.16 on 2023-04-03 08:30

import django.core.files.storage
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import producers.utils


class Migration(migrations.Migration):

    dependencies = [
        ('dixa', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChannelMapping',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('slack_channel_id', models.CharField(max_length=100)),
                ('slack_channel_name', models.Char<PERSON>ield(max_length=100)),
                ('dixa_queue_name', models.CharField(max_length=100)),
                ('dixa_queue_id', models.CharField(max_length=100)),
            ],
        ),
        migrations.CreateModel(
            name='Conversation',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('dixa_id', models.CharField(max_length=100, null=True)),
                ('slack_timestamp', models.Char<PERSON>ield(max_length=100, null=True)),
                (
                    'original_source',
                    models.CharField(
                        choices=[('slack', 'SLACK'), ('dixa', 'DIXA')], max_length=100
                    ),
                ),
                (
                    'channel_mapping',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dixa.channelmapping',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='Message',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('dixa_id', models.CharField(max_length=100, null=True)),
                ('slack_timestamp', models.CharField(max_length=100, null=True)),
                (
                    'original_source',
                    models.CharField(
                        choices=[('slack', 'SLACK'), ('dixa', 'DIXA')], max_length=100
                    ),
                ),
                ('text', models.TextField()),
                (
                    'conversation',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='messages',
                        to='dixa.conversation',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='AttachmentFile',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'file',
                    models.FileField(
                        blank=True,
                        max_length=400,
                        null=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to=producers.utils.RandomizedUploadTo(
                            'producers/batch/nesting_zip/%Y/%m'
                        ),
                    ),
                ),
                ('dixa_url_path', models.CharField(max_length=1000, null=True)),
                ('slack_id', models.CharField(max_length=1000, null=True)),
                ('file_name', models.CharField(max_length=100)),
                (
                    'source',
                    models.CharField(
                        choices=[('slack', 'SLACK'), ('dixa', 'DIXA')], max_length=100
                    ),
                ),
                ('slack_permalink', models.CharField(max_length=1000, null=True)),
                (
                    'message',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='attachments',
                        to='dixa.message',
                    ),
                ),
            ],
        ),
    ]
