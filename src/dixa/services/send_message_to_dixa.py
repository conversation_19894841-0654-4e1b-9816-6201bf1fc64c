from django.core.mail import EmailMessage

from orders.models import Order


def send_delay_message_to_dixa(order: Order):
    subject = f'New delayed order from LS {order.id}'
    body = (
        f'Order number: {order.id} \n'
        f'Email address: {order.email} \n'
        f'Country: {order.country} \n'
        f'Phone number: {order.full_phone} \n'
        f'Assembly Service: {"Yes" if order.assembly else "No"} \n'
        f'Delayed by: {order.should_be_produced} days'  # should_be_produced - annotated
    )

    message = EmailMessage(
        subject=subject, body=body, from_email='<EMAIL>', to='<EMAIL>'
    )
    message.send(fail_silently=False)
