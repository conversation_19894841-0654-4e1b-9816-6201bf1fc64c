from django.conf import settings
from django.urls import reverse
from django.utils import translation
from django.utils.translation import gettext

import requests

from celery.utils.log import get_task_logger

from complaints.models import ComplaintImage
from custom.enums import LanguageEnum
from custom.utils.emails import send_html_mail
from dixa.api_client.manager import DixaManager
from dixa.sentiment import detect_language
from dixa.services.errors import (
    DixaConversationCreationError,
    DixaUserCreationError,
)
from orders.models import Order

logger = get_task_logger('celery_task')


def create_contact_form_conversation(
    requester_email, first_name, subject, message, order_id=None, contact_id=None
):
    try:
        service = DixaContactFormConversation(
            requester_email,
            first_name,
            subject,
            message,
            order_id=order_id,
            contact_id=contact_id,
        )
        dixa_conversation_id = service.create_conversation()
    except (DixaUserCreationError, DixaConversationCreationError) as e:
        logger.error(e)
        return
    service.add_customer_contact_form_note(dixa_conversation_id)


class DixaContactFormConversation:
    def __init__(
        self,
        requester_email,
        first_name,
        subject,
        message,
        order_id=None,
        contact_id=None,
    ):
        self.requester_email = requester_email
        self.first_name = first_name
        self.order_id = order_id
        self.contact_id = contact_id
        self.language = self.get_language_code(message)
        with translation.override(self.language):
            self.message_subject = gettext(
                'dixa_contact_form_subject_%(contact_id)s'
            ) % {'contact_id': self.contact_id}
        self.message = f'<b>{self.message_subject}</b><br><br>{message}'
        self.dixa_user_id = self.get_or_create_dixa_user()

    def get_or_create_dixa_user(self):
        payload = {
            'displayName': self.first_name,
            'firstName': self.first_name,
            'lastName': None,
            'email': self.requester_email.lower(),
        }
        dixa_manager = DixaManager()
        try:
            dixa_instance = dixa_manager.get_or_create_user(data=payload)
        except requests.HTTPError as e:
            send_html_mail(
                'mail_notify_cs_dixa_contact_form_error.html',
                'Tylko notification - Dixa contact form creation error',
                context={
                    'requester_email': self.requester_email,
                    'first_name': self.first_name,
                    'subject': self.message_subject,
                    'message': self.message,
                    'order_id': self.order_id,
                    'contact_id': self.contact_id,
                },
                to=settings.DEFAULT_FROM_EMAIL,
            )

            raise DixaUserCreationError(
                f'User {self.requester_email} creation '
                f'for contact form {self.contact_id} '
                f'error: {e.response.text}'
            )

        return dixa_instance['id']

    def create_conversation(self):
        payload = self.get_dixa_payload()
        dixa_manager = DixaManager()
        try:
            dixa_instance = dixa_manager.create_conversation(payload)
        except requests.HTTPError as e:
            raise DixaConversationCreationError(
                f'Conversation creation for '
                f'contact form {self.contact_id} error: {e.response.text}'
            )
        return dixa_instance['id']

    def get_dixa_payload(self):
        payload = {
            '_type': 'ContactForm',
            'emailIntegrationId': settings.DIXA_INTEGRATION_EMAIL,
            'message': {
                'content': {
                    'value': self.message,
                    '_type': 'Html',
                },
                'attachments': self.get_attachments_for_conversation(),
                '_type': 'Inbound',
            },
            'requesterId': self.dixa_user_id,
            'language': self.language,
            'subject': self.message_subject,
        }
        return payload

    def add_customer_contact_form_note(self, conversation_id):
        url = requests.compat.urljoin(
            settings.SITE_URL,
            reverse('cs_customer_contact_detail', args=(self.contact_id,)),
        )
        message = f'Customer case:<br><a href="{url}">{url}</a>'
        if self.order_id:
            try:
                owner_id = Order.objects.get(id=self.order_id).owner_id
            except (Order.DoesNotExist, ValueError):
                pass
            else:
                user_overview_link = requests.compat.urljoin(
                    settings.SITE_URL,
                    reverse('cs_user_overview', args=(owner_id,)),
                )
                message += (
                    f'<br>User overview:'
                    f'<br> <a href="{user_overview_link}">{user_overview_link}</a>'
                )
        dixa_manager = DixaManager()
        try:
            dixa_manager.create_note(conversation_id, {'message': message})
        except requests.HTTPError as e:
            logger.error(
                'Adding note to contact form conversation %s failed: %s',
                conversation_id,
                e.response.text,
            )
            return

    @classmethod
    def get_language_code(cls, message):
        language = detect_language(message)
        return LanguageEnum.get_code_by_language(language)

    def get_attachments_for_conversation(self):
        if not self.contact_id:
            return []
        images = ComplaintImage.objects.filter(customer_contact_id=self.contact_id)
        return [self.serialize_image(image) for image in images]

    @classmethod
    def serialize_image(cls, image: ComplaintImage) -> dict:
        try:
            extension = image.image.name.split('.')[-1]
        except AttributeError:
            extension = ''
        return {
            'prettyName': f'{image.id}.{extension}',
            'url': image.image.url,
        }
