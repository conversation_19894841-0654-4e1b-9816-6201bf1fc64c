import logging
import re

from typing import Optional

from django.conf import settings
from django.core.files.base import ContentFile

import requests

from slack_sdk import WebClient
from slack_sdk.errors import SlackApiError

from dixa.api_client.manager import DixaManager
from dixa.models import (
    AttachmentFile,
    ChannelMapping,
    Conversation,
    Message,
    MessageSource,
)
from dixa.serializers import (
    DixaConversationSerializer,
    DixaMessageSerializer,
)

logger = logging.getLogger('cstm')


class SlackConversationHandler:
    """
    Handles the creation of a conversation and message from a Slack webhook.
    If new conversation, creates a new dixa conversation via API and
    save dixa conversation id to conversation object.
    If existing conversation, creates a new message and saves to database.
    """

    @classmethod
    def handle(cls, data):
        conversation, new_conversation = cls.get_or_create_conversation(data)
        message, new_message = cls.get_or_create_message(conversation, data)
        cls.save_attachments(message, data)
        if new_conversation or not conversation.dixa_id:
            dixa_conversation_id = cls.send_conversation_to_dixa(conversation)
            cls.update_dixa_ids(message, dixa_conversation_id, conversation)
        elif new_message:
            dixa_id = cls.send_message_to_dixa(message)
            message.dixa_id = dixa_id
            message.save(update_fields=['dixa_id'])

    @classmethod
    def get_dixa_message_id(cls, dixa_conversation_id: str):
        manager = DixaManager()
        data = manager.get_conversation_messages(dixa_conversation_id)
        return data[0]['id']

    @classmethod
    def get_or_create_conversation(cls, data: dict) -> (Conversation, bool):
        if 'thread_ts' in data:
            thread_ts = data['thread_ts']
            conversation = Conversation.objects.get(slack_timestamp=thread_ts)
            return conversation, False
        channel_mapping = cls.get_channel_mapping(data)
        conversation, created = Conversation.objects.get_or_create(
            slack_timestamp=data['ts'],
            channel_mapping=channel_mapping,
            defaults={
                'original_source': MessageSource.SLACK.value,
            },
        )
        return conversation, created

    @classmethod
    def update_dixa_ids(cls, message, dixa_id, conversation):
        conversation.dixa_id = dixa_id
        conversation.save(update_fields=['dixa_id'])
        dixa_message_id = cls.get_dixa_message_id(dixa_id)
        message.dixa_id = dixa_message_id
        message.save(update_fields=['dixa_id'])

    @classmethod
    def get_or_create_message(
        cls, conversation: Conversation, data: dict
    ) -> (Message, bool):
        return Message.objects.get_or_create(
            slack_timestamp=data['ts'],
            conversation=conversation,
            defaults={
                'text': data['text'],
                'original_source': MessageSource.SLACK.value,
            },
        )

    @classmethod
    def get_channel_mapping(cls, data):
        return ChannelMapping.objects.get(slack_channel_id=data['channel'])

    @classmethod
    def send_conversation_to_dixa(cls, conversation: Conversation) -> str:
        manager = DixaManager()
        user = manager.get_or_create_user({'email': '<EMAIL>'})
        payload = DixaConversationSerializer(
            conversation, context={'requester_id': user['id']}
        ).data
        response_data = manager.create_conversation(payload)
        dixa_id = response_data['id']
        queue_id = conversation.channel_mapping.dixa_queue_id
        transfer_payload = {'queueId': queue_id}
        manager.transfer_conversation_to_queue(dixa_id, transfer_payload)
        return dixa_id

    @classmethod
    def send_message_to_dixa(cls, message: Message):
        if message.dixa_id:
            return
        manager = DixaManager()
        payload = DixaMessageSerializer(message).data
        response_data = manager.create_message(message.conversation.dixa_id, payload)
        return response_data['messageId']

    @classmethod
    def save_attachments(cls, message, data):
        if 'files' not in data:
            return
        for file_data in data['files']:
            if AttachmentFile.objects.filter(
                slack_id=file_data['id'], message=message
            ).exists():
                continue
            cls.save_attachment(message, file_data['id'])

    @classmethod
    def save_attachment(cls, message, file_id):
        client = WebClient(token=settings.SLACK_BOT_TOKEN)
        file_data = client.files_info(file=file_id).data
        file_info = file_data['file']
        content = cls.download_file(file_info)
        attachment = AttachmentFile(
            message=message,
            slack_id=file_info['id'],
            file_name=file_info['name'],
        )
        attachment.file.save(name=file_info['name'], content=content)
        attachment.save()

    @classmethod
    def download_file(cls, file_info):
        response = requests.get(
            file_info['url_private'],
            headers={'Authorization': f'Bearer {settings.SLACK_BOT_TOKEN}'},
        )
        content = ContentFile(response.content)
        return content


class DixaConversationHandler:
    @classmethod
    def handle(cls, data):
        conversation, new_conversation = cls.get_or_create_conversation(data)
        message, new_message = cls.get_or_create_message(conversation, data)
        attachments = cls.save_attachments(message, data) if new_message else []
        slack_timestamp = cls.send_event_to_slack(
            message, conversation, attachments, start_new_thread=new_conversation
        )
        cls.update_slack_timestamps(
            message, conversation, slack_timestamp, new_conversation
        )

    @classmethod
    def update_slack_timestamps(
        cls,
        message: Message,
        conversation: Conversation,
        slack_timestamp: str,
        new_conversation: bool,
    ):
        if message.slack_timestamp or not slack_timestamp:
            return
        message.slack_timestamp = slack_timestamp
        message.save(update_fields=['slack_timestamp'])
        if new_conversation:
            conversation.slack_timestamp = slack_timestamp
            conversation.save(update_fields=['slack_timestamp'])

    @classmethod
    def get_or_create_message(
        cls, conversation: Conversation, data: dict
    ) -> (Message, bool):
        return Message.objects.get_or_create(
            dixa_id=data['message_id'],
            conversation=conversation,
            defaults={
                'text': cls.clean_html_tags(data['content']['text']),
                'original_source': MessageSource.DIXA.value,
            },
        )

    @classmethod
    def clean_html_tags(cls, text):
        if not text:
            return text
        cleaner = re.compile('<[^<>]*?>')
        cleantext = re.sub(cleaner, '', text)
        return cleantext

    @classmethod
    def get_or_create_conversation(cls, data: dict) -> (Conversation, bool):
        dixa_id = data['conversation']['csid']
        channel_mapping = cls.get_channel_mapping(data)
        return Conversation.objects.get_or_create(
            dixa_id=dixa_id,
            channel_mapping=channel_mapping,
            defaults={
                'original_source': MessageSource.DIXA.value,
            },
        )

    @classmethod
    def get_channel_mapping(cls, data: dict) -> ChannelMapping:
        return ChannelMapping.objects.get(
            dixa_queue_name=data['conversation']['queue']['name']
        )

    @classmethod
    def save_attachments(cls, message: Message, data: dict):
        attachments = []
        if not data.get('attachments'):
            return attachments
        for attachment in data.get('attachments', list()):
            manager = DixaManager()
            file_content = manager.get_file(attachment['url'])
            file_name = attachment['pretty_name']
            attachment_file = AttachmentFile(
                message=message,
                file_name=file_name,
                dixa_url_path=attachment['url'],
                source=MessageSource.DIXA.value,
            )
            attachment_file.file.save(file_name, ContentFile(file_content))
            attachment_file.save()
            attachments.append(attachment_file)
        return attachments

    @classmethod
    def send_event_to_slack(
        cls,
        message: Message,
        conversation: Conversation,
        attachments: list[AttachmentFile],
        start_new_thread: bool,
    ) -> Optional[str]:
        """
        Send message to Slack Api using WebClient if start_new_thread is True,
        then send message as a new thread, otherwise send message as a reply to
        a thread. Save ts from response to Message model.
        """
        if message.slack_timestamp:
            return
        cls.upload_attachments(attachments)
        slack_timestamp = cls.send_message(
            conversation, message, attachments, start_new_thread
        )
        return slack_timestamp

    @classmethod
    def send_message(cls, conversation, message, attachments, start_new_thread):
        message_payload = cls.get_message_payload(
            conversation, message, attachments, start_new_thread
        )
        try:
            client = WebClient(token=settings.SLACK_BOT_TOKEN)
            response = client.chat_postMessage(**message_payload)
        except SlackApiError as e:
            logger.error(
                'Problem with sending message %s to slack. Reason: %s',
                message.id,
                e,
            )
            return
        return response.data['ts']

    @classmethod
    def upload_attachments(cls, attachments):
        client = WebClient(token=settings.SLACK_BOT_TOKEN)
        for attachment in attachments:
            payload = cls.get_attachment_payload(attachment)
            try:
                response = client.files_upload(**payload)
            except SlackApiError as e:
                logger.error(
                    'Problem with sending attachment %s to slack. Reason: %s',
                    attachment.file_name,
                    e,
                )
            else:
                attachment.slack_id = response.data['file']['id']
                attachment.slack_permalink = response.data['file']['permalink']
                attachment.save(update_fields=['slack_id', 'slack_permalink'])

    @classmethod
    def get_attachment_payload(cls, attachment):
        return {
            'filename': attachment.file_name,
            'content': attachment.file.file.read(),
        }

    @classmethod
    def get_message_payload(cls, conversation, message, attachments, start_new_thread):
        attachments_text = ' '.join(
            f'<{attachment.slack_permalink}| >' for attachment in attachments
        )
        message_payload = {
            'channel': conversation.channel_mapping.slack_channel_id,
            'text': ' '.join([message.text, attachments_text]),
        }
        if not start_new_thread:
            message_payload['thread_ts'] = conversation.slack_timestamp
        return message_payload
