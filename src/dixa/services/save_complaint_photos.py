from django.core.files.base import ContentFile

from complaints.models import ComplaintPhoto
from dixa.api_client.manager import DixaManager
from orders.enums import OrderStatus
from orders.models import Order


def save_complaint_photos(
    attachments: list[dict], user_email: str, conversation_id: int
) -> None:
    """Save photos to complaint."""
    if user_email.endswith('tylko.com'):
        return
    orders = get_matching_orders(user_email) if user_email else []
    for attachment in attachments:
        manager = DixaManager()
        file_content = manager.get_file(attachment['url'])
        file_name = attachment['pretty_name']
        complaint_photo = ComplaintPhoto.objects.create(
            file_name=file_name,
            dixa_url_path=attachment['url'],
            dixa_conversation_id=conversation_id,
            email=user_email,
        )
        complaint_photo.photo.save(file_name, ContentFile(file_content))
        complaint_photo.save()
        if orders:
            complaint_photo.orders.set(orders)


def get_matching_orders(user_email: str) -> list[Order]:
    orders = Order.objects.filter_with_proxy_by_email(email=user_email).exclude(
        status__in=[
            OrderStatus.CANCELLED,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_PENDING,
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.CART,
        ]
    )
    return orders
