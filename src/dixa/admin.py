from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from dixa.models import (
    ChannelMapping,
    WhatsappFile,
)


class ChannelMappingAdmin(admin.ModelAdmin):
    list_display = (
        'slack_channel_name',
        'slack_channel_id',
        'dixa_queue_name',
        'dixa_queue_id',
    )
    search_fields = (
        'slack_channel_name',
        'slack_channel_id',
        'dixa_queue_name',
        'dixa_queue_id',
    )


class WhatsappFileAdmin(admin.ModelAdmin):
    list_display = (
        'id',
        'file',
        'url',
    )
    search_fields = ('id',)

    @admin.display(description='URL')
    def url(self, obj: WhatsappFile):
        target_url = reverse('whatsapp_files-detail', kwargs={'pk': str(obj.pk)})
        return format_html(
            '<a href="{}" target="_blank">{}</a>',
            target_url,
            target_url,
        )


admin.site.register(ChannelMapping, ChannelMappingAdmin)
admin.site.register(WhatsappFile, WhatsappFileAdmin)
