from datetime import (
    date,
    timedelta,
)

from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.core.paginator import Paginator
from django.db.models import QuerySet
from django.utils import timezone

from celery import shared_task
from celery.utils.log import get_task_logger

from events.braze.batch_processors import (
    BrazeSubscriptionsProcessor,
    BrazeTrackingEventsProcessor,
)
from events.domain_events.marketing_events import (
    DeliveryAnniversaryEvent,
    WinBack120Event,
    WinBack300Event,
)
from events.models import Event
from events.models.choices import EventStatusChoices
from events.services.subscription_event_handler import (
    EmailSubscriptionData,
    EmailSubscriptionEventHandler,
)
from events.utils import hash_normalized_string
from orders.enums import (
    OrderStatus,
    OrderType,
)
from orders.models import Order

task_logger = get_task_logger('cstm')


@shared_task
def process_events():
    """Collect pending events and process with the appropriate processor."""
    event_processors = []

    if settings.BRAZE_COLLECT_EVENTS:
        event_processors += [
            # Order is important
            BrazeTrackingEventsProcessor,
            BrazeSubscriptionsProcessor,
        ]

    if event_processors:
        queryset = Event.objects.pending()
        for event_processor in event_processors:
            event_processor(queryset).process_events()


@shared_task
def clean_old_events():
    """Remove events that were already processed and are older than 5 days."""
    done_events = Event.objects.filter(status=EventStatusChoices.DONE)
    boundary_date = timezone.now() - timedelta(days=5)
    old_done_events = done_events.filter(created_at__lte=boundary_date)
    old_done_events.delete()


def get_delivered_orders_queryset(delivery_date: date) -> QuerySet[Order]:
    queryset = (
        Order.objects.filter(
            status=OrderStatus.DELIVERED,
            order_type=OrderType.CUSTOMER,
            serialized_logistic_info__contains=[{'delivered_date': str(delivery_date)}],
            items__content_type__in=[
                ContentType.objects.get(app_label='gallery', model='jetty'),
                ContentType.objects.get(app_label='gallery', model='watty'),
            ],
        )
        .select_related('owner', 'owner__profile')
        .only('owner', 'email')
        .distinct('id')
    )
    return queryset


@shared_task
def emit_delivery_anniversary_events() -> None:
    """Trigger Braze events for orders that were delivered exactly 1 year ago."""
    delivery_date = (timezone.now() - timedelta(days=365)).date()
    queryset = get_delivered_orders_queryset(delivery_date)

    for page in Paginator(queryset, 100):
        for order in page:
            DeliveryAnniversaryEvent(user=order.owner, email=order.email)


@shared_task
def emit_win_back_120_events() -> None:
    """Trigger Braze events for Win-Back campaign."""

    delivery_date = (timezone.now() - timedelta(days=120)).date()
    queryset = get_delivered_orders_queryset(delivery_date)

    for page in Paginator(queryset, 100):
        for order in page:
            WinBack120Event(user=order.owner, email=order.email)


@shared_task
def emit_win_back_300_events() -> None:
    """Trigger Braze events for Win-Back campaign."""

    delivery_date = (timezone.now() - timedelta(days=300)).date()
    queryset = get_delivered_orders_queryset(delivery_date)

    for page in Paginator(queryset, 100):
        for order in page:
            WinBack300Event(user=order.owner, email=order.email)


@shared_task
def subscribe_user_to_sms_group(
    user_id: int, email: str, subscription_data: dict[str, str]
) -> None:
    from events.services.subscription_event_handler import (
        SmsSubscriptionData,
        SmsSubscriptionEventHandler,
    )

    user = User.objects.get(pk=user_id)
    external_id = hash_normalized_string(email)
    subscription_data = SmsSubscriptionData(**subscription_data)

    subscription_handler = SmsSubscriptionEventHandler(
        user=user,
        external_id=external_id,
        subscription_data=subscription_data,
        create_new_profile=False,
    )
    subscription_handler.handle()


@shared_task
def subscribe_email_event(
    user_id: int, email: str, subscription_data: dict[str, str]
) -> None:
    user = User.objects.get(pk=user_id)
    external_id = hash_normalized_string(email)
    subscription_data = EmailSubscriptionData(**subscription_data)

    EmailSubscriptionEventHandler(
        user=user,
        external_id=external_id,
        subscription_data=subscription_data,
        check_subscription=True,
    ).handle()
