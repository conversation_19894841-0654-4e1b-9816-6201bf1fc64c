from django.contrib import admin

from events.models import Event
from events.models.models import EventInternal


class EventAdmin(admin.ModelAdmin):
    list_display = ('event_name', 'user_id', 'status', 'retries', 'created_at')
    list_filter = ('event_name',)
    search_fields = ('user_id', 'properties__email')


class EventInternalAdmin(admin.ModelAdmin):
    list_display = ('id', 'event_name', 'user_id', 'status', 'retries', 'created_at')
    list_filter = ('event_name',)
    search_fields = ('user_id', 'properties__email')


admin.site.register(Event, EventAdmin)
admin.site.register(EventInternal, EventInternalAdmin)
