import pytest

from events.choices import (
    BrazeSubscriptionGroupStatus,
    BrazeSubscriptionStates,
)
from events.models import Event
from events.services.subscription_event_handler import (
    EmailSubscriptionData,
    EmailSubscriptionEventHandler,
)
from events.utils import hash_normalized_string


@pytest.mark.django_db
class TestEmailSubscriptionEventHandler:
    @pytest.fixture
    def subscription_data(self):
        return EmailSubscriptionData(
            email='<EMAIL>',
            source='test source',
            group='test group',
            group_state=BrazeSubscriptionStates.SUBSCRIBED,
            global_state=BrazeSubscriptionStates.OPTED_IN,
        )

    def test_creates_subscription_status_update_event_with_expected_properties(
        self,
        user,
        subscription_data,
    ):
        external_id = hash_normalized_string(subscription_data.email)
        subscription_handler = EmailSubscriptionEventHandler(
            user=user,
            external_id=external_id,
            subscription_data=subscription_data,
            check_subscription=False,
        )
        subscription_handler.handle()

        expected_properties = {
            'external_id': external_id,
            'email': '<EMAIL>',
            'email_subscribe': BrazeSubscriptionStates.OPTED_IN,
            'subscription_source': 'test source',
        }

        event = Event.objects.get(event_name='EmailSubscriptionStatusUpdateEvent')
        assert event.properties == expected_properties

    def test_creates_subscription_event_with_expected_properties(
        self,
        user,
        subscription_data,
    ):
        external_id = hash_normalized_string(subscription_data.email)
        subscription_handler = EmailSubscriptionEventHandler(
            user=user,
            external_id=external_id,
            subscription_data=subscription_data,
            check_subscription=False,
        )
        subscription_handler.handle()

        expected_properties = {
            'external_id': external_id,
            'email': '<EMAIL>',
            'subscription_group': 'test group',
            'subscription_state': 'subscribed',
        }

        event = Event.objects.get(event_name='EmailSubscriptionEvent')
        assert event.properties == expected_properties

    def test_no_subscription_related_events_emitted_if_user_already_subscribed(
        self,
        mocker,
        user,
        subscription_data,
    ):
        external_id = hash_normalized_string(subscription_data.email)
        events_count_before_subscription = Event.objects.count()
        with mocker.patch(
            (
                'events.services.subscription_event_handler.'
                'BrazeClient.get_email_subscription_group_status'
            ),
            return_value=BrazeSubscriptionGroupStatus.SUBSCRIBED,
        ):
            subscription_handler = EmailSubscriptionEventHandler(
                user=user,
                external_id=external_id,
                subscription_data=subscription_data,
            )
            subscription_handler.handle()

        assert Event.objects.count() == events_count_before_subscription
