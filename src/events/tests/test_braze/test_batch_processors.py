from datetime import timed<PERSON><PERSON>
from unittest.mock import patch

from django.utils import timezone

import pytest

from freezegun import freeze_time
from requests import RequestException

from events.braze.batch_processors import (
    BrazeCanvasesProcessor,
    BrazeSubscriptionsProcessor,
    BrazeTrackingEventsProcessor,
    BrazeUserTrackBatchGroup,
)
from events.braze.batches import (
    BrazeEventsBatch,
    BrazePurchasesBatch,
    BrazeUserAttributesBatch,
)
from events.models import Event
from events.models.choices import EventStatusChoices
from events.tests.test_braze.braze_concretes import (
    BrazeBatchConcrete,
    BrazeBatchConcreteTwo,
    BrazeBatchProcessorConcrete,
)


@pytest.mark.django_db
class TestBrazeBatchProcessorConcrete:
    def test_queryset_fallback_to_pending_events_if_not_provided(self, event_factory):
        pending_event = event_factory(status=EventStatusChoices.PENDING)
        done_event = event_factory(status=EventStatusChoices.DONE)
        failed_event = event_factory(status=EventStatusChoices.FAILED)

        batch_processor = BrazeBatchProcessorConcrete(queryset=None)

        assert pending_event in batch_processor.queryset
        assert done_event not in batch_processor.queryset
        assert failed_event not in batch_processor.queryset

    def test_get_accepted_events_returns_filtered_events(
        self,
        concrete_batch_event,
        event_factory,
    ):
        other_event = event_factory(event_name='OtherEvent')
        batch_processor = BrazeBatchProcessorConcrete()

        assert Event.objects.count() == 2

        accepted_events = batch_processor._get_accepted_events()

        assert concrete_batch_event in accepted_events
        assert other_event not in accepted_events
        assert len(accepted_events) == 1

    def test_prepare_batches_adds_events_for_each_batch_class(
        self,
        concrete_batch_event,
        concrete_batch_two_event,
    ):
        batch_processor = BrazeBatchProcessorConcrete()
        events = Event.objects.pending()

        batches = batch_processor._prepare_batches(events)
        batch = next(batches)
        batch_two = next(batches)

        assert isinstance(batch, BrazeBatchConcrete)
        assert isinstance(batch_two, BrazeBatchConcreteTwo)
        assert concrete_batch_event in batch.events
        assert concrete_batch_two_event in batch_two.events

    def test_prepare_batches_yields_batch_when_is_full(
        self,
        concrete_batch_event,
        mocker,
    ):
        batch_processor = BrazeBatchProcessorConcrete()
        events = Event.objects.pending()

        with mocker.patch(
            'events.braze.batches.BrazeBatchAbstract.is_full',
            return_value=True,
        ):
            batches = batch_processor._prepare_batches(events)
            batch = next(batches)
            assert batch.is_full

    def test_prepare_batches_packs_events_into_batches_with_no_leftovers(
        self,
        concrete_batch_event,
        concrete_batch_two_event,
        event_factory,
    ):
        batch_processor = BrazeBatchProcessorConcrete()

        event_factory.create_batch(5, event_name=concrete_batch_event.event_name)
        event_factory.create_batch(5, event_name=concrete_batch_two_event.event_name)

        events = Event.objects.pending()

        batches = batch_processor._prepare_batches(events)
        events_packed_into_batches = []
        for batch in batches:
            events_packed_into_batches += batch.events

        assert len(events_packed_into_batches) == len(events)

    def test_create_new_batch_if_previous_is_full(
        self,
        event_factory,
        concrete_batch_event,
    ):
        batch_processor = BrazeBatchProcessorConcrete()
        batch_class = batch_processor.batch_classes[0]
        for _ in range(0, batch_class.max_batch_len):
            event_factory(event_name='BatchAcceptedEvent')

        events = Event.objects.pending()
        assert len(events) == batch_class.max_batch_len + 1

        batches = batch_processor._prepare_batches(events)

        full_batch = next(batches)
        another_batch = next(batches)

        assert full_batch.is_full
        assert another_batch.events

    def test_process_batch_increases_event_retries_if_request_fails(
        self,
        concrete_batch_event,
    ):
        batch = BrazeBatchConcrete()
        batch.add_event(concrete_batch_event)
        retries = concrete_batch_event.retries
        with patch.object(BrazeBatchProcessorConcrete, '_send_to_braze') as send_mock:
            batch_processor = BrazeBatchProcessorConcrete()
            send_mock.side_effect = RequestException
            batch_processor._process_batch(batch)
            assert send_mock.called

            concrete_batch_event.refresh_from_db()
            assert concrete_batch_event.retries == retries + 1

    def test_process_batch_marks_events_as_done_if_request_succeeds(
        self,
        concrete_batch_event,
    ):
        batch = BrazeBatchConcrete()
        assert concrete_batch_event.status == EventStatusChoices.PENDING
        batch.add_event(concrete_batch_event)

        batch_processor = BrazeBatchProcessorConcrete()
        batch_processor._process_batch(batch)

        concrete_batch_event.refresh_from_db()
        assert concrete_batch_event.status == EventStatusChoices.DONE


@pytest.mark.django_db
class TestBrazeTrackingEventsProcessor:
    def test_prepare_batches_yields_grouped_batches(
        self,
        user_attribute_event,
        braze_custom_event,
        purchase_event,
    ):
        processor = BrazeTrackingEventsProcessor()
        events = Event.objects.pending()

        user_track_batch_groups = processor._prepare_batches(events)
        user_track_batch_group = next(user_track_batch_groups)

        assert isinstance(user_track_batch_group, BrazeUserTrackBatchGroup)

    def test_prepare_batches_groups_batches_in_appropriate_order(
        self,
        user_attribute_event,
        braze_custom_event,
        purchase_event,
    ):
        processor = BrazeTrackingEventsProcessor()
        events = Event.objects.pending()

        user_track_batch_groups = processor._prepare_batches(events)
        user_track_batch_group = next(user_track_batch_groups)

        assert isinstance(
            user_track_batch_group.user_attributes_batch,
            BrazeUserAttributesBatch,
        )
        assert isinstance(user_track_batch_group.events_batch, BrazeEventsBatch)
        assert isinstance(user_track_batch_group.purchases_batch, BrazePurchasesBatch)

    def test_prepare_batches_accepts_none_if_no_events_of_a_type(
        self,
        user_attribute_event,
    ):
        processor = BrazeTrackingEventsProcessor()
        events = Event.objects.pending()

        user_track_batch_groups = processor._prepare_batches(events)
        user_track_batch_group = next(user_track_batch_groups)

        assert isinstance(
            user_track_batch_group.user_attributes_batch,
            BrazeUserAttributesBatch,
        )
        assert user_track_batch_group.events_batch is None
        assert user_track_batch_group.purchases_batch is None

    def test_does_not_process_empty_batch_group(self):
        processor = BrazeTrackingEventsProcessor()
        with patch(
            'events.braze.batch_processors.BrazeTrackingEventsProcessor._send_to_braze',
        ) as mock_send_to_braze:
            processor.process_events()
            assert not mock_send_to_braze.called

    def test_handles_failed_and_success_events_returned_from_braze(
        self,
        user_attribute_event,
        braze_custom_event,
    ):
        failed_events_ids = [
            user_attribute_event.id,
        ]
        with patch(
            'events.braze.batch_processors.BrazeTrackingEventsProcessor._send_to_braze',
            return_value=failed_events_ids,
        ) as mock_send_to_braze:
            BrazeTrackingEventsProcessor().process_events()

            user_attribute_event.refresh_from_db()
            braze_custom_event.refresh_from_db()

            assert mock_send_to_braze.call_count == 1
            assert user_attribute_event.retries == 1
            assert user_attribute_event.status == EventStatusChoices.PENDING

            assert braze_custom_event.retries == 0
            assert braze_custom_event.status == EventStatusChoices.DONE

    def test_expected_braze_client_method_called_with_expected_args(
        self,
        user_attribute_event,
        braze_user_attributes_object,
    ):
        batch_processor = BrazeTrackingEventsProcessor()
        braze_object = braze_user_attributes_object.from_event(user_attribute_event)
        with patch(
            'custom.api_clients.braze.BrazeClient.send_user_tracking_data',
        ) as braze_client_method_mock:
            batch_processor.process_events()

            assert braze_client_method_mock.call_count == 1
            assert braze_client_method_mock.call_args.kwargs == {
                'attribute_objects': [braze_object],
                'event_objects': [],
                'purchase_objects': [],
            }


@pytest.mark.django_db
class TestBrazeSubscriptionsProcessor:
    def test_expected_braze_client_method_called_with_expected_args(
        self,
        subscription_event,
        braze_subscription_object,
    ):
        with patch(
            'custom.api_clients.braze.BrazeClient.update_subscriptions',
        ) as braze_client_method_mock:
            braze_object = braze_subscription_object.from_event(subscription_event)
            batch_processor = BrazeSubscriptionsProcessor()
            batch_processor.process_events()

            assert braze_client_method_mock.call_count == 1
            assert braze_client_method_mock.call_args.kwargs == {
                'subscription_group': braze_object.subscription_group_id,
                'subscription_state': braze_object.subscription_state,
                'subscriptions_objects': [braze_object],
            }

    def test_filter_out_events_from_last_3_minutes(
        self,
        event_factory,
    ):
        now = timezone.now()

        with freeze_time(now - timedelta(seconds=181)):  # older than 3 minutes
            event_factory(event_name='EmailSubscriptionEvent')

        with freeze_time(now - timedelta(seconds=179)):
            new_event = event_factory(event_name='EmailSubscriptionEvent')

        with freeze_time(now):
            batch_processor = BrazeSubscriptionsProcessor()
            accepted_events = batch_processor._get_accepted_events()

        assert Event.objects.count() == 2
        assert accepted_events.count() == 1
        assert new_event not in accepted_events


@pytest.mark.django_db
class TestBrazeCanvasesProcessor:
    def test_expected_braze_client_method_called_with_expected_args(
        self,
        canvas_trigger_event,
        braze_recipient_object,
    ):
        batch_processor = BrazeCanvasesProcessor()
        braze_object = braze_recipient_object.from_event(canvas_trigger_event)
        with patch(
            'custom.api_clients.braze.BrazeClient.trigger_canvas',
        ) as braze_client_method_mock:
            batch_processor.process_events()

            assert braze_client_method_mock.call_count == 1
            assert braze_client_method_mock.call_args.kwargs == {
                'canvas_id': braze_object.canvas_id,
                'braze_objects': [braze_object],
            }
