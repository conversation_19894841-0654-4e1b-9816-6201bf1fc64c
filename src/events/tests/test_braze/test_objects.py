class TestBrazeObjectConcrete:
    def test_event_not_equal_when_instances_are_different(
        self,
        braze_object_concrete,
        braze_user_attributes_object,
    ):
        assert braze_object_concrete != braze_user_attributes_object

    def test_events_are_not_equal_when_event_ids_are_different(
        self,
        braze_object_concrete_factory,
    ):
        braze_object_1 = braze_object_concrete_factory(event_id=1)
        braze_object_2 = braze_object_concrete_factory(event_id=2)

        assert braze_object_1 != braze_object_2

    def test_events_are_equal_when_event_ids_are_same(
        self,
        braze_object_concrete_factory,
    ):
        braze_object_1 = braze_object_concrete_factory(event_id=1)
        braze_object_2 = braze_object_concrete_factory(event_id=1)

        assert braze_object_1 == braze_object_2

    def test_to_dict_pops_event_id_from_output(self, braze_object_concrete):
        braze_object_dict = braze_object_concrete.to_dict()
        assert 'event_id' not in braze_object_dict

    def test_to_dict_removes_empty_values_from_output(
        self,
        braze_object_concrete_factory,
    ):
        braze_object = braze_object_concrete_factory(email=None, user_id='')
        braze_object_dict = braze_object.to_dict()

        assert 'email' not in braze_object_dict
        assert 'user_id' not in braze_object_dict
