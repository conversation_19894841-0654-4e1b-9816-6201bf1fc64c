import pytest

from events.braze.batches import (
    BrazeCanvasBatch,
    BrazeSubscriptionsBatch,
)
from events.braze.exceptions import BrazeBatchFullError
from events.choices import BrazeSubscriptionStates
from events.tests.test_braze.braze_concretes import (
    BrazeBatchConcrete,
    BrazeObjectConcrete,
)


@pytest.mark.django_db
class TestBrazeBatchConcrete:
    @pytest.fixture
    def testing_batch(self):
        return BrazeBatchConcrete()

    def test_add_event_saves_braze_object_to_batch_braze_objects_property(
        self,
        testing_batch,
        event,
    ):
        testing_batch.add_event(event)
        braze_object = BrazeObjectConcrete.from_event(event)

        assert braze_object in testing_batch.braze_objects

    def test_add_event_saves_event_to_batch_events_property(self, testing_batch, event):
        testing_batch.add_event(event)

        assert event in testing_batch.events

    def test_add_event_when_not_full(self, testing_batch, event):
        assert len(testing_batch) == 0

        testing_batch.add_event(event)
        assert len(testing_batch) == 1

    def test_batch_is_falsy_when_has_no_braze_objects_added(self, testing_batch):
        assert testing_batch.braze_objects == []
        assert bool(testing_batch) is False

    def test_batch_is_truthy_when_has_braze_objects(self, testing_batch, event):
        testing_batch.add_event(event)

        assert len(testing_batch.braze_objects) == 1
        assert bool(testing_batch) is True

    def test_raises_batch_full_exception_when_full(
        self,
        mocker,
        testing_batch,
        event,
    ):
        with mocker.patch(
            'events.braze.batches.BrazeBatchAbstract.is_full',
            return_value=True,
        ):
            with pytest.raises(BrazeBatchFullError):
                testing_batch.add_event(event)


@pytest.mark.django_db
class TestBrazeSubscriptionsBatch:
    def test_pass_subscription_group_and_state_to_batch(self):
        batch = BrazeSubscriptionsBatch(
            subscription_group='test group',
            subscription_state=BrazeSubscriptionStates.SUBSCRIBED,
        )
        assert batch.subscription_group == 'test group'
        assert batch.subscription_state == BrazeSubscriptionStates.SUBSCRIBED


@pytest.mark.django_db
class TestBrazeCanvasBatch:
    def test_pass_canvas_id_to_batch(self):
        batch = BrazeCanvasBatch(canvas_id='123')
        assert batch.canvas_id == '123'
