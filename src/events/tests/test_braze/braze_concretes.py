from dataclasses import dataclass
from typing import Optional

from events.braze.batch_processors import BrazeBatchProcessorAbstract
from events.braze.batches import B<PERSON><PERSON>atchAbstract
from events.braze.objects import BrazeObjectAbstract


@dataclass(frozen=True, eq=False)
class BrazeObjectConcrete(BrazeObjectAbstract):
    email: Optional[str] = None
    user_id: Optional[int] = None

    @classmethod
    def from_event(cls, event):
        return cls(
            event_id=event.id,
            email=event.properties.get('email'),
            user_id=event.properties.get('user_id'),
        )


class BrazeBatchConcrete(BrazeBatchAbstract):
    max_batch_len = 10
    braze_object_type = BrazeObjectConcrete
    accepted_events = []


class BrazeBatchConcreteTwo(BrazeBatchAbstract):
    max_batch_len = 10
    braze_object_type = BrazeObjectConcrete
    accepted_events = []


class BrazeBatchProcessorConcrete(BrazeBatchProcessorAbstract):
    batch_classes = [
        BrazeBatchConcrete,
        BrazeBatchConcreteTwo,
    ]

    def _send_to_braze(self, batch):
        return
