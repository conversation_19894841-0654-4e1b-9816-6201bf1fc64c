from decimal import Decimal
from unittest.mock import patch

from django.test import override_settings
from django.urls import reverse
from django.utils import (
    timezone,
    translation,
)

import pytest

from freezegun import freeze_time
from rest_framework import status

from custom.enums import LanguageEnum
from custom.internal_api.enums import Shipper
from events.choices import BrazeEventClasses
from events.models import Event
from events.utils import get_sellable_items_sorted_ids
from gallery.enums import (
    FurnitureStatusEnum,
    SellableItemContentTypes,
)
from invoice.tasks import create_pdf_and_send_to_client
from orders.enums import OrderStatus
from orders.tasks import process_paid_order_to_production
from pricing_v3.services.price_calculators import OrderPriceCalculator


@pytest.fixture
def cart_order(order_factory):
    return order_factory(status=OrderStatus.CART)


@pytest.fixture
def sample_cart_order(order_factory, order_item_factory, sample_box_factory):
    cart_order = order_factory(
        items=None,
        status=OrderStatus.CART,
        email='<EMAIL>',
    )
    sample_box = sample_box_factory(owner=cart_order.owner)
    order_item_factory(order=cart_order, order_item=sample_box)
    return cart_order


@pytest.mark.django_db
class TestCartAbandonerFlow:
    def test_item_added_to_cart(self, api_client, jetty, cart):
        events_before = Event.objects.count()
        items_before = cart.items.count()

        url = reverse('cart-v2-add-to-cart', args=[cart.id])
        api_client.force_login(cart.owner)
        data = {
            'sellable_item_id': jetty.id,
            'content_type': SellableItemContentTypes.JETTY,
        }
        response = api_client.post(url, data=data)
        assert response.status_code == status.HTTP_201_CREATED

        self._make_cart_update_assertions(cart, events_before, items_before)

    def test_item_deleted_from_cart(self, api_client, cart_factory):
        cart = cart_factory()
        events_before = Event.objects.count()
        items_before = cart.items.count()
        assert items_before == 2

        api_client.force_login(cart.owner)
        response = api_client.post(
            reverse('cart-v2-delete-from-cart', args=[cart.id]),
            {
                'sellable_item_id': cart.items.first().object_id,
                'content_type': cart.items.first().content_type.model,
            },
        )
        assert response.status_code == status.HTTP_204_NO_CONTENT

        self._make_cart_update_assertions(
            cart,
            events_before,
            items_before,
            item_removed=True,
        )

    def test_add_item_to_cart_from_wishlist(
        self,
        api_client,
        jetty_factory,
        cart,
    ):
        events_before = Event.objects.count()
        items_before = cart.items.count()

        wishlist_item = jetty_factory(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner=cart.owner,
        )

        url = reverse('cart-v2-add-to-cart', kwargs={'pk': cart.id})
        api_client.force_login(cart.owner)
        data = {'sellable_item_id': wishlist_item.id, 'content_type': 'jetty'}
        response = api_client.post(url, data=data, format='json')
        assert response.status_code == status.HTTP_201_CREATED

        self._make_cart_update_assertions(cart, events_before, items_before)

    def test_last_item_deleted_from_cart(
        self,
        api_client,
        cart_factory,
        cart_item_factory,
    ):
        cart = cart_factory(items=[])
        cart_item_factory(cart=cart)

        events_before = Event.objects.count()
        items_before = cart.items.count()
        assert items_before == 1

        api_client.force_login(cart.owner)
        response = api_client.post(
            reverse('cart-v2-delete-from-cart', args=[cart.id]),
            {
                'sellable_item_id': cart.items.last().object_id,
                'content_type': cart.items.last().content_type.model,
            },
        )
        assert response.status_code == status.HTTP_204_NO_CONTENT

        cart.refresh_from_db()
        assert Event.objects.count() == events_before + 1
        assert cart.items.count() == items_before - 1

        event = Event.objects.last()
        assert event.event_name == 'CartEmptyEvent'

    @staticmethod
    def _make_cart_update_assertions(
        cart,
        events_before,
        items_before,
        item_removed=False,
    ):
        cart.refresh_from_db()
        assert Event.objects.count() == events_before + 1

        expected_items_count = items_before - 1 if item_removed else items_before + 1
        assert cart.items.count() == expected_items_count

        event = Event.objects.last()
        assert event.event_name == 'CartUpdateEvent'
        assert Decimal(event.properties['total_price']) == cart.total_price
        assert event.properties['cart_id'] == cart.id


@pytest.mark.django_db
class TestTransactFlow:
    def test_start_payment(self, cart_order, api_client):
        payment_entry_events_before = Event.objects.filter(
            event_name=BrazeEventClasses.PAYMENT_ENTRY
        ).count()

        url = reverse('order-change-status-to-pending', args=(cart_order.id,))
        api_client.force_authenticate(cart_order.owner)
        response = api_client.post(url, {})
        assert response.status_code == status.HTTP_200_OK

        payment_entry_events = Event.objects.filter(
            event_name=BrazeEventClasses.PAYMENT_ENTRY
        )
        assert payment_entry_events.count() == payment_entry_events_before + 1

        properties = payment_entry_events.last().properties
        assert properties['order_id'] == cart_order.id
        assert properties['email'] == cart_order.email

    @override_settings(CELERY_TASK_ALWAYS_EAGER=True)
    @patch('user_profile.models.LoginAccessToken.create_for_user', return_value='abc')
    def test_send_order_summary(self, _, cart_order, api_client):
        url = reverse('order-change-status-to-pending', args=(cart_order.id,))
        api_client.force_authenticate(cart_order.owner)
        response = api_client.post(url, {})
        assert response.status_code == status.HTTP_200_OK

        order_summary_request_event = Event.objects.filter(
            event_name=BrazeEventClasses.ORDER_SUMMARY_REQUEST,
        )
        assert order_summary_request_event.count() == 1

        properties = order_summary_request_event.last().properties
        assert properties['order_id'] == cart_order.id
        assert properties['email'] == cart_order.email
        assert properties['lat'] == 'abc'
        assert properties['items_ids'] == get_sellable_items_sorted_ids(cart_order)

    def test_start_payment_if_user_entered_different_email_in_checkout(
        self,
        order_factory,
        api_client,
    ):
        cart_order = order_factory(status=OrderStatus.CART, email='<EMAIL>')
        assert cart_order.email != cart_order.owner.email

        payment_entry_events_before = Event.objects.filter(
            event_name=BrazeEventClasses.PAYMENT_ENTRY
        ).count()

        url = reverse('order-change-status-to-pending', args=(cart_order.id,))
        api_client.force_authenticate(cart_order.owner)
        response = api_client.post(url, {})
        assert response.status_code == status.HTTP_200_OK

        external_marketing_profile_create_event = Event.objects.filter(
            event_name=BrazeEventClasses.CREATE_PROFILE,
        )
        payment_entry_events = Event.objects.filter(
            event_name=BrazeEventClasses.PAYMENT_ENTRY
        )
        assert external_marketing_profile_create_event.count() == 1
        assert payment_entry_events.count() == payment_entry_events_before + 1

        properties = payment_entry_events.last().properties
        assert properties['order_id'] == cart_order.id
        assert properties['email'] == cart_order.email

    def test_payment_failed(
        self,
        transaction_factory,
        notification_factory,
        api_client,
    ):
        transaction = transaction_factory(
            status='PENDING',
            order__status=OrderStatus.PAYMENT_PENDING,
        )
        OrderPriceCalculator(transaction.order).calculate()
        unsuccessful_notification = notification_factory(
            code='AUTHORISATION',
            success=False,
            transaction=transaction,
        )

        transaction.update_after_notification(unsuccessful_notification)
        transaction.refresh_from_db()

        payment_failed_events = Event.objects.filter(
            event_name=BrazeEventClasses.PAYMENT_FAILED,
        )
        assert payment_failed_events.count() == 1

        properties = payment_failed_events.last().properties
        assert properties['order_id'] == transaction.order.id
        assert properties['email'] == transaction.order.email

    @patch('b2b.tasks.update_pipedrive_account_after_order_payment')
    def test_payment_success(
        self,
        _,
        transaction_factory,
        notification_factory,
        api_client,
        cart_factory,
    ):
        transaction = transaction_factory(
            status='PENDING',
            order__status=OrderStatus.PAYMENT_PENDING,
        )
        OrderPriceCalculator(transaction.order).calculate()
        success_notification = notification_factory(
            code='AUTHORISATION',
            success=True,
            transaction=transaction,
        )
        now = timezone.now()
        with freeze_time(now), translation.override(LanguageEnum.EN):
            transaction.update_after_notification(success_notification)
            transaction.refresh_from_db()

        purchase_events = Event.objects.filter(
            event_name=BrazeEventClasses.PURCHASE,
        )
        purchase_attribute_update_events = Event.objects.filter(
            event_name=BrazeEventClasses.PURCHASE_ATTRIBUTES_UPDATE,
        )
        assert purchase_events.count() == 1
        assert purchase_attribute_update_events.count() == 1
        properties = purchase_events.last().properties
        assert properties['email'] == transaction.order.email
        assert properties['product_id'] == 'Tylko Purchase'
        assert properties['price'] == str(transaction.order.region_total_price)
        assert properties['currency'] == transaction.order.currency.code
        assert properties['time'] == now.isoformat(timespec='minutes')
        assert properties['items'][0]['color'] == 'White'
        assert properties['items'][0]['shelf_type'] == 0
        assert properties['items'][0]['region_price'] == (
            str(transaction.order.items.last().region_price)
        )
        assert properties['items'][0]['furniture_type'] == 'jetty'
        assert properties['items'][0]['furniture_category'] == 'bookcase'
        assert properties['items'][1]['color'] == 'White'
        assert properties['items'][1]['shelf_type'] == 0
        assert properties['items'][1]['region_price'] == (
            str(transaction.order.items.first().region_price)
        )
        assert properties['items'][1]['furniture_type'] == 'jetty'
        assert properties['items'][1]['furniture_category'] == 'bookcase'

    @patch('invoice.models.Invoice._generate_pretty_id_db_sequence', return_value='123')
    @patch('invoice.models.Invoice.get_exchange_date_and_rate', return_value=(None, 1))
    @patch(
        'orders.services.process_to_production.MoveOrderToProduction.move_order_items_to_production'
    )
    @patch('b2b.tasks.update_pipedrive_account_after_order_payment')
    @patch('orders.internal_api.events.OrderPushedToProductionEvent.execute')
    @patch('orders.internal_api.events.OrderStatusChangedEvent.execute')
    def test_send_invoice(
        self,
        mocked_order_status_changed_event,
        mocked_order_pushed_to_production_event,
        _,
        __,
        ___,
        ____,
        transaction_factory,
        notification_factory,
        api_client,
    ):
        transaction = transaction_factory(
            status='PENDING',
            order__status=OrderStatus.PAYMENT_PENDING,
        )
        OrderPriceCalculator(transaction.order).calculate()
        success_notification = notification_factory(
            code='AUTHORISATION',
            success=True,
            transaction=transaction,
        )
        transaction.update_after_notification(success_notification)
        transaction.refresh_from_db()
        process_paid_order_to_production()
        create_pdf_and_send_to_client()

        invoice_ready_events = Event.objects.filter(
            event_name=BrazeEventClasses.INVOICE_READY,
        )
        assert invoice_ready_events.count() == 1

        properties = invoice_ready_events.first().properties
        assert properties['email'] == transaction.order.email
        assert properties['order_id'] == transaction.order.id
        assert properties['invoice_url'] == (
            transaction.order.get_invoices().first().pdf.url
        )

    # TODO: Sending corrections temporarily disabled
    # @pytest.mark.nbp
    # @patch('invoice.models.Invoice._validate_correction')
    # @patch('invoice.models.Invoice._generate_pretty_id', return_value='123abc')
    # @patch('customer_service.models.CSCorrectionRequest._add_tag_to_invoice_item')
    # @patch('customer_service.models.MoneyCashBack')
    # def test_send_invoice_correction(
    #     self,
    #     _,
    #     __,
    #     ___,
    #     ____,
    #     invoice_factory,
    #     region_factory,
    #     cs_correction_request_factory,
    #     admin_user,
    # ):
    #     invoice = invoice_factory(
    #         pretty_id='test/1/2/3',
    #         order__country='germany',
    #         order__region=region_factory(germany=True),
    #     )
    #     cs_correction_request = cs_correction_request_factory(
    #         correction_amount_gross=123,
    #         invoice=invoice,
    #         issuer=admin_user,
    #     )
    #
    #     cs_correction_request.accept_correction_request()
    #
    #     invoice_correction_events = Event.objects.filter(event_name='InvoiceReadyEvent')
    #     correction_invoice = Invoice.objects.filter(
    #         order_id=invoice.order.id,
    #         status=InvoiceStatus.CORRECTING,
    #     ).first()
    #     assert invoice_correction_events.count() == 1
    #
    #     properties = invoice_correction_events.first().properties
    #     assert properties['email'] == correction_invoice.order.email
    #     assert properties['order_id'] == correction_invoice.order.id
    #     assert properties['invoice_url'] == correction_invoice.pdf.url
    #     assert properties['invoice_type'] == MailingInvoiceTypeChoices.CORRECTION


@pytest.mark.django_db
class TestSampleTransactFlow:
    def test_payment_start_with_only_samples_order(
        self,
        sample_cart_order,
        api_client,
    ):
        url = reverse('order-change-status-to-pending', args=(sample_cart_order.id,))
        api_client.force_authenticate(sample_cart_order.owner)
        response = api_client.post(url, {})
        assert response.status_code == status.HTTP_200_OK

        payment_entry_event = Event.objects.filter(
            event_name='PaymentEntryEvent'
        ).last()
        assert payment_entry_event.properties['is_sample_order'] is True

    def test_payment_start_with_sample_and_furniture_order(
        self,
        sample_cart_order,
        order_item_factory,
        jetty,
        api_client,
    ):
        order_item_factory(order=sample_cart_order, order_item=jetty)

        url = reverse('order-change-status-to-pending', args=(sample_cart_order.id,))
        api_client.force_authenticate(sample_cart_order.owner)
        response = api_client.post(url, {})
        assert response.status_code == status.HTTP_200_OK

        payment_entry_event = Event.objects.filter(
            event_name='PaymentEntryEvent'
        ).last()
        assert payment_entry_event.properties['is_sample_order'] is False

    def test_order_shipped_with_sample_only(
        self,
        order_factory,
        sample_box_factory,
        order_item_factory,
        logistic_order_dto_factory,
        shipper_dto_factory,
    ):
        shipper = shipper_dto_factory(name=Shipper.FEDEX.value)
        logistic_order_dto = logistic_order_dto_factory(
            carrier=shipper.name,
            tracking_number='abc',
            shipper=shipper,
        )

        cart_order = order_factory(
            items=None,
            status=OrderStatus.CART,
            email='<EMAIL>',
            logistic_info=[logistic_order_dto],
        )
        sample_box = sample_box_factory(owner=cart_order.owner)
        order_item_factory(order=cart_order, order_item=sample_box)

        cart_order.change_status(OrderStatus.SHIPPED, should_callback_logistic=False)
        order_shipped_event = Event.objects.filter(
            event_name='OrderShippedEvent'
        ).last()
        assert order_shipped_event.properties['email'] == cart_order.email
        assert order_shipped_event.properties['is_sample_order'] is True

    def test_order_shipped_with_sample_and_furniture(
        self,
        order_factory,
        sample_box_factory,
        order_item_factory,
        shipper_dto_factory,
        logistic_order_dto_factory,
        jetty,
    ):
        shipper = shipper_dto_factory(name=Shipper.FEDEX.value)
        logistic_order_dto = logistic_order_dto_factory(
            carrier=shipper.name,
            tracking_number='abc',
            shipper=shipper,
        )

        cart_order = order_factory(
            items=None,
            status=OrderStatus.TO_BE_SHIPPED,
            email='<EMAIL>',
            logistic_info=[logistic_order_dto],
        )
        sample_box = sample_box_factory(owner=cart_order.owner)
        order_item_factory(order=cart_order, order_item=sample_box)
        order_item_factory(order=cart_order, order_item=jetty)

        cart_order.change_status(OrderStatus.SHIPPED, should_callback_logistic=False)

        order_shipped_event = Event.objects.filter(
            event_name='OrderShippedEvent'
        ).last()
        assert order_shipped_event.properties['email'] == cart_order.email
        assert order_shipped_event.properties['is_sample_order'] is False
