from datetime import timedelta
from unittest.mock import (
    PropertyMock,
    patch,
)

from django.utils import timezone

import pytest

from freezegun import freeze_time

from events.choices import BrazeSubscriptionStates


@pytest.fixture
def concrete_batch_event(event_factory):
    with patch(
        'events.tests.test_braze.braze_concretes.BrazeBatchConcrete.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['BatchAcceptedEvent']
        yield event_factory(event_name='BatchAcceptedEvent')


@pytest.fixture
def concrete_batch_two_event(event_factory):
    with patch(
        'events.tests.test_braze.braze_concretes.BrazeBatchConcreteTwo.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['BatchTwoAcceptedEvent']
        yield event_factory(event_name='BatchTwoAcceptedEvent')


@pytest.fixture
def user_attribute_event(event_factory):
    with patch(
        'events.braze.batches.BrazeUserAttributesBatch.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['UserAttributeEvent']
        yield event_factory(event_name='UserAttributeEvent')


@pytest.fixture
def braze_custom_event(event_factory):
    with patch(
        'events.braze.batches.BrazeEventsBatch.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['BrazeCustomEvent']
        yield event_factory(event_name='BrazeCustomEvent')


@pytest.fixture
def purchase_event(event_factory):
    with patch(
        'events.braze.batches.BrazePurchasesBatch.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['PurchaseEvent']
        yield event_factory(event_name='PurchaseEvent')


@pytest.fixture
def subscription_event(event_factory, settings):
    settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['TEST'] = 'some_subscription_group'
    with patch(
        'events.braze.batches.BrazeSubscriptionsBatch.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['EmailSubscriptionEvent']
        with freeze_time(timezone.now() - timedelta(minutes=4)):
            event = event_factory(
                event_name='EmailSubscriptionEvent',
                properties={
                    'subscription_group': 'some_subscription_group',
                    'subscription_state': BrazeSubscriptionStates.SUBSCRIBED,
                },
            )
        yield event


@pytest.fixture
def canvas_trigger_event(event_factory, settings):
    settings.BRAZE_CANVASES['CANVAS'] = 'canvas_id'
    with patch(
        'events.braze.batches.BrazeCanvasBatch.accepted_events',
        new_callable=PropertyMock,
    ) as batch_accepted_event_mock:
        batch_accepted_event_mock.return_value = ['CanvasTriggerEvent']
        yield event_factory(
            event_name='CanvasTriggerEvent',
            properties={'email': '<EMAIL>', 'canvas_id': 'canvas_id'},
        )
