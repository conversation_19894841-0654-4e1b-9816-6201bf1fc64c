from django.utils import timezone

import factory

from factory import fuzzy

from events.braze.objects import (
    BrazeEventObject,
    BrazePurchaseObject,
    BrazeRecipientObject,
    BrazeSubscriptionObject,
    BrazeUserAttributesObject,
)
from events.choices import BrazeSubscriptionStates
from events.models.choices import EventStatusChoices
from events.tests.test_braze.braze_concretes import BrazeObjectConcrete


class EventFactory(factory.django.DjangoModelFactory):
    user_id = fuzzy.FuzzyInteger(low=1, high=1000)
    event_name = 'TestEvent'
    created_at = fuzzy.FuzzyAttribute(timezone.now)
    properties = factory.Dict(
        params={'first_name': 'test name', 'email': '<EMAIL>'}
    )
    status = EventStatusChoices.PENDING

    class Meta:
        model = 'events.Event'


class BrazeObjectConcreteFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    email = factory.Faker('email')
    user_id = fuzzy.FuzzyInteger(low=1, high=1000)

    class Meta:
        model = BrazeObjectConcrete


class BrazeUserAttributesObjectFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    external_id = fuzzy.FuzzyText()
    first_name = fuzzy.FuzzyText()
    email = factory.Faker('email')

    class Meta:
        model = BrazeUserAttributesObject


class BrazeEventObjectFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    external_id = fuzzy.FuzzyText()
    name = fuzzy.FuzzyText()
    time = fuzzy.FuzzyAttribute(timezone.now)

    class Meta:
        model = BrazeEventObject


class BrazePurchaseObjectFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    external_id = fuzzy.FuzzyText()
    time = fuzzy.FuzzyAttribute(timezone.now)
    product_id = fuzzy.FuzzyText()
    currency = factory.Faker('currency')
    price = fuzzy.FuzzyDecimal(low=1, high=1000)

    class Meta:
        model = BrazePurchaseObject


class BrazeSubscriptionObjectFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    subscription_group_id = fuzzy.FuzzyText()
    subscription_state = fuzzy.FuzzyChoice(BrazeSubscriptionStates)
    email = factory.Faker('email')

    class Meta:
        model = BrazeSubscriptionObject


class BrazeRecipientObjectFactory(factory.Factory):
    event_id = fuzzy.FuzzyInteger(low=1, high=1000)
    canvas_id = fuzzy.FuzzyText()
    recipient = {}

    class Meta:
        model = BrazeRecipientObject
