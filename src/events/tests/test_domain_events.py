import pytest

from events.domain_events.marketing_events import MarketingEvent
from events.domain_events.transact_events import OrderSummaryRequestEvent
from events.models import Event
from events.utils import (
    get_sellable_items_sorted_ids,
    hash_normalized_string,
)


@pytest.mark.django_db
class TestMarketingEvent:
    def test_event_saved_to_db(self, user):
        event = MarketingEvent(user=user)
        database_event = Event.objects.last()

        assert Event.objects.count() == 1
        assert database_event.user_id == user.id
        assert database_event.event_name == event.__class__.__name__

    def test_event_properties_saved_to_db(self, user):
        MarketingEvent(user=user)
        database_event = Event.objects.last()

        assert database_event.properties['external_id'] == hash_normalized_string(
            user.profile.email
        )

    def test_user_not_in_event_properties(self, user):
        event = MarketingEvent(user=user)

        assert 'user' not in event.properties

    def test_set_event_external_id_from_email_if_not_provided(
        self,
        user_factory,
    ):
        user = user_factory(email='<EMAIL>')

        event = MarketingEvent(user=user)

        assert event.external_id == hash_normalized_string(user.profile.email)

    def test_set_event_external_id_from_profile_if_not_provided_and_no_user_email(
        self,
        user_factory,
    ):
        user = user_factory(email='', profile__email='<EMAIL>')
        event = MarketingEvent(user=user)

        assert not user.email
        assert event.external_id == hash_normalized_string(user.profile.email)

    def test_creates_braze_profile_if_no_external_marketing_profile_created(
        self,
        user_factory,
        region_factory,
    ):
        region = region_factory(germany=True)
        user = user_factory(
            email='<EMAIL>',
            profile__external_marketing_profile_created=False,
            profile__country='de',
            profile__first_name='Jan',
            profile__last_name='Kowalski',
            profile__language='de',
            profile__region=region,
        )
        MarketingEvent(user=user)

        assert Event.objects.filter(
            event_name='CreateExternalMarketingProfile'
        ).exists()

        create_profile_event = Event.objects.filter(
            event_name='CreateExternalMarketingProfile'
        ).last()

        expected_properties = {
            'external_id': hash_normalized_string('<EMAIL>'),
            'email': '<EMAIL>',
            'country': 'de',
            'first_name': 'Jan',
            'last_name': 'Kowalski',
            'language': 'de',
            'currency_code': region.get_currency().code,
            'registration_source': user.profile.get_registration_source_display(),
            'phone': None,
            'advertising_consents': False,
        }
        assert create_profile_event.properties == expected_properties

    def test_sets_flag_on_user_profile_after_creating_external_marketing_profile(
        self,
        user_factory,
    ):
        user = user_factory(profile__external_marketing_profile_created=False)

        MarketingEvent(user=user)

        user.refresh_from_db()

        assert Event.objects.filter(
            event_name='CreateExternalMarketingProfile'
        ).exists()
        assert user.profile.external_marketing_profile_created is True

    def test_no_profile_created_event_if_external_marketing_profile_created_set_to_true(
        self,
        user_factory,
    ):
        user = user_factory(profile__external_marketing_profile_created=True)

        MarketingEvent(user=user)

        assert not Event.objects.filter(
            event_name='CreateExternalMarketingProfile'
        ).exists()


@pytest.mark.django_db
class TestOrderSummaryRequestEvent:
    def test_event_is_not_emitted_for_the_same_order_twice(self, order):
        event_data = {
            'user': order.owner,
            'user_id': order.owner.id,
            'order_id': order.id,
            'email': order.email,
            'is_sample_order': order.contains_only_samples,
            'items_ids': get_sellable_items_sorted_ids(order),
            'force_emit': False,
        }
        OrderSummaryRequestEvent(**event_data)
        assert Event.objects.filter(event_name='OrderSummaryRequestEvent').count() == 1

        OrderSummaryRequestEvent(**event_data)
        assert Event.objects.filter(event_name='OrderSummaryRequestEvent').count() == 1

    def test_emit_event_for_two_different_orders(self, order_factory):
        order1, order2 = order_factory.create_batch(2)
        OrderSummaryRequestEvent(
            user=order1.owner,
            user_id=order1.owner.id,
            order_id=order1.id,
            email=order1.email,
            is_sample_order=order1.contains_only_samples,
            items_ids=get_sellable_items_sorted_ids(order1),
            force_emit=False,
        )
        OrderSummaryRequestEvent(
            user=order2.owner,
            user_id=order2.owner.id,
            order_id=order2.id,
            email=order2.email,
            is_sample_order=order2.contains_only_samples,
            items_ids=get_sellable_items_sorted_ids(order2),
            force_emit=False,
        )

        assert Event.objects.filter(event_name='OrderSummaryRequestEvent').count() == 2

    def test_events_is_emitted_for_the_same_order_with_force_emit(self, order):
        event_data = {
            'user': order.owner,
            'user_id': order.owner.id,
            'order_id': order.id,
            'email': order.email,
            'is_sample_order': order.contains_only_samples,
            'items_ids': get_sellable_items_sorted_ids(order),
        }
        OrderSummaryRequestEvent(**event_data, force_emit=False)
        assert Event.objects.filter(event_name='OrderSummaryRequestEvent').count() == 1

        OrderSummaryRequestEvent(**event_data, force_emit=True)
        assert Event.objects.filter(event_name='OrderSummaryRequestEvent').count() == 2
