import pytest

from events.models.choices import EventStatusChoices


@pytest.mark.django_db
class TestEvent:
    def test_handle_delivery_failure_add_retries_when_less_than_max_retries(
        self,
        event_factory,
    ):
        event = event_factory(retries=2)
        event.handle_delivery_failure()

        assert event.retries == 3

    def test_handle_delivery_failure_change_status_when_max_retries_is_reached(
        self,
        event_factory,
    ):
        event = event_factory(retries=4)
        event.handle_delivery_failure()

        assert event.status == EventStatusChoices.FAILED

    def test_handle_delivery_success_change_status(
        self,
        event,
    ):
        event.handle_delivery_success()

        assert event.status == EventStatusChoices.DONE
