import hashlib

from enum import Enum
from typing import TYPE_CHECKING

from events.choices import (
    BrazeSavedItemSubscriptionSources,
    MailingInvoiceTypeChoices,
)
from invoice.choices import InvoiceStatus
from mailing.internal_api.serializers import ServiceDateProposalEventDTO

if TYPE_CHECKING:
    from orders.models import Order


def hash_normalized_string(user_email: str) -> str:
    """Used for generating external_id for Braze"""
    return hashlib.md5((user_email.lower().encode())).hexdigest()


def map_s4l_source(source: str) -> BrazeSavedItemSubscriptionSources:
    return {
        'save for later popup': BrazeSavedItemSubscriptionSources.CLASSIC_POPUP,
        'save for later reward popup': BrazeSavedItemSubscriptionSources.REWARD_POPUP,
        'exit popup': BrazeSavedItemSubscriptionSources.EXIT_POPUP,
        'mobile exit section': BrazeSavedItemSubscriptionSources.FLOATING_POPUP,
        'wishlist': BrazeSavedItemSubscriptionSources.WISHLIST,
    }.get(source, BrazeSavedItemSubscriptionSources.CLASSIC_POPUP)


def map_invoice_status_to_type(status: int) -> MailingInvoiceTypeChoices:
    status_to_type_map = {
        InvoiceStatus.CORRECTING: MailingInvoiceTypeChoices.CORRECTION,
        InvoiceStatus.PROFORMA: MailingInvoiceTypeChoices.PROFORMA,
        InvoiceStatus.ENABLED: MailingInvoiceTypeChoices.NORMAL,
    }
    return status_to_type_map.get(status, MailingInvoiceTypeChoices.NORMAL)


def serialize_delivery_date_proposal(
    proposal: ServiceDateProposalEventDTO,
) -> dict[str, str]:
    return {
        'date': proposal.date.strftime('%d/%m/%Y'),
        'from_hour': proposal.from_hour.strftime('%H:%M'),
        'to_hour': proposal.to_hour.strftime('%H:%M'),
    }


def get_sellable_items_sorted_ids(order: 'Order') -> list:
    return sorted((item.order_item.id for item in order.items.all()))


class LogisticSMSEventTypes(Enum):
    AssemblyServiceChooseDateSMSEvent = 'AssemblyServiceChooseDateSMSEvent'
    AssemblyServiceUnconfirmedDateSMSEvent = 'AssemblyServiceUnconfirmedDateSMSEvent'
    DeliveryTimeFrameProposalSMSEvent = 'DeliveryTimeFrameProposalSMSEvent'
    Email24DeliveryDetailProposalSMSEvent = 'Email24DeliveryDetailProposalSMSEvent'
