from __future__ import annotations

from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder
from django.db import models

from events.models.choices import (
    EventSource,
    EventStatusChoices,
)
from events.models.managers import (
    EventManager,
    InternalEventManager,
)


class Event(models.Model):
    user_id = models.PositiveIntegerField(null=True)
    event_name = models.CharField(max_length=100, db_index=True)
    created_at = models.DateTimeField(auto_now_add=True)
    status = models.PositiveIntegerField(
        choices=EventStatusChoices.choices,
        default=EventStatusChoices.PENDING,
        db_index=True,
    )
    retries = models.PositiveIntegerField(default=0)
    properties = models.JSONField(default=dict, encoder=DjangoJSONEncoder)
    source = models.CharField(
        blank=True,
        choices=EventSource.choices,
        max_length=100,
    )
    exception_meta = models.JSONField(blank=True, default=dict)

    objects = EventManager()

    def handle_delivery_failure(self, save: bool = True) -> None:
        self.retries += 1
        if self.retries >= settings.BRAZE_MAX_EVENT_RETRIES:
            self.status = EventStatusChoices.FAILED

        if save:
            self.save(update_fields=['status', 'retries'])

    def handle_delivery_success(self, save: bool = True) -> None:
        self.status = EventStatusChoices.DONE
        if save:
            self.save(update_fields=['status'])

    def __str__(self):
        return f'Event: {self.event_name} - {self.status} for user {self.user_id}'


class EventInternal(Event):
    objects = InternalEventManager()

    class Meta:
        proxy = True
