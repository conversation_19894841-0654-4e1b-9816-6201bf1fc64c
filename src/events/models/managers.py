from typing import TYPE_CHECKING

if TYPE_CHECKING:
    from events.models import Event

from typing import Iterable

from django.db.models import (
    QuerySet,
    manager,
)

from events.models.choices import (
    EventSource,
    EventStatusChoices,
)


class EventManager(manager.Manager):
    def pending(self) -> QuerySet:
        return (
            self.get_queryset()
            .filter(status=EventStatusChoices.PENDING)
            .order_by('created_at')
        )

    def bulk_handle_delivery_failure(self, events: Iterable['Event']) -> None:
        for obj in events:
            obj.handle_delivery_failure(save=False)

        self.bulk_update(events, ['status', 'retries'])

    def bulk_handle_delivery_success(self, events: Iterable['Event']) -> None:
        for obj in events:
            obj.handle_delivery_success(save=False)

        self.bulk_update(events, ['status'])


class InternalEventManager(EventManager):
    def get_queryset(self):
        return super().get_queryset().filter(source=EventSource.INTERNAL)
