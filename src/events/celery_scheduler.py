from datetime import timedelta

from celery.schedules import crontab

events_tasks_scheduler = {
    'process_events': {
        'task': 'events.tasks.process_events',
        'schedule': timedelta(seconds=10),
    },
    'clean_old_events': {
        'task': 'events.tasks.clean_old_events',
        'schedule': crontab(hour='22', minute='0'),
    },
    'emit_delivery_anniversary_events': {
        'task': 'events.tasks.emit_delivery_anniversary_events',
        'schedule': crontab(hour='9', minute='0'),
    },
    'emit_win_back_120_events': {
        'task': 'events.tasks.emit_win_back_120_events',
        'schedule': crontab(hour='9', minute='0'),
    },
    'emit_win_back_300_events': {
        'task': 'events.tasks.emit_win_back_300_events',
        'schedule': crontab(hour='9', minute='0'),
    },
}

events_tasks_scheduler_dev = {
    'process_events': {
        'task': 'events.tasks.process_events',
        'schedule': timedelta(minutes=1),
    },
    'clean_old_events': {
        'task': 'events.tasks.clean_old_events',
        'schedule': crontab(hour='22', minute='0'),
    },
}
