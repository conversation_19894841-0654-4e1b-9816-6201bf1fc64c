import logging

from abc import (
    ABC,
    abstractmethod,
)
from collections import defaultdict
from dataclasses import dataclass
from datetime import timed<PERSON><PERSON>
from itertools import (
    chain,
    zip_longest,
)
from typing import (
    ClassVar,
    Generator,
    Optional,
    Type,
)

from django.conf import settings
from django.db.models import QuerySet
from django.utils import timezone

from requests import RequestException

from custom.api_clients.braze import B<PERSON><PERSON><PERSON>
from events.braze.batches import (
    BrazeBatchAbstract,
    BrazeCanvasBatch,
    BrazeEventsBatch,
    BrazePurchasesBatch,
    BrazeSubscriptionsBatch,
    BrazeUserAttributesBatch,
)
from events.braze.exceptions import BrazeBatchFullError
from events.braze.objects import (
    BrazeEventObject,
    BrazePurchaseObject,
    BrazeUserAttributesObject,
)
from events.choices import BrazeSubscriptionStates
from events.models import Event

logger = logging.getLogger('cstm')


@dataclass
class BrazeUserTrackBatchGroup:
    """A helper class to group together batches with user tracking events"""

    user_attributes_batch: Optional[BrazeUserAttributesBatch] = None
    events_batch: Optional[BrazeEventsBatch] = None
    purchases_batch: Optional[BrazePurchasesBatch] = None

    @property
    def events(self):
        return chain.from_iterable(
            [
                getattr(self.user_attributes_batch, 'events', []),
                getattr(self.events_batch, 'events', []),
                getattr(self.purchases_batch, 'events', []),
            ]
        )

    @property
    def attribute_objects(self) -> list[BrazeUserAttributesObject]:
        return getattr(self.user_attributes_batch, 'braze_objects', [])

    @property
    def event_objects(self) -> list[BrazeEventObject]:
        return getattr(self.events_batch, 'braze_objects', [])

    @property
    def purchase_objects(self) -> list[BrazePurchaseObject]:
        return getattr(self.purchases_batch, 'braze_objects', [])


class BrazeBatchProcessorAbstract(ABC):
    batch_classes: ClassVar[list[Type[BrazeBatchAbstract]]]

    def __init__(
        self,
        queryset: Optional[QuerySet] = None,
    ) -> None:
        self.queryset = queryset or Event.objects.pending()

    def process_events(self) -> None:
        """Pack events into batches and send to Braze"""
        events = self._get_accepted_events()
        for batch in self._prepare_batches(events):
            self._process_batch(batch)

    @abstractmethod
    def _send_to_braze(
        self,
        batch: BrazeBatchAbstract,
    ) -> Optional[list[int]]:
        """Send a batch filled with events to Braze"""

    def _get_accepted_events(self) -> QuerySet:
        """Filter events accepted by batch classes"""
        accepted_event_names = []
        for batch_class in self.batch_classes:
            accepted_event_names += batch_class.accepted_events

        return self.queryset.filter(event_name__in=accepted_event_names)

    def _prepare_batches(
        self,
        events: QuerySet[Event],
        *args,
        **kwargs,
    ) -> Generator[BrazeBatchAbstract, None, None]:
        """Pack already filtered events into batches

        Events here are not ordered and that's why we initially create a batch instance
        for each event_name in queryset. When iterating over events we add event
        to appropriate batch based on event_name property. When batch is full - create
        a new batch instance in place of previous and add event to newly created batch.
        After iteration ends, filter out empty batch instances and yield all
        non-empty batches.
        """
        event_name_to_batch_map = {
            event_name: batch_class(*args, **kwargs)
            for batch_class in self.batch_classes
            for event_name in batch_class.accepted_events
        }

        for event in events:
            event_name = event.event_name
            batch = event_name_to_batch_map.get(event_name)
            try:
                batch.add_event(event)
            except BrazeBatchFullError:
                yield batch
                batch = batch.__class__(*args, **kwargs)
                event_name_to_batch_map[event_name] = batch
                batch.add_event(event)

        yield from filter(bool, event_name_to_batch_map.values())

    def _process_batch(self, batch: BrazeBatchAbstract) -> None:
        """Try sending batches to Braze.

        Mark events as success of failed respectively.
        """
        try:
            self._send_to_braze(batch)
        except RequestException as e:
            error_msg = e
            args = ()
            if e.response:
                error_msg = 'Request to Braze failed with status %s. Response: %s'
                args = (e.response.status_code, e.response.text)

            logger.error(error_msg, *args, exc_info=True)
            Event.objects.bulk_handle_delivery_failure(batch.events)
        else:
            Event.objects.bulk_handle_delivery_success(batch.events)


class BrazeTrackingEventsProcessor(BrazeBatchProcessorAbstract):
    batch_classes = [BrazeUserAttributesBatch, BrazeEventsBatch, BrazePurchasesBatch]

    def _send_to_braze(self, batch_group: BrazeUserTrackBatchGroup) -> list[int]:
        return BrazeClient.send_user_tracking_data(
            attribute_objects=batch_group.attribute_objects,
            event_objects=batch_group.event_objects,
            purchase_objects=batch_group.purchase_objects,
        )

    def _prepare_batches(
        self,
        events: QuerySet[Event],
        *args,
        **kwargs,
    ) -> Generator[BrazeUserTrackBatchGroup, None, None]:
        """Group batches into BrazeUserTrackBatchGroup

        Some payload consists of multiple types of batches. To communicate efficiently
        we pack each type of batches to the maximum allowed limit.

        For example if we want to prepare user tracking data a valid payload consists of
        batches of three types: user attributes, events and purchases.

        So for a valid payload we yield all three types of batches each time - even
        when one type is empty.
        """
        batches = super()._prepare_batches(events)

        sorted_batches_dict = {batch_class: [] for batch_class in self.batch_classes}
        for batch in batches:
            sorted_batches_dict[batch.__class__].append(batch)

        zipped = zip_longest(*sorted_batches_dict.values())
        for grouped_batches in zipped:
            yield BrazeUserTrackBatchGroup(*grouped_batches)

    def _process_batch(self, batch_group: BrazeUserTrackBatchGroup) -> None:
        """Try sending batches to Braze.

        Due to endpoint specification, we get in return the ids of failed events.
        Map events ids to `batch.events` and mark as failed or successful
        respectively.
        """
        try:
            failed_events_ids = set(self._send_to_braze(batch_group))
        except RequestException as e:
            logger.error(e, exc_info=True)
            Event.objects.bulk_handle_delivery_failure(batch_group.events)
        else:
            events_dict = defaultdict(list)
            for event in batch_group.events:
                if event.id in failed_events_ids:
                    events_dict['failed_events'].append(event)
                else:
                    events_dict['successful_events'].append(event)

            Event.objects.bulk_handle_delivery_failure(events_dict['failed_events'])
            Event.objects.bulk_handle_delivery_success(events_dict['successful_events'])


class BrazeSubscriptionsProcessor(BrazeBatchProcessorAbstract):
    batch_classes = [BrazeSubscriptionsBatch]

    def _send_to_braze(self, batch: BrazeSubscriptionsBatch) -> None:
        BrazeClient.update_subscriptions(
            subscription_group=batch.subscription_group,
            subscription_state=batch.subscription_state,
            subscriptions_objects=batch.braze_objects,
        )

    def _prepare_batches(
        self,
        events: QuerySet[Event],
        *args,
        **kwargs,
    ) -> Generator[BrazeBatchAbstract, None, None]:
        """Pack already filtered events into batches

        Create batches with each of the subscription group and subscription state,
        and pack this batches with appropriate events.
        """
        subscription_groups = [
            *settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS.values(),
            *settings.BRAZE_SMS_SUBSCRIPTION_GROUPS.values(),
        ]
        for subscription_group in subscription_groups:
            for subscription_state in BrazeSubscriptionStates.values:
                filtered_events = events.filter(
                    properties__subscription_state=subscription_state,
                    properties__subscription_group=subscription_group,
                )
                if filtered_events:
                    batches = super()._prepare_batches(
                        filtered_events,
                        subscription_state=subscription_state,
                        subscription_group=subscription_group,
                    )
                    yield from batches

    def _get_accepted_events(self) -> QuerySet:
        """Filter events accepted by batch classes which are older than 3 minutes.

        In case of subscription events Braze requires to make a delay
        of at least 2 minutes to fully create user profile.
        So we don't want to process events from last 3 minutes.
        """
        accepted_events = super()._get_accepted_events()
        delta = timedelta(minutes=3)
        return accepted_events.filter(created_at__lte=timezone.now() - delta)


class BrazeCanvasesProcessor(BrazeBatchProcessorAbstract):
    batch_classes = [BrazeCanvasBatch]

    def _prepare_batches(
        self,
        events: QuerySet[Event],
        *args,
        **kwargs,
    ) -> Generator[BrazeBatchAbstract, None, None]:
        for canvas_id in settings.BRAZE_CANVASES.values():
            filtered_events = events.filter(properties__canvas_id=canvas_id)
            yield from super()._prepare_batches(
                filtered_events,
                canvas_id=canvas_id,
            )

    def _send_to_braze(self, batch: BrazeCanvasBatch) -> None:
        BrazeClient.trigger_canvas(
            canvas_id=batch.canvas_id,
            braze_objects=batch.braze_objects,
        )
