import abc

from dataclasses import (
    asdict,
    dataclass,
)
from typing import (
    Any,
    Optional,
)

from django.conf import settings

from events.choices import (
    BrazeSubscriptionStates,
    BrazeSubscriptionTypes,
)
from events.models import Event
from events.utils import hash_normalized_string


@dataclass(frozen=True, eq=False)
class BrazeObjectAbstract(abc.ABC):
    event_id: int

    def __hash__(self) -> int:
        return hash(self.event_id)

    def __eq__(self, other: 'BrazeObjectAbstract') -> bool:
        return isinstance(self, other.__class__) and self.event_id == other.event_id

    def to_dict(self) -> dict:
        data = asdict(self)
        data.pop('event_id')
        if data.get('advertising_consents'):
            data.pop('advertising_consents')
            data['$google_ad_user_data'] = True
            data['$google_ad_personalization'] = True

        return self._filter_empty_values(data)

    @staticmethod
    def _filter_empty_values(data: dict) -> dict:
        return {key: value for key, value in data.items() if value not in (None, '')}

    @classmethod
    @abc.abstractmethod
    def from_event(cls, event: Event) -> Optional['BrazeObjectAbstract']:
        raise NotImplementedError


@dataclass(frozen=True, eq=False)
class BrazeUserAttributesObject(BrazeObjectAbstract):
    external_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    email_subscribe: Optional[BrazeSubscriptionStates] = None
    phone: Optional[str] = None
    user_id: Optional[int] = None
    _update_existing_only: Optional[bool] = None
    push_token_import: Optional[bool] = None
    registration_source: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    currency_code: Optional[str] = None
    subscription_source: Optional[str] = None
    marketing_excludes: Optional[dict[str, Any]] = None
    is_b2b: Optional[bool] = None
    last_furniture_purchase_date: Optional[str] = None
    last_sample_purchase_date: Optional[str] = None
    last_viewed_furniture_id: Optional[int] = None
    last_viewed_furniture_type: Optional[str] = None
    postal_code: Optional[str] = None
    last_order_surface: Optional[float] = None
    contact_phone: Optional[str] = None  # custom attribute for contacting customers
    _merge_objects: bool = False
    dev_env: str = None
    advertising_consents: bool = None
    lp_sofa_teaser: Optional[bool] = False

    @classmethod
    def from_event(cls, event: Event) -> Optional['BrazeUserAttributesObject']:
        marketing_excludes = cls._pop_marketing_excludes_from_properties(
            event.properties
        )

        if country := event.properties.get('country'):
            event.properties['country'] = country.upper()

        return cls(
            event_id=event.id,
            external_id=event.properties.pop('external_id', None),
            user_id=event.user_id,
            marketing_excludes=marketing_excludes,
            _merge_objects=bool(marketing_excludes),
            dev_env=settings.SITE_URL if settings.IS_DEV else None,
            **event.properties,
        )

    @staticmethod
    def _pop_marketing_excludes_from_properties(
        properties: dict,
    ) -> Optional[dict[str, Any]]:
        """Pop marketing excludes from properties"""
        marketing_excludes_time_fields = (
            'last_furniture_delivery_date',
            'last_finished_complaint_date',
        )
        marketing_excludes_keys = (
            'last_furniture_order_status',
            'complaint_active',
            *marketing_excludes_time_fields,
        )
        marketing_excludes = {}
        for key in marketing_excludes_keys:
            value = properties.pop(key, None)
            if value is not None:
                if key in marketing_excludes_time_fields:
                    value = {'$time': value}  # for Braze to recognize the date type

                marketing_excludes[key] = value

        return marketing_excludes or None


@dataclass(frozen=True, eq=False)
class BrazeEventObject(BrazeObjectAbstract):
    external_id: str
    name: str
    time: str
    properties: Optional[dict] = None
    _update_existing_only: Optional[bool] = None
    app_id: Optional[str] = settings.BRAZE_APP_ID

    @classmethod
    def from_event(cls, event: Event) -> 'BrazeEventObject':
        return cls(
            event_id=event.id,
            external_id=event.properties.pop('external_id', None),
            name=event.event_name,
            time=event.created_at.isoformat(),
            properties=event.properties,
            _update_existing_only=event.properties.get('_update_existing_only'),
        )


@dataclass(frozen=True, eq=False)
class BrazePurchaseObject(BrazeObjectAbstract):
    external_id: str
    time: str
    product_id: str
    currency: str
    price: float
    properties: Optional[dict] = None
    _update_existing_only: bool = True
    app_id: Optional[str] = settings.BRAZE_APP_ID

    @classmethod
    def from_event(cls, event: Event) -> 'BrazePurchaseObject':
        properties = event.properties
        return cls(
            event_id=event.id,
            external_id=properties.pop('external_id', None),
            time=properties.get('time'),
            product_id=properties.get('product_id'),
            currency=properties.get('currency'),
            price=float(properties.get('price', 0)),
            properties={
                'items': properties.get('items'),
                'estimated_delivery_date': properties.get('estimated_delivery_date'),
                'estimated_delivery_range': properties.get('estimated_delivery_range'),
            },
        )


@dataclass(frozen=True, eq=False)
class BrazeSubscriptionObject(BrazeObjectAbstract):
    subscription_group_id: str
    subscription_state: str
    email: Optional[str] = None
    phone: Optional[str] = None

    @classmethod
    def from_event(cls, event: Event) -> 'BrazeSubscriptionObject':
        return cls(
            event_id=event.id,
            subscription_group_id=event.properties.get('subscription_group'),
            subscription_state=event.properties.get('subscription_state'),
            email=event.properties.get('email'),
            phone=event.properties.get('phone'),
        )

    def get_subscription_value(self, subscription_type: BrazeSubscriptionTypes) -> str:
        return (
            self.email
            if subscription_type == BrazeSubscriptionTypes.EMAIL
            else self.phone
        )


@dataclass(frozen=True, eq=False)
class BrazeRecipientObject(BrazeObjectAbstract):
    canvas_id: str
    recipient: dict

    @classmethod
    def from_event(cls, event: Event) -> 'BrazeRecipientObject':
        email = event.properties.pop('email', None)
        canvas_id = event.properties.pop('canvas_id', None)
        attributes = {'email': email}
        if event.properties.pop('employee_notification', None):
            attributes['service_employee'] = True

        recipient = {
            'external_user_id': hash_normalized_string(email),
            'attributes': attributes,
            'canvas_entry_properties': event.properties,
            'send_to_existing_only': False,
        }

        return cls(
            event_id=event.id,
            canvas_id=canvas_id,
            recipient=recipient,
        )
