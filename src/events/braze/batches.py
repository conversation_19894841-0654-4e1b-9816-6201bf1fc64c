import logging

from abc import ABC
from typing import (
    ClassVar,
    Type,
)

from events.braze.exceptions import BrazeBatchFullError
from events.braze.objects import (
    BrazeEventObject,
    BrazeObjectAbstract,
    BrazePurchaseObject,
    BrazeRecipientObject,
    BrazeSubscriptionObject,
    BrazeUserAttributesObject,
)
from events.choices import (
    BrazeEventClasses,
    BrazeSubscriptionStates,
)
from events.models import Event

logger = logging.getLogger('cstm')


class BrazeBatchAbstract(ABC):
    max_batch_len: ClassVar[int]
    braze_object_type: ClassVar[Type[BrazeObjectAbstract]]
    accepted_events: ClassVar[list[str]]

    def __init__(self):
        self.braze_objects = []
        self.events = []

    def __len__(self):
        return len(self.braze_objects)

    def __bool__(self):
        return bool(self.braze_objects)

    def add_event(self, event: Event) -> None:
        if self.is_full:
            raise BrazeBatchFullError

        braze_object = self.braze_object_type.from_event(event)
        self.braze_objects.append(braze_object)
        self.events.append(event)

    @property
    def is_full(self):
        return len(self) == self.max_batch_len


class BrazeSubscriptionsBatch(BrazeBatchAbstract):
    max_batch_len = 50
    braze_object_type = BrazeSubscriptionObject
    accepted_events = [
        BrazeEventClasses.EMAIL_SUBSCRIPTION,
        BrazeEventClasses.SMS_SUBSCRIPTION,
    ]

    def __init__(
        self,
        subscription_group: str,
        subscription_state: BrazeSubscriptionStates,
    ) -> None:
        super().__init__()
        self.subscription_group = subscription_group
        self.subscription_state = subscription_state


class BrazeUserAttributesBatch(BrazeBatchAbstract):
    max_batch_len = 75
    braze_object_type = BrazeUserAttributesObject
    accepted_events = [
        BrazeEventClasses.MARKETING_EXCLUDE_UPDATE,
        BrazeEventClasses.CREATE_PROFILE,
        BrazeEventClasses.REGION_UPDATE,
        BrazeEventClasses.LANGUAGE_UPDATE,
        BrazeEventClasses.EMAIL_SUBSCRIPTION_STATUS_UPDATE,
        BrazeEventClasses.SMS_SUBSCRIPTION_STATUS_UPDATE,
        BrazeEventClasses.PURCHASE_ATTRIBUTES_UPDATE,
        BrazeEventClasses.B2B_UPDATE,
        BrazeEventClasses.CONTACT_DATA_UPDATE,
        BrazeEventClasses.ORDER_SURFACE_CALCULATION,
        BrazeEventClasses.FURNITURE_VIEW,
        BrazeEventClasses.ORDER_UPDATE_PHONE_EVENT,
    ]


class BrazeEventsBatch(BrazeBatchAbstract):
    max_batch_len = 75
    braze_object_type = BrazeEventObject
    accepted_events = [
        BrazeEventClasses.CHECKOUT_ENTRY,
        BrazeEventClasses.CHECKOUT_FULFILLED,
        BrazeEventClasses.CART_UPDATE,
        BrazeEventClasses.CART_EMPTY,
        BrazeEventClasses.ORDER_SHIPPED,
        BrazeEventClasses.ORDER_DELIVERED,
        BrazeEventClasses.SAVED_ITEM,
        BrazeEventClasses.WISHLIST_EMPTY,
        BrazeEventClasses.PAYMENT_ENTRY,
        BrazeEventClasses.PAYMENT_FAILED,
        BrazeEventClasses.INVOICE_READY,
        BrazeEventClasses.PASSWORD_RESET_REQUEST,
        BrazeEventClasses.DELIVERY_ANNIVERSARY,
        BrazeEventClasses.WIN_BACK_120,
        BrazeEventClasses.WIN_BACK_300,
        BrazeEventClasses.ORDER_SUMMARY_REQUEST,
        BrazeEventClasses.RESEND_PAYMENT_CONFIRMATION_REQUEST,
        BrazeEventClasses.DELIVERY_DATE_PROPOSAL_REQUEST,
        BrazeEventClasses.DELIVERY_DATE_PROPOSAL_REMINDER_REQUEST,
        BrazeEventClasses.SERVICE_DELIVERY_DATE_CHOSEN,
        BrazeEventClasses.SERVICE_DELIVERY_NEW_DATE_REQUEST,
        BrazeEventClasses.DELIVERY_TIME_FRAME_PROPOSAL_READY,
        BrazeEventClasses.DELIVERY_SETTLED,
        BrazeEventClasses.EMAIL24_DELIVERY_PROPOSAL_READY,
        BrazeEventClasses.EMAIL24_DELIVERY_CONFIRMATION_READY,
        BrazeEventClasses.COMPLAINT_READY_TO_BE_SHIPPED,
        BrazeEventClasses.COMPLAINT_SHIPPED,
        BrazeEventClasses.ORDER_READY_TO_BE_SHIPPED,
        BrazeEventClasses.PRODUCT_PASSPORT_READY,
        BrazeEventClasses.WISHLIST_ADD,
        BrazeEventClasses.ASSEMBLY_SERVICE_CHOOSE_DATE_SMS,
        BrazeEventClasses.ASSEMBLY_SERVICE_UNCONFIRMED_DATE_SMS,
        BrazeEventClasses.DELIVERY_TIME_FRAME_PROPOSAL_SMS,
        BrazeEventClasses.EMAIL24_DELIVERY_DETAIL_PROPOSALS_SMS,
    ]


class BrazePurchasesBatch(BrazeBatchAbstract):
    max_batch_len = 75
    braze_object_type = BrazePurchaseObject
    accepted_events = [BrazeEventClasses.PURCHASE]


class BrazeCanvasBatch(BrazeBatchAbstract):
    max_batch_len = 50
    braze_object_type = BrazeRecipientObject
    accepted_events = [
        BrazeEventClasses.ACCEPTED_REQUEST_TO_PROVIDER,
        BrazeEventClasses.DELIVERY_TIME_FRAME_DATE_CHANGED_REQUEST,
        BrazeEventClasses.DELIVERY_TIME_FRAME_NEW_DATES_REQUEST,
        BrazeEventClasses.EMAIL24_LOGISTIC_CONFIRMATION_READY,
    ]

    def __init__(self, canvas_id):
        super().__init__()
        self.canvas_id: str = canvas_id
        self.objects: list[dict] = []
