import abc

from dataclasses import dataclass
from typing import (
    Optional,
    Union,
)

from django.conf import settings
from django.contrib.auth.models import User

from custom.api_clients.braze import BrazeClient
from events.choices import (
    BrazeSubscriptionGroupStatus,
    BrazeSubscriptionStates,
)
from events.domain_events.marketing_events import (
    EmailSubscriptionEvent,
    EmailSubscriptionStatusUpdateEvent,
    SmsSubscriptionEvent,
    SmsSubscriptionStatusUpdateEvent,
)
from user_profile.choices import SubscriptionSources
from user_profile.services.subscription_logic import is_subscription_override_forbidden


@dataclass
class EmailSubscriptionData:
    email: str
    source: str
    group: str
    group_state: BrazeSubscriptionStates
    global_state: Optional[BrazeSubscriptionStates] = None

    @property
    def is_newsletter(self) -> bool:
        return self.group == settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter']

    @classmethod
    def create_for_newsletter(
        cls, email: str, source: SubscriptionSources
    ) -> 'EmailSubscriptionData':
        return cls(
            group=settings.BRAZE_EMAIL_SUBSCRIPTION_GROUPS['newsletter'],
            group_state=BrazeSubscriptionStates.SUBSCRIBED,
            email=email,
            source=source,
            global_state=BrazeSubscriptionStates.OPTED_IN,
        )


@dataclass
class SmsSubscriptionData:
    source: str
    phone_prefix: str
    phone_number: str
    group_state: BrazeSubscriptionStates

    @property
    def full_phone(self) -> str:
        return f'{self.phone_prefix}{self.phone_number}'

    @property
    def group(self) -> Optional[str]:
        return settings.BRAZE_SMS_SUBSCRIPTION_GROUPS.get(self.phone_prefix, None)


class BaseSubscriptionEventHandler(abc.ABC):
    """
    Base class for managing email/sms subscriptions in Braze.

    This class performs the following tasks:
    1. Checks if a user is already subscribed to a given group.
    2. Updates user data and global subscription state in Braze.
    3. Subscribes the user to a given subscription.
    4. Makes necessary data update on User and UserProfile models.
    """

    def __init__(
        self,
        user: User,
        external_id: str,
        subscription_data: Union[EmailSubscriptionData, SmsSubscriptionData],
        check_subscription: bool = True,
        create_new_profile: bool = True,
    ):
        self.user = user
        self.external_id = external_id
        self.profile = user.profile
        self.subscription_data = subscription_data
        self.check_subscription = check_subscription
        self.create_new_profile = create_new_profile

    def handle(self) -> None:
        subscription_status = BrazeSubscriptionGroupStatus.UNKNOWN
        if self.check_subscription:
            subscription_status = self._check_subscription_status()

        if is_subscription_override_forbidden(
            status=subscription_status, source=self.subscription_data.source
        ):
            return

        self._emit_subscription_status_update_event()
        self._emit_subscription_event()
        self._update_user_data()

    @abc.abstractmethod
    def _check_subscription_status(self) -> BrazeSubscriptionGroupStatus:
        pass

    @abc.abstractmethod
    def _emit_subscription_status_update_event(self) -> None:
        pass

    @abc.abstractmethod
    def _emit_subscription_event(self) -> None:
        pass

    @abc.abstractmethod
    def _update_user_data(self) -> None:
        pass


class EmailSubscriptionEventHandler(BaseSubscriptionEventHandler):
    def _check_subscription_status(self) -> BrazeSubscriptionGroupStatus:
        return BrazeClient.get_email_subscription_group_status(
            self.subscription_data.group,
            self.subscription_data.email,
        )

    def _emit_subscription_status_update_event(self) -> None:
        EmailSubscriptionStatusUpdateEvent(
            user=self.user,
            email=self.subscription_data.email,
            external_id=self.external_id,
            email_subscribe=self.subscription_data.global_state,
            subscription_source=self.subscription_data.source,
            lp_sofa_teaser=(
                self.subscription_data.source == SubscriptionSources.LP_SOFA_TEASER
            ),
        )

    def _emit_subscription_event(self) -> None:
        EmailSubscriptionEvent(
            user=self.user,
            subscription_group=self.subscription_data.group,
            subscription_state=self.subscription_data.group_state,
            email=self.subscription_data.email,
        )

    def _update_user_data(self) -> None:
        profile_update_fields = ()
        if self.subscription_data.is_newsletter:
            self.profile.newsletter_agreed = True
            profile_update_fields += ('newsletter_agreed',)

        if not self.user.email and not self.profile.email:
            email = self.subscription_data.email
            self.user.email = email
            self.profile.email = email

            self.user.save(update_fields=('email',))
            self.profile.save(update_fields=profile_update_fields)


class SmsSubscriptionEventHandler(BaseSubscriptionEventHandler):
    def _check_subscription_status(self) -> BrazeSubscriptionGroupStatus:
        return BrazeClient.get_sms_subscription_group_status(
            self.subscription_data.group,
            self.subscription_data.full_phone,
        )

    def _emit_subscription_status_update_event(self) -> None:
        SmsSubscriptionStatusUpdateEvent(
            user=self.user,
            external_id=self.external_id,
            phone=self.subscription_data.full_phone,
            subscription_source=self.subscription_data.source,
            create_external_marketing_profile=self.create_new_profile,
            lp_sofa_teaser=(
                self.subscription_data.source == SubscriptionSources.LP_SOFA_TEASER
            ),
        )

    def _emit_subscription_event(self) -> None:
        SmsSubscriptionEvent(
            user=self.user,
            subscription_group=self.subscription_data.group,
            subscription_state=self.subscription_data.group_state,
            phone=self.subscription_data.full_phone,
        )

    def _update_user_data(self) -> None:
        if not self.profile.phone:
            self.profile.phone = self.subscription_data.phone_number
            self.profile.phone_prefix = self.subscription_data.phone_prefix
            self.profile.save(update_fields=('phone', 'phone_prefix'))
