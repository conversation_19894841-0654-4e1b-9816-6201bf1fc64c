from django.db import models


class BrazeSubscriptionStates(models.TextChoices):
    SUBSCRIBED = 'subscribed'
    OPTED_IN = 'opted_in'
    UNSUBSCRIBED = 'unsubscribed'


class BrazeSubscriptionGroupStatus(models.TextChoices):
    SUBSCRIBED = 'Subscribed'
    UNSUBSCRIBED = 'Unsubscribed'
    UNKNOWN = 'Unknown'

    @classmethod
    def _missing_(cls, value):
        return cls.UNKNOWN


class BrazeSubscriptionTypes(models.TextChoices):
    EMAIL = 'email'
    PHONE = 'phone'


class BrazeEventClasses(models.TextChoices):
    EMAIL_SUBSCRIPTION = 'EmailSubscriptionEvent'
    SMS_SUBSCRIPTION = 'SmsSubscriptionEvent'
    MARKETING_EXCLUDE_UPDATE = 'MarketingExcludeUpdateEvent'
    PURCHASE = 'PurchaseEvent'
    CHECKOUT_ENTRY = 'CheckoutEntryEvent'
    CHECKOUT_FULFILLED = 'CheckoutFulfilledEvent'
    CART_UPDATE = 'CartUpdateEvent'
    CART_EMPTY = 'CartEmptyEvent'
    ORDER_SHIPPED = 'OrderShippedEvent'
    ORDER_DELIVERED = 'OrderDeliveredEvent'
    SAVED_ITEM = 'SavedItemEvent'
    CREATE_PROFILE = 'CreateExternalMarketingProfile'
    REGION_UPDATE = 'RegionUpdateEvent'
    LANGUAGE_UPDATE = 'LanguageUpdateEvent'
    EMAIL_SUBSCRIPTION_STATUS_UPDATE = 'EmailSubscriptionStatusUpdateEvent'
    SMS_SUBSCRIPTION_STATUS_UPDATE = 'SmsSubscriptionStatusUpdateEvent'
    PURCHASE_ATTRIBUTES_UPDATE = 'PurchaseAttributesUpdateEvent'
    B2B_UPDATE = 'B2BUpdateEvent'
    WISHLIST_EMPTY = 'WishlistEmptyEvent'
    PAYMENT_ENTRY = 'PaymentEntryEvent'
    CONTACT_DATA_UPDATE = 'UserContactDataUpdateEvent'
    PAYMENT_FAILED = 'PaymentFailedEvent'
    INVOICE_READY = 'InvoiceReadyEvent'
    PASSWORD_RESET_REQUEST = 'ChangePasswordRequestEvent'
    DELIVERY_ANNIVERSARY = 'DeliveryAnniversaryEvent'
    WIN_BACK_120 = 'WinBack120Event'
    WIN_BACK_300 = 'WinBack300Event'
    ORDER_SUMMARY_REQUEST = 'OrderSummaryRequestEvent'
    RESEND_PAYMENT_CONFIRMATION_REQUEST = 'ResendPaymentConfirmationRequestEvent'
    ACCEPTED_REQUEST_TO_PROVIDER = 'AcceptedProviderRequestEvent'
    DELIVERY_DATE_PROPOSAL_REQUEST = 'DeliveryDateProposalRequestEvent'
    DELIVERY_DATE_PROPOSAL_REMINDER_REQUEST = 'DeliveryDateProposalReminderRequestEvent'
    SERVICE_DELIVERY_DATE_CHOSEN = 'ServiceDeliveryDateChosenEvent'
    SERVICE_DELIVERY_NEW_DATE_REQUEST = 'ServiceDeliveryNewDateRequestEvent'
    DELIVERY_TIME_FRAME_PROPOSAL_READY = 'DeliveryTimeFrameProposalReadyEvent'
    DELIVERY_SETTLED = 'DeliverySettledEvent'
    DELIVERY_TIME_FRAME_DATE_CHANGED_REQUEST = (
        'DeliveryTimeFrameDateChangedRequestEvent'
    )
    DELIVERY_TIME_FRAME_NEW_DATES_REQUEST = 'DeliveryTimeFrameNewDateRequestEvent'
    EMAIL24_DELIVERY_PROPOSAL_READY = 'Email24DeliveryProposalReadyEvent'
    EMAIL24_DELIVERY_CONFIRMATION_READY = 'Email24DeliveryConfirmationReadyEvent'
    EMAIL24_LOGISTIC_CONFIRMATION_READY = 'Email24LogisticConfirmationReadyEvent'
    COMPLAINT_READY_TO_BE_SHIPPED = 'ComplaintReadyToBeShippedEvent'
    COMPLAINT_SHIPPED = 'ComplaintShippedEvent'
    ORDER_READY_TO_BE_SHIPPED = 'OrderReadyToBeShippedEvent'
    ORDER_SURFACE_CALCULATION = 'OrderSurfaceCalculationEvent'
    PRODUCT_PASSPORT_READY = 'ProductPassportReadyEvent'
    FURNITURE_VIEW = 'FurnitureViewEvent'
    WISHLIST_ADD = 'WishlistAddEvent'
    ASSEMBLY_SERVICE_CHOOSE_DATE_SMS = 'AssemblyServiceChooseDateSMSEvent'
    ASSEMBLY_SERVICE_UNCONFIRMED_DATE_SMS = 'AssemblyServiceUnconfirmedDateSMSEvent'
    DELIVERY_TIME_FRAME_PROPOSAL_SMS = 'DeliveryTimeFrameProposalSMSEvent'
    EMAIL24_DELIVERY_DETAIL_PROPOSALS_SMS = 'Email24DeliveryDetailProposalSMSEvent'
    ORDER_UPDATE_PHONE_EVENT = 'OrderUpdatePhoneEvent'


class BrazeSavedItemSubscriptionSources(models.TextChoices):
    CLASSIC_POPUP = 's4l_classic_pop_up'
    REWARD_POPUP = 's4l_classic_pop_up_reward'
    EXIT_POPUP = 's4l_exit_pop_up'
    FLOATING_POPUP = 's4l_floating_pop_up'
    WISHLIST = 'wishlist'


class MailingInvoiceTypeChoices(models.TextChoices):
    CORRECTION = 'correction'
    NORMAL = 'normal'
    PROFORMA = 'proforma'
