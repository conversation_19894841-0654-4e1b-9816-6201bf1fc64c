from dataclasses import (
    dataclass,
    field,
)
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    Optional,
)

from django.utils import timezone

from events.choices import MailingInvoiceTypeChoices
from events.domain_events.marketing_events import MarketingEvent
from events.models import Event

if TYPE_CHECKING:
    from orders.models import Order


@dataclass
class PurchaseEvent(MarketingEvent):
    """Successful payment received."""

    email: str  # email assigned to order
    order: 'Order'
    product_id: str = 'Tylko Purchase'
    time: str = field(init=False)
    estimated_delivery_date: str = field(init=False)
    estimated_delivery_range: str = field(init=False)
    price: Decimal = field(init=False)
    currency: str = field(init=False)
    items: list[dict] = field(init=False)

    def __post_init__(self):
        from orders.serializers import MailingPurchaseOrderItemSerializer

        self.time = timezone.now().isoformat(timespec='minutes')
        self.estimated_delivery_date = (
            self.order.get_delivery_time_with_offset().strftime('%d.%m.%Y')
        )
        self.estimated_delivery_range = self.order.delivery_range_formatted
        self.price = self.order.region_total_price
        self.currency = getattr(self.order.currency, 'code', 'EUR')

        serializer = MailingPurchaseOrderItemSerializer(
            self.order.material_items.all(),
            many=True,
        )
        # serializer.data is a list of OrderedDicts, we need simple data types
        self.items = [dict(data) for data in serializer.data]

        super().__post_init__()

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties.pop('order')
        return properties


@dataclass
class PurchaseAttributesUpdateEvent(MarketingEvent):
    """User made a purchase.

    Update purchase related attributes on an external marketing profile.
    """

    last_sample_purchase_date: Optional[str] = None
    last_furniture_purchase_date: Optional[str] = None


@dataclass
class PaymentEntryEvent(MarketingEvent):
    """User submitted payment information or proceeded to external payment page.

    Note:
        'email' parameter should come from `Order.email`.
    """

    order_id: int
    email: str
    user_id: int
    is_sample_order: bool
    create_login_access_token: bool = True
    create_external_marketing_profile: bool = field(init=False)

    def __post_init__(self) -> None:
        # Create a new external marketing profile if user entered different email
        # during checkout.
        self.create_external_marketing_profile = self.user.email != self.email
        super().__post_init__()


@dataclass
class PaymentFailedEvent(MarketingEvent):
    """Payment failed from whatever reason.

    Note:
        'email' parameter should come from `Order.email`.
    """

    order_id: int
    email: str


@dataclass
class InvoiceReadyEvent(MarketingEvent):
    """Invoice is ready for order.

    Note:
        'email' parameter should come from `Order.email`.
    """

    order_id: int
    email: str
    invoice_type: MailingInvoiceTypeChoices
    # TODO: make sure that invoice_url is a valid URL without expiration
    invoice_url: str
    is_klarna: bool


@dataclass
class OrderSummaryRequestEvent(MarketingEvent):
    """Send email with order summary to user."""

    order_id: int
    email: str
    user_id: int
    is_sample_order: bool
    items_ids: list
    force_emit: bool = False
    create_login_access_token: bool = True

    def __post_init__(self):
        # Emit event only for modified orders, unless force_emit is set to True.
        events_emitted_for_order = Event.objects.filter(
            event_name=self.__class__.__name__,
            properties__order_id=self.order_id,
            properties__items_ids=self.items_ids,
        )

        if self.force_emit or not events_emitted_for_order.exists():
            super().__post_init__()

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties.pop('force_emit')
        return properties


@dataclass
class ResendPaymentConfirmationRequestEvent(MarketingEvent):
    """CS employee requested to resend email with payment confirmation to user."""

    email: str
    order_id: int
    is_sample_order: bool
