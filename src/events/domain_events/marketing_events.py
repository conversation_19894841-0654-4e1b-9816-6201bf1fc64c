import datetime
import urllib.parse

from dataclasses import (
    dataclass,
    field,
)
from typing import Optional

from django.conf import settings
from django.urls import reverse

from custom.enums import Furniture
from events.choices import BrazeSubscriptionStates
from events.domain_events.base_events import DomainEventBase
from events.utils import hash_normalized_string


@dataclass
class MarketingEvent(DomainEventBase):
    """Base class for marketing related events.

    If email is not explicitly provided, get email from user.
    Creates database event only if it has email or phone attribute,
    otherwise do nothing.
    """

    external_id: str = field(init=False)
    lat: Optional[str] = field(init=False)

    def __post_init__(self) -> None:
        self.lat = self._get_login_access_token()
        self.external_id = self._get_external_id()

        if self.external_id:
            if self.should_create_marketing_profile:
                self._create_external_marketing_profile()

            super().__post_init__()

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties.pop('create_external_marketing_profile', None)
        properties.pop('create_login_access_token', None)

        if not self.lat:
            properties.pop('lat')

        return properties

    @property
    def should_create_marketing_profile(self) -> bool:
        return (
            getattr(self, 'create_external_marketing_profile', None)
            or not self.user.profile.external_marketing_profile_created
        )

    def _get_login_access_token(self) -> str:
        from user_profile.models import LoginAccessToken

        return (
            LoginAccessToken.get_or_create_for_user(self.user)
            if getattr(self, 'create_login_access_token', None)
            else None
        )

    def _get_external_id(self) -> Optional[str]:
        """Get external_id directly from events or based on email address.

        To enable Braze on dev envs, we need to add SITE_URL to external_id.
        """

        prefix = ''
        if settings.IS_DEV:
            prefix = settings.SITE_URL

        if email := self._get_email():
            external_id = hash_normalized_string(email)
        elif hasattr(self, 'external_id'):
            external_id = getattr(self, 'external_id', None)
        elif self.user.username:
            external_id = self.user.username
        else:
            return None

        return f'{prefix}{external_id}'

    def _get_email(self) -> Optional[str]:
        return (
            getattr(self, 'email', None)
            or self.user.email
            or getattr(self.user.profile, 'email', None)
        )

    def _create_external_marketing_profile(self) -> None:
        """Create an external profile in Braze.

        Also set `UserProfile.external_marketing_profile_created` to True.
        """
        profile = self.user.profile

        CreateExternalMarketingProfile(
            user=self.user,
            external_id=self.external_id,
            email=self._get_email(),
            phone=getattr(self, 'phone', None) or profile.full_phone,
            first_name=getattr(self, 'first_name', profile.first_name),
            last_name=getattr(self, 'last_name', profile.last_name),
            country=getattr(self, 'country', profile.country),
            language=getattr(self, 'language', profile.language),
            registration_source=getattr(
                self,
                'registration_source',
                profile.get_registration_source_display(),
            ),
            currency_code=getattr(
                self,
                'currency_code',
                profile.get_region().get_currency().code,
            ),
            advertising_consents=(
                profile.additional_data.get('advertising_consents', False)
            ),
        )

        profile.external_marketing_profile_created = True
        profile.save(update_fields=('external_marketing_profile_created',))


@dataclass
class CreateExternalMarketingProfile(DomainEventBase):
    """User has no external marketing profile.

    Create one with a set of basic attributes needed for a proper email marketing.
    """

    external_id: str
    first_name: Optional[str] = None
    last_name: Optional[str] = None
    email: Optional[str] = None
    registration_source: Optional[str] = None
    country: Optional[str] = None
    language: Optional[str] = None
    currency_code: Optional[str] = None
    phone: Optional[str] = None
    advertising_consents: Optional[bool] = None


@dataclass
class RegistrationEvent(MarketingEvent):
    """User created an account."""

    create_external_marketing_profile: bool = True


@dataclass
class RegionUpdateEvent(MarketingEvent):
    """User changed region."""

    country: Optional[str] = None
    language: Optional[str] = None
    currency_code: Optional[str] = None


@dataclass
class LanguageUpdateEvent(MarketingEvent):
    """User changed language."""

    language: Optional[str] = None


@dataclass
class EmailSubscriptionEvent(MarketingEvent):
    """User subscribed to some kind of newsletter."""

    subscription_group: str
    subscription_state: str
    email: str


@dataclass
class SmsSubscriptionEvent(DomainEventBase):
    subscription_group: Optional[str] = None
    subscription_state: Optional[str] = None
    phone: Optional[str] = None


@dataclass
class EmailSubscriptionStatusUpdateEvent(MarketingEvent):
    """User's newsletter subscription has changed."""

    external_id: Optional[str] = None
    email: Optional[str] = None
    email_subscribe: Optional[BrazeSubscriptionStates] = None
    subscription_source: Optional[str] = None
    create_external_marketing_profile: bool = True

    # Custom
    lp_sofa_teaser: bool = False

    @property
    def properties(self) -> dict:
        properties = super().properties
        if self.lp_sofa_teaser is False:
            properties.pop('lp_sofa_teaser')
        return properties


@dataclass
class SmsSubscriptionStatusUpdateEvent(MarketingEvent):
    """User's SMS subscription has changed."""

    external_id: Optional[str] = None
    subscription_source: Optional[str] = None
    phone: Optional[str] = None
    create_external_marketing_profile: bool = True

    # Custom
    lp_sofa_teaser: bool = False

    @property
    def properties(self) -> dict:
        properties = super().properties
        if self.lp_sofa_teaser is False:
            properties.pop('lp_sofa_teaser')
        return properties


@dataclass
class UserContactDataUpdateEvent(MarketingEvent):
    """Some of the user's contact data got updated."""

    first_name: str
    last_name: str
    phone_number: str
    phone_prefix: str
    postal_code: str
    contact_phone: str = field(init=False)
    phone: str = field(init=False)

    def __post_init__(self):
        self.contact_phone = self.phone = f'{self.phone_prefix}{self.phone_number}'
        super().__post_init__()

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties.pop('phone_number')
        properties.pop('phone_prefix')
        return properties


@dataclass
class MarketingExcludeUpdateEvent(MarketingEvent):
    """
    Attributes for marketing exclude.

    Prepare nested object with attributes necessary to temporary exclude customers from
    further marketing.
    """

    last_furniture_delivery_date: Optional[datetime.datetime] = None
    last_furniture_order_status: Optional[str] = None
    complaint_active: Optional[bool] = None
    last_finished_complaint_date: Optional[datetime.datetime] = None


@dataclass
class B2BUpdateEvent(MarketingEvent):
    """User profile has been marked as b2b."""

    is_b2b: Optional[bool] = None


@dataclass
class CheckoutEntryEvent(MarketingEvent):
    """User entered checkout."""

    cart_id: int
    create_login_access_token: bool = True


@dataclass
class CheckoutFulfilledEvent(MarketingEvent):
    """User finished checkout and was redirected to payment."""

    order_id: int
    create_login_access_token: bool = True


@dataclass
class CartUpdateEvent(MarketingEvent):
    """User added/removed item from Cart."""

    cart_id: int
    total_price: int
    is_sample_box: bool
    create_login_access_token: bool = True


@dataclass
class CartEmptyEvent(MarketingEvent):
    """User removed last item from Cart."""


@dataclass
class SavedItemEvent(MarketingEvent):
    """User entered email in Save For Later pop-up."""

    email: str
    user_id: int
    furniture_id: int
    furniture_type: Furniture
    create_external_marketing_profile: bool = True
    create_login_access_token: bool = True


@dataclass
class WishlistAddEvent(MarketingEvent):
    """Logged-in user added item to wishlist."""

    user_id: int
    furniture_id: int
    furniture_type: Furniture
    create_login_access_token: bool = True


@dataclass
class WishlistEmptyEvent(MarketingEvent):
    """User deleted last item from wishlist."""

    user_id: int


@dataclass
class DeliveryAnniversaryEvent(MarketingEvent):
    """Exactly 1 year passed after order with furniture was delivered."""

    email: str


@dataclass
class WinBack120Event(MarketingEvent):
    """Exactly 120 days passed after order with furniture was delivered."""

    email: str


@dataclass
class WinBack300Event(MarketingEvent):
    """Exactly 300 days passed after order with furniture was delivered."""

    email: str


@dataclass
class ChangePasswordRequestEvent(MarketingEvent):
    """User requested a password change."""

    email: str
    password_reset_token: str
    password_reset_url: str = field(init=False)

    def __post_init__(self):
        self.password_reset_url = urllib.parse.urljoin(
            settings.SITE_URL,
            reverse('front-changepassword-token', args=(self.password_reset_token,)),
        )
        super().__post_init__()

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties.pop('password_reset_token')
        return properties


@dataclass
class OrderSurfaceCalculationEvent(MarketingEvent):
    """Update external marketing profile with 'last_order_surface' attribute.

    Used for targeting.
    """

    email: str
    last_order_surface: float


@dataclass
class ProductPassportReadyEvent(MarketingEvent):
    """All data needed for product passport email is ready."""

    email: str
    order_id: int


@dataclass
class FurnitureViewEvent(MarketingEvent):
    """User enters PDP.

    Update external marketing profile with information about last viewed furniture.
    """

    last_viewed_furniture_id: int
    last_viewed_furniture_type: Furniture


@dataclass
class OrderUpdatePhoneEvent(MarketingEvent):
    """Update external marketing profile with phone attribute."""

    email: str
    phone: str


@dataclass
class OrderUpdateDeliveryTimeFrameIDEvent(MarketingEvent):
    """Update external marketing profile with delivery time frame id attribute.
    Used for sending link with dtf via SMS.
    """

    email: str
    delivery_time_frame_id: int
