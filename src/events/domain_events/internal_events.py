import logging
import traceback

from dataclasses import (
    asdict,
    dataclass,
)
from typing import Union

from events.models import Event
from events.models.choices import (
    EventSource,
    EventStatusChoices,
)

logger = logging.getLogger('cstm')


@dataclass
class InternalEventBase:
    """Internal domain events - no user required.
    Used to synchronize objects between our services

    Handles saving created events to DB.
    """

    def __post_init__(self) -> Union[dict, None]:
        db_event = self._create_db_event()
        try:
            with_response = self.execute()
        except Exception as e:
            db_event.status = EventStatusChoices.FAILED
            db_event.exception_meta = {
                'exception_type': str(e),
                'exception_traceback': traceback.print_exc(),
            }
            db_event.save(update_fields=['status', 'exception_meta'])
            logger.exception('Exception %s %s', self.__class__.__name__, str(e))
            raise
        else:
            db_event.status = EventStatusChoices.DONE
            db_event.save(update_fields=['status'])
            return with_response

    @property
    def properties(self) -> dict:
        """Extend this method to add or remove data."""
        return asdict(self)

    def _create_db_event(self) -> 'Event':
        return Event.objects.create(
            event_name=self.__class__.__name__,
            properties=self.properties,
            user_id=self.user.id if getattr(self, 'user', None) else None,
            status=EventStatusChoices.PENDING,
            source=EventSource.INTERNAL,
        )

    def execute(self):
        raise NotImplementedError
