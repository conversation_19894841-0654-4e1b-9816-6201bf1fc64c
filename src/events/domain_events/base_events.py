from dataclasses import (
    asdict,
    dataclass,
)

from django.contrib.auth.models import User

from events.models import Event


@dataclass
class DomainEventBase:
    """Base class for all domain events.

    Handles saving created events to DB.
    """

    user: User

    def __post_init__(self) -> None:
        self._create_db_event()

    @property
    def properties(self) -> dict:
        """Extend this method to add or remove data."""
        properties = asdict(self)
        properties.pop('user')
        return properties

    def _create_db_event(self) -> None:
        Event.objects.create(
            event_name=self.__class__.__name__,
            user_id=self.user.id,
            properties=self.properties,
        )
