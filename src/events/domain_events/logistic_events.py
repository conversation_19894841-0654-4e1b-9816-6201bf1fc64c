import abc
import logging

from dataclasses import (
    dataclass,
    field,
)
from datetime import (
    datetime,
    time,
)
from decimal import Decimal
from typing import (
    TYPE_CHECKING,
    List,
    Optional,
)

from django.conf import settings

from events.domain_events.base_events import DomainEventBase
from events.domain_events.marketing_events import MarketingEvent
from events.utils import (
    LogisticSMSEventTypes,
    serialize_delivery_date_proposal,
)
from mailing.internal_api.serializers import ServiceDateProposalEventDTO

if TYPE_CHECKING:
    from custom.internal_api.enums import ProposalType

logger = logging.getLogger('cstm')


@dataclass
class OrderReadyToBeShippedEvent(MarketingEvent):
    """Order is ready to be shipped."""

    email: str
    order_id: int
    order_pretty_id: int
    packages: list
    total_weight: str
    is_ups_shipper: bool


@dataclass
class OrderShippedEvent(MarketingEvent):
    """Order is shipped."""

    email: str
    order_id: int
    order_pretty_id: int
    is_sample_order: bool
    carrier: str
    tracking_number: str
    tracking_link: str


@dataclass
class OrderDeliveredEvent(MarketingEvent):
    """Order is delivered."""

    email: str
    order_id: int
    typeform_url: str
    is_sample_order: bool
    has_assembly: bool
    postal_code: str
    furniture_category: str


@dataclass
class AcceptedProviderRequestEvent(DomainEventBase):
    """Accepted request to AS or Complaint providers.

    Send notification to service employee about accepted request.
    """

    service_type: str
    logistic_order_id: int
    email: str
    employee_notification: bool = True
    canvas_id: str = field(init=False)

    def __post_init__(self):
        canvas_name = f'{self.service_type}_service_notification'
        self.canvas_id = settings.BRAZE_CANVASES.get(canvas_name, None)
        if not self.canvas_id:
            logger.error(
                f'Missing Braze Canvas ID for {canvas_name}.'
                ' Notification cant be sent.'
            )

        super().__post_init__()


@dataclass
class DeliveryDateProposalRequestEvent(MarketingEvent):
    """Delivery date proposal request for user is ready."""

    order_id: int
    email: str
    service_id: str
    service_type: str
    duration: Decimal
    proposals: List[ServiceDateProposalEventDTO]
    expiration_date: Optional[datetime]
    expiration_time: Optional[time]

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties['duration'] = int(self.duration)
        properties['expiration_date'] = (
            self.expiration_date.strftime('%d/%m/%Y') if self.expiration_date else None
        )
        properties['expiration_time'] = (
            self.expiration_time.strftime('%H:%M') if self.expiration_time else None
        )
        properties['proposals'] = [
            serialize_delivery_date_proposal(proposal) for proposal in self.proposals
        ]
        return properties


@dataclass
class DeliveryDateProposalReminderRequestEvent(MarketingEvent):
    """Delivery date proposal reminder for user is ready."""

    email: str
    order_id: int
    service_id: str
    service_type: str
    proposals: List[ServiceDateProposalEventDTO]

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties['proposals'] = [
            serialize_delivery_date_proposal(proposal) for proposal in self.proposals
        ]
        return properties


@dataclass
class ServiceDeliveryDateChosenEvent(MarketingEvent):
    """User confirmed the date for the assembly/complaint service."""

    email: str
    order_id: str
    service_type: str
    accepted_proposal: ServiceDateProposalEventDTO

    @property
    def properties(self) -> dict:
        properties = super().properties
        properties['accepted_proposal'] = serialize_delivery_date_proposal(
            self.accepted_proposal
        )
        return properties


@dataclass
class ServiceDeliveryNewDateRequestEvent(MarketingEvent):
    """Last delivery date proposal was not chosen, new one is ready to be sent."""

    email: str
    order_id: int
    service_id: str
    service_type: str


@dataclass
class DeliveryTimeFrameProposalReadyEvent(MarketingEvent):
    """Delivery time frame proposal is ready to be sent to client."""

    email: str
    order_id: int
    dtf_id: int
    proposal_type: 'ProposalType'
    is_email24_sent: bool
    minimal_timeslots: int
    create_login_access_token: bool = True


@dataclass
class DeliverySettledEvent(MarketingEvent):
    """Delivery settled and confirmation email is ready to be sent to client."""

    email: str
    order_id: int
    date: str
    start_hour: str
    end_hour: str


@dataclass
class DeliveryTimeFrameNotificationAbstract(DomainEventBase, abc.ABC):
    """Base class for delivery time frame notifications."""

    email: str
    order_ids: str
    manufacturer: str
    employee_notification: bool = True

    @property
    @abc.abstractmethod
    def canvas_id(self):
        pass


@dataclass
class DeliveryTimeFrameDateChangedRequestEvent(DeliveryTimeFrameNotificationAbstract):
    """Delivery dates were changed.

    Notify logistic recipients about changed dates.
    """

    canvas_id: str = settings.BRAZE_CANVASES['logistic_date_change_notification']


@dataclass
class DeliveryTimeFrameNewDateRequestEvent(DeliveryTimeFrameNotificationAbstract):
    """New delivery dates are available.

    Notify logistic recipients about new dates.
    """

    canvas_id: str = settings.BRAZE_CANVASES['logistic_new_dates_notification']


@dataclass
class Email24DeliveryProposalReadyEvent(MarketingEvent):
    """Email24 proposal is ready to be sent to client."""

    email: str
    order_id: str
    email24_lat: str


@dataclass
class Email24DeliveryConfirmationReadyEvent(MarketingEvent):
    """Email24 confirmation is ready to be sent to client."""

    email: str
    order_id: str
    email24_lat: str


@dataclass
class Email24LogisticConfirmationReadyEvent(DomainEventBase):
    """Email24 confirmation for logistic is ready."""

    email: str
    order_id: int
    manufacturer: str
    email24_id: int
    email24_elevator_floor_info: str
    email24_note: str
    employee_notification: bool = True
    canvas_id: str = settings.BRAZE_CANVASES['email24_logistic_notification']


@dataclass
class ComplaintReadyToBeShippedEvent(MarketingEvent):
    """Complaint is ready to be sent to client."""

    email: str
    order_id: int
    availability_question: bool


@dataclass
class ComplaintShippedEvent(MarketingEvent):
    """Complaint is shipped with the appropriate carrier."""

    email: str
    order_id: int
    carrier: str
    tracking_number: str
    tracking_link: str


@dataclass
class AssemblyServiceChooseDateSMSEvent(MarketingEvent):
    """Notification SMS to check email and choose assembly service date"""

    email: str
    logistic_order_id: int
    phone: str


@dataclass
class AssemblyServiceUnconfirmedDateSMSEvent(MarketingEvent):
    """Reminder SMS to respond for suggested assembly date"""

    email: str
    logistic_order_id: int
    phone: str


@dataclass
class DeliveryTimeFrameProposalSMSEvent(MarketingEvent):
    """Order ready for ship. SMS reminder about choosing delivery date"""

    email: str
    logistic_order_id: int
    phone: str
    dtf_id: int


@dataclass
class Email24DeliveryDetailProposalSMSEvent(MarketingEvent):
    """Order ready for ship. Request SMS for more information"""

    email: str
    logistic_order_id: int
    phone: str
    email24_url: str


LOGISTIC_SMS_EVENTS_MAPPING = {
    LogisticSMSEventTypes.AssemblyServiceChooseDateSMSEvent: AssemblyServiceChooseDateSMSEvent,  # noqa E501
    LogisticSMSEventTypes.AssemblyServiceUnconfirmedDateSMSEvent: AssemblyServiceUnconfirmedDateSMSEvent,  # noqa E501
    LogisticSMSEventTypes.DeliveryTimeFrameProposalSMSEvent: DeliveryTimeFrameProposalSMSEvent,  # noqa E501
    LogisticSMSEventTypes.Email24DeliveryDetailProposalSMSEvent: Email24DeliveryDetailProposalSMSEvent,  # noqa E501
}
