# Generated by Django 4.1.9 on 2023-08-28 09:32

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('events', '0002_alter_event_user_id'),
    ]

    operations = [
        migrations.CreateModel(
            name='EventInternal',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('events.event',),
        ),
        migrations.AddField(
            model_name='event',
            name='source',
            field=models.CharField(
                blank=True, choices=[('internal', 'Internal')], max_length=100
            ),
        ),
    ]
