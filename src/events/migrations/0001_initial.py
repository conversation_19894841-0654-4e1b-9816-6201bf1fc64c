# Generated by Django 3.2.12 on 2022-05-26 13:49

import django.core.serializers.json

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = []

    operations = [
        migrations.CreateModel(
            name='Event',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('user_id', models.PositiveIntegerField()),
                ('event_name', models.CharField(db_index=True, max_length=100)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'status',
                    models.PositiveIntegerField(
                        choices=[(1, 'Pending'), (2, 'Done'), (3, 'Failed')],
                        db_index=True,
                        default=1,
                    ),
                ),
                ('retries', models.PositiveIntegerField(default=0)),
                (
                    'properties',
                    models.<PERSON><PERSON><PERSON>ield(
                        default=dict,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
            ],
        ),
    ]
