min_estimated_delivery_time_per_type = '''select row_to_json(row)
       from (select o.paid_at::date,
       MIN(o.estimated_delivery_time::date)              as min_delivery,
       MAX(o.estimated_delivery_time::date)              as max_delivery,
       o.estimated_delivery_time::date - o.paid_at::date as days,
       j.material,
       j.shelf_type,
       case
           when json_array_length(j.doors::json)::integer > 0
               and json_array_length(j.drawers::json)::integer > 0
               then 'drawers'
           when json_array_length(j.doors::json)::integer > 0 then 'doors'
           when json_array_length(j.drawers::json)::integer > 0 then 'drawers'
           else 'none'
           end as door_or_drawers,
       count(o.id) as orders_count,
       json_agg(o.id)::varchar as orders
from gallery_jetty j
         join orders_orderitem i
              on j.id = i.object_id and
              (select count(*) as x from orders_orderitem
                where order_id = i.order_id) = 1
         join orders_order o on i.order_id = o.id
where o.paid_at is not null
group by 1, 4, 5, 6, 7
order by 1 desc
    ) as row;'''
