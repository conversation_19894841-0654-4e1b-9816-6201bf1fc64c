from django.conf import settings
from django.db import models
from django.utils import timezone

from kpi.choices import (
    CategoryForCosts,
    CountriesMarketing,
)


class KPIValueField(models.JSONField):
    def from_db_value(self, value, expression, connection):
        from kpi.kpis import KPIValueObject

        value = super().from_db_value(value, expression, connection)
        if isinstance(value, KPIValueObject):
            return value
        else:
            return KPIValueObject.from_json(value)

    def get_db_prep_value(self, value, connection, prepared=False):
        kvo_json = value.to_json() if value is not None else None
        return super().get_db_prep_value(kvo_json, connection, prepared=prepared)


class KPIValue(models.Model):
    kpi_name = models.CharField(max_length=128, db_index=True)
    bi_type = models.CharField(max_length=32, db_index=True)
    region = models.CharField(db_index=True, max_length=32, blank=True, null=True)
    segment_beginning = models.DateTimeField(db_index=True)
    segment_end = models.DateTimeField(db_index=True)
    utms = models.CharField(max_length=512)
    utms_hash = models.CharField(max_length=32, db_index=True)
    value = KPIValueField()
    generated_at = models.DateTimeField(auto_now_add=True)

    class Meta(object):
        unique_together = (
            'kpi_name',
            'bi_type',
            'utms_hash',
            'segment_beginning',
            'segment_end',
            'region',
        )

    def __str__(self):
        return 'KPIValue[kpi_name="%s" bi_type=%s beginning="%s" end="%s" value=%s]' % (
            self.kpi_name,
            self.bi_type,
            self.segment_beginning,
            self.segment_end,
            self.value,
        )


class MarketingNonperformanceCost(models.Model):
    name = models.CharField(max_length=64)
    date_at = models.DateField(default=timezone.now)
    description = models.CharField(max_length=256, blank=True, null=True)
    cost_amount = models.DecimalField(max_digits=12, decimal_places=2)
    countries = models.CharField(
        choices=CountriesMarketing.choices, null=True, blank=True, max_length=70
    )
    category = models.CharField(
        choices=CategoryForCosts.choices, null=True, blank=True, max_length=70
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )

    def biq_query_export(self):
        data = dict(self.__dict__)
        data['author'] = self.author.username
        for key in ['id', '_state', '_author_cache', 'author_id']:
            if key in data:
                del data[key]
        return data
