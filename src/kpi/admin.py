from django.contrib import admin

from admin_customization.admin import ButtonActionsBaseAdmin
from kpi.importers import (
    MarketingNonperformanceCostExporter,
    MarketingNonperformanceCostImporter,
)
from kpi.models import (
    KPIValue,
    MarketingNonperformanceCost,
)
from production_margins.admin_mixins import (
    CSVExportActionMixin,
    CSVImportActionMixin,
)


class KPIValueAdmin(admin.ModelAdmin):
    date_hierarchy = 'segment_end'
    list_display = (
        'kpi_name',
        'bi_type',
        'region',
        'utms',
        'segment_beginning',
        'segment_end',
        'value',
        'generated_at',
    )
    list_filter = (
        'kpi_name',
        'bi_type',
        'region',
    )


admin.site.register(KPIValue, KPIValueAdmin)


class MarketingNonperformanceCostAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    date_hierarchy = 'date_at'
    list_display = ('name', 'cost_amount', 'date_at', 'author', 'countries', 'category')
    raw_id_fields = ('author',)
    search_fields = (
        'name',
        'author__username',
    )
    list_filter = ('countries', 'category')

    model = MarketingNonperformanceCost

    actions = ('export_to_csv',)
    button_actions = ('import_from_csv',)

    @admin.action(description='Export selected items as CSV')
    def export_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset=queryset,
            filename='marketing-nonperformance-costs',
            exporter_class=MarketingNonperformanceCostExporter,
        )

    @admin.action(description='Import from CSV')
    def import_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            MarketingNonperformanceCostImporter,
        )


admin.site.register(MarketingNonperformanceCost, MarketingNonperformanceCostAdmin)
