import hashlib
import inspect
import logging
import sys

from functools import lru_cache
from urllib.parse import quote

from django.utils.encoding import force_bytes

from kpi import kpis

logger = logging.getLogger('cstm')


@lru_cache(typed=True)
def get_derived_class_by_name(module, base_class, class_name):
    classes = [
        c
        for c in get_all_derived_classes(module, base_class)
        if c.__name__ == class_name
    ]
    return classes[0] if len(classes) > 0 else None


@lru_cache(typed=True)
def get_all_derived_classes(module, base_class):
    return [
        i[1]
        for i in inspect.getmembers(
            sys.modules[module],
            lambda c: inspect.isclass(c)
            and issubclass(c, base_class)
            and c != base_class,
        )
    ]


def create_kpiview_cache_key(request, path=None):
    path = path if path is not None else request.path
    request_params = {k: v for k, v in list(request.GET.items()) if k != 'regenerating'}
    path_hash = hashlib.md5(force_bytes(quote(path))).hexdigest()
    params_hash = hash(frozenset(list(request_params.items())))
    cache_key = 'kpisview_%s_%s' % (path_hash, params_hash)
    return cache_key


def get_kpi_class(kpi_name):
    modules_with_kpis = ['customer_service', 'kpi', 'orders']
    for module_name in modules_with_kpis:
        kpi_class = get_derived_class_by_name(
            '{}.kpis'.format(module_name), kpis.KPIType, kpi_name
        )
        if kpi_class:
            return kpi_class
    return None
