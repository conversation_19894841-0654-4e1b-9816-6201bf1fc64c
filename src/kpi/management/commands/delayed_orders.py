from collections import OrderedDict

from django.core.management.base import BaseCommand

from kpi.big_query import (
    bigquery,
    upload_list_to_big_query,
)
from orders.models import Order

DATA_SPACE = 'kpi'
DATA_TABLE = 'delayed_orders'


class Command(BaseCommand):
    def send_data_to_big_query(self, all_data):
        upload_list_to_big_query(
            DATA_SPACE,
            DATA_TABLE,
            all_data,
            write=bigquery.WriteDisposition.WRITE_TRUNCATE,
        )

    def fetch_orders(self):
        return Order.objects.locker_studio_delayed_orders()

    def extract_order_data(self, order):
        logistics_orders = order.logistic_info
        products = order.product_set.all()

        order_data = OrderedDict()
        order_data['order_id'] = order.pk
        order_data['order_status'] = order.get_status_display()
        order_data['order_notes'] = order.notes
        order_data['assembly'] = order.assembly
        order_data['estimated_delivery_time'] = order.estimated_delivery_time.strftime(
            '%Y-%m-%d'
        )
        order_data['should_be_produced'] = order.should_be_produced
        order_data['days_since_expected_delivery'] = order.days_since_expected_delivery
        order_data['items'] = order.items.count()
        order_data['cs_link'] = f'https://tylko.com/cs/user_overview/{order.owner.id}/'

        # Logistics data
        order_data['logistics_orders'] = len(logistics_orders)
        order_data['logistics_carrier'] = [
            {
                'logistics_order_id': logistics_order.id,
                'carrier': logistics_order.carrier,
            }
            for logistics_order in logistics_orders
        ]
        order_data['logistics_assembly_type'] = [
            {
                'logistics_order_id': logistics_order.id,
                'assembly_type': logistics_order.assembly_type,
            }
            for logistics_order in logistics_orders
        ]
        order_data['logistics_link'] = (
            f'https://logistic.tylko.com/admin/logistic/logisticorder/?paid_at=all&q='
            f'{order.id}'
        )
        order_data['logistics_sent_to_customer'] = [
            {
                'logistics_order_id': logistics_order.id,
                'sent_to_customer': logistics_order.sent_to_customer.strftime(
                    '%Y-%m-%d'
                )
                if logistics_order.sent_to_customer
                else '',
            }
            for logistics_order in logistics_orders
        ]
        order_data['logistics_delivered_date'] = [
            {
                'logistics_order_id': logistics_order.id,
                'delivered_date': logistics_order.delivered_date.strftime('%Y-%m-%d')
                if logistics_order.delivered_date
                else '',
            }
            for logistics_order in logistics_orders
        ]

        # Product data
        order_data['products'] = len(products)
        order_data['products_statuses'] = [
            {'product_id': p.id, 'status': p.get_status_display()} for p in products
        ]

        return order_data

    def handle(self, *args, **options):
        orders = self.fetch_orders()
        data_for_report = [self.extract_order_data(order) for order in orders]
        self.send_data_to_big_query(data_for_report)
