import abc
import calendar
import csv
import json
import logging
import time

from collections import namedtuple
from datetime import (
    datetime,
    timedelta,
)
from decimal import Decimal
from functools import wraps
from io import StringIO
from urllib.parse import quote_plus

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.db.models.aggregates import (
    Count,
    Sum,
)
from django.db.models.expressions import (
    F,
    Value,
)
from django.db.models.functions import Coalesce
from django.db.models.query_utils import Q
from django.db.utils import IntegrityError
from django.template import loader
from django.utils import timezone
from django.utils.safestring import mark_safe

import numpy

from facebook_business.adobjects.adaccount import AdAccount
from facebook_business.api import FacebookAdsApi
from facebook_business.exceptions import FacebookRequestError
from past.utils import old_div

from custom.utils.google_analytics import google_analytics_funnel_fetch
from kpi.constants import (
    KPI_INTERVAL_DAILY,
    KPI_INTERVAL_MONTHLY,
    KPI_INTERVAL_WEEKLY,
    KPI_INTERVAL_YEARLY,
    KPI_TYPE_ALL,
    KPI_TYPE_FACEBOOK,
    KPI_TYPE_GOOGLE,
    KPI_TYPE_GOOGLE_ANALYTICS,
    KPI_TYPE_ORGANIC,
    KPI_TYPE_UTM,
)
from kpi.models import (
    KPIValue,
    MarketingNonperformanceCost,
)
from kpi.tasks import task_kpis_generate_value
from orders.choices import OrderSource
from orders.models import Order
from user_profile.choices import UserType
from user_profile.models import UserProfile
from vouchers.enums import VoucherOrigin
from vouchers.models import Voucher

logger = logging.getLogger('cstm')


########################################
#               HELPERS                #
########################################


class KPITimeSegments(object):
    # TODO _segments into objects.

    def __init__(self, interval, time_span, labels=tuple()):
        self.interval = interval
        self.time_span = time_span
        self.labels = labels
        self._generate_segments()

    def _calculate_td_args(self):
        if self.interval == KPI_INTERVAL_DAILY:
            self._td_designation = 'days'
            self._td_multiplier = 1
            self._td_subinterval = 'seconds'
        elif self.interval == KPI_INTERVAL_WEEKLY:
            self._td_designation = 'weeks'
            self._td_multiplier = 1
            self._td_subinterval = 'days'
        elif self.interval == KPI_INTERVAL_MONTHLY:
            self._td_designation = 'weeks'
            self._td_multiplier = 4
            self._td_subinterval = 'days'
        elif self.interval == KPI_INTERVAL_YEARLY:
            self._td_designation = 'weeks'
            self._td_multiplier = 48
            self._td_subinterval = 'days'

    def _find_inverval_frame(self, time_segment_n):
        now = timezone.now()
        if not now.tzinfo:
            now = timezone.make_aware(now)
        # if self.interval == KPITimeSegments.INTERVAL_WEEKLY:
        #     #if its week based, lets take week from friday to thursday.
        #     now = now + relativedelta(weekday=FR(-1))
        segment_date = now - timedelta(
            **{self._td_designation: self._td_multiplier * time_segment_n}
        )
        beginning, end = segment_date, segment_date
        day_start = timezone.make_aware(
            datetime(segment_date.year, segment_date.month, segment_date.day)
        )
        if self.interval == KPI_INTERVAL_DAILY:
            beginning = day_start
            end = beginning + timedelta(**{self._td_designation: self._td_multiplier})
        elif self.interval == KPI_INTERVAL_WEEKLY:
            beginning = day_start - timedelta(days=segment_date.weekday())
            end = beginning + timedelta(**{self._td_designation: self._td_multiplier})
            # if end.month > beginning.month:
            #     end = timezone.make_aware(datetime(end.year, end.month, 1))
        elif self.interval == KPI_INTERVAL_MONTHLY:
            beginning = day_start - timedelta(days=day_start.day - 1)
            end = beginning + timedelta(
                days=calendar.monthrange(day_start.year, day_start.month)[1]
            )
        elif self.interval == KPI_INTERVAL_YEARLY:
            beginning = day_start - timedelta(days=day_start.timetuple().tm_yday - 1)
            end = beginning + timedelta(**{self._td_designation: self._td_multiplier})
        return beginning, end

    def _generate_segments(self):
        self._calculate_td_args()
        self._segments = []
        for time_segment_n in range(0, self.time_span):
            segment_beginning, segment_end = self._find_inverval_frame(time_segment_n)
            # Days in month hack because universe.
            if (
                self.interval == KPI_INTERVAL_MONTHLY
                and len(self._segments) > 0
                and segment_beginning.month == self._segments[-1][0].month
            ):
                segment_beginning, segment_end = self._find_inverval_frame(
                    time_segment_n + 1
                )
            self._segments.append((segment_beginning, segment_end))
        self._segments = list(reversed(self._segments))
        return self._segments

    def get_date_format(self, for_template=True):
        result = '%Y-%m-%d'
        if self.interval == KPI_INTERVAL_DAILY:
            result = '%Y-%m-%d'
        elif self.interval == KPI_INTERVAL_WEEKLY:
            result = '%W'
        elif self.interval == KPI_INTERVAL_MONTHLY:
            result = '%Y/%m'
        elif self.interval == KPI_INTERVAL_YEARLY:
            result = '%Y'
        if for_template:
            result = result.replace('%', '')
        return result

    def get_segments_cache_key(self, time_segment_n):
        segment_beginning, segment_end = self.segments[time_segment_n]
        segment_beginning_ts = int(time.mktime(segment_beginning.timetuple()))
        segment_end_ts = int(time.mktime(segment_end.timetuple()))
        return '%s-%s' % (segment_beginning_ts, segment_end_ts)

    @property
    def subintervals_in_the_last_segment(self):
        now = timezone.now()
        last_segment_beginning, last_segment_end = self.segments[len(self.segments) - 1]
        if now.tzinfo is None or now.tzinfo.utcoffset(last_segment_beginning) is None:
            now = timezone.make_aware(now)
        since_beginning = getattr((now - last_segment_beginning), self._td_subinterval)
        until_end = getattr((last_segment_end - now), self._td_subinterval)
        return since_beginning, until_end

    @property
    def segments(self):
        return dict(enumerate(self._segments))


KPIValueComponent = namedtuple('KPIValueComponent', 'name description value')


class KPIValueObject(object):
    @staticmethod
    def from_json(json_input):
        json_input = json.loads(json_input)
        value = json_input['value']
        if isinstance(value, float):
            value = Decimal(value).quantize(Decimal('.01'))
        if json_input['value_components']:
            kvcs = [KPIValueComponent(*vc) for vc in json_input['value_components']]
        else:
            kvcs = None
        return KPIValueObject(value, value_components=kvcs)

    def __init__(self, value, value_components=None):
        self._value = value
        self._value_components = value_components

    def __repr__(self):
        return self.__str__()

    def __str__(self):
        return 'KPIValueObject[value="%s" value_components=%s]' % (
            self.value,
            self.value_components,
        )

    def tolist(self):
        result = [
            self._value,
        ]
        if self._value_components:
            result += self._value_components
        return result

    @property
    def value(self):
        return self._value

    @property
    def value_components(self):
        return self._value_components

    def as_dict(self):
        return {'value': self.value, 'value_components': self.value_components}

    def to_json(self):
        return json.dumps(self.as_dict())


def kpi_cache_value(*args, **kwargs):
    func = None
    if len(args) == 1 and __builtins__['callable'](args[0]):
        func = args[0]
    cache_period = kwargs.get('cache_period', None) if not func else None
    global_cache = kwargs.get('global_cache', False) if not func else None

    def _kpi_cache_value(func):
        @wraps(func)
        def _decorator(self, *args, **kwargs):
            cache_key = '%s_%s' % (
                self.get_cache_key(*args, global_cache=global_cache),
                func.__name__,
            )

            is_cachable = self._is_cachable(*args)
            value = cache.get(cache_key)
            if value is not None:
                if is_cachable:
                    _cache_period = cache_period or self._cache_period
                    cache_timestamp = cache.get('%s_timestamp' % cache_key)
                    cache_timestamp_dt = datetime.fromtimestamp(
                        cache_timestamp if cache_timestamp else 0
                    )
                    if (datetime.now() - cache_timestamp_dt).seconds > _cache_period:
                        task_kpis_generate_value.delay(
                            self.kpi_group.__class__.__name__,
                            self.kpi_group.name,
                            self.__class__.__name__,
                            self._bi_type,
                            self._utm_filters,
                            *args,
                        )
                    return value
            else:
                value = func(self, *args, **kwargs)
            if is_cachable:
                _cache_period = None
                cache.set('%s_timestamp' % cache_key, int(time.time()), None)
            else:
                _cache_period = 60
            cache.set(cache_key, value, _cache_period)
            return value

        return _decorator

    return _kpi_cache_value(func) if func else _kpi_cache_value


def kpi_save_value(func):
    @wraps(func)
    def _decorator(self, *args, **kwargs):
        is_savable = self._is_savable(*args)
        if is_savable:
            kpi_ids = self._get_kpi_ids(*args)
            kpi_value_kwargs = {
                'kpi_name': kpi_ids['kpi_name'],
                'bi_type': kpi_ids['bi_type'],
                'region': kpi_ids['region'],
                'segment_beginning': kpi_ids['segment_beginning'],
                'segment_end': kpi_ids['segment_end'],
                'utms_hash': kpi_ids['utms_hash'],
            }
            try:
                kpi_value = KPIValue.objects.get(**kpi_value_kwargs)
                return kpi_value.value
            except KPIValue.DoesNotExist:
                pass
            except KPIValue.MultipleObjectsReturned:
                KPIValue.objects.filter(**kpi_value_kwargs).delete()
        value = func(self, *args, **kwargs)
        if is_savable:
            kpi_value_kwargs['value'] = value
            try:
                KPIValue.objects.create(**kpi_value_kwargs)
            except IntegrityError:
                KPIValue.objects.filter(
                    kpi_name=kpi_ids['kpi_name'],
                    bi_type=kpi_ids['bi_type'],
                    region=kpi_ids['region'],
                    segment_beginning=kpi_ids['segment_beginning'],
                    segment_end=kpi_ids['segment_end'],
                    utms_hash=kpi_ids['utms_hash'],
                ).update(value=value)
            except Exception:
                logger.exception('Error while saving KPIValue[%s]' % kpi_value_kwargs)
        return value

    return _decorator


def write_row_list_to_csv_string(row_list):
    csv_stream = StringIO()
    csv_writer = csv.writer(csv_stream)
    for row in row_list:
        csv_writer.writerow(row)
    csv_stream.seek(0)
    return csv_stream.read()


########################################
# DEFINITIONS, ABSTRACTS, INTERFACES   #
########################################


class KPITemplateInterface(abc.ABC):
    @abc.abstractproperty
    def as_csv(self, as_row_list=False, with_header=False):
        raise NotImplementedError

    @abc.abstractproperty
    def as_html(self, request=None):
        raise NotImplementedError

    @abc.abstractproperty
    def name(self):
        raise NotImplementedError

    @abc.abstractproperty
    def template_name(self):
        raise NotImplementedError


class KPIGroup(KPITemplateInterface, metaclass=abc.ABCMeta):
    def __init__(self, name):
        self._name = name
        self._registry = list()

    def __repr__(self):
        return self.__str__()

    def __str__(self):
        return '{}[name="{}"]'.format(type(self).__name__, self.name)

    @property
    def class_name(self):
        return self.__class__.__name__

    def get_kpi_by_class_name(self, class_name):
        for kpi in self._registry:
            if kpi.__class__.__name__ == class_name:
                return kpi

    @property
    def kpis(self):
        return self._registry

    @property
    def name(self):
        return self._name

    def register_kpi(self, kpi):
        if kpi not in self._registry:
            self._registry.append(kpi)
            for dependency_class in kpi.dependencies:
                if not self.get_kpi_by_class_name(dependency_class.__name__):
                    dependency_class(
                        self,
                        bi_type=kpi._bi_type,
                        region=kpi._region,
                        utm_filters=kpi._utm_filters,
                        is_dependency=True,
                    )

    @abc.abstractproperty
    def time_segments(self):
        raise NotImplementedError


class KPIType(KPITemplateInterface, metaclass=abc.ABCMeta):
    def __init__(
        self,
        kpi_group,
        bi_type=None,
        region=None,
        utm_filters=None,
        is_dependency=False,
    ):
        self._kpi_group = kpi_group
        self._bi_type = bi_type if bi_type else 'all'
        self._region = region
        self._utm_filters = utm_filters if utm_filters else {}
        if self._bi_type == KPI_TYPE_FACEBOOK:
            self._utm_filters['source'] = (
                'facebook',
                r'.*\.fb',
            )
            self._utm_filters['medium'] = ('cpc',)
        elif self._bi_type == KPI_TYPE_GOOGLE:
            self._utm_filters['source'] = ('google',)
            self._utm_filters['medium'] = ('cpc',)
        self._is_dependency = is_dependency
        self._kpi_group.register_kpi(self)

    def __repr__(self):
        return self.__str__()

    def __str__(self):
        return '{}[name="{}"]'.format(type(self).__name__, self.name)

    @property
    def _cache_period(self):
        now = timezone.now()
        time_segments = self.kpi_group.time_segments
        delta = timedelta(
            **{time_segments._td_designation: time_segments._td_multiplier}
        )
        return int(
            old_div(((now + delta) - now).total_seconds(), time_segments.time_span)
        )

    @property
    def bi_type(self):
        return self._bi_type

    @property
    def class_name(self):
        return self.__class__.__name__

    @property
    def dependencies(self):
        return tuple()

    @property
    def is_dependency(self):
        return self._is_dependency

    @property
    def region(self):
        return self._region

    @property
    def region_q(self):
        if self._region:
            return Q(region__name=self._region.name)
        return Q()

    @property
    def utm_filters(self):
        return self._utm_filters

    def get_cache_key(self, time_segment, global_cache=False):
        time_segment_key = self.kpi_group.time_segments.get_segments_cache_key(
            time_segment
        )
        class_or_global = 'global' if global_cache else self.__class__.__name__
        return '%s_%s_%s_%s_%s' % (
            class_or_global,
            self._bi_type,
            self._region.name if self._region else '',
            hash(frozenset(list(self._utm_filters.items()))),
            time_segment_key,
        )

    def _get_kpi_ids(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        return {
            'kpi_name': self.__class__.__name__,
            'bi_type': self._bi_type,
            'region': self.region.name if self.region else None,
            'segment_beginning': segment_beginning,
            'segment_end': segment_end,
            'utms': ','.join(
                [
                    '%s:"%s"' % (r[0], ','.join(r[1]))
                    for r in list(self._utm_filters.items())
                ]
            ),
            'utms_hash': hash(frozenset(list(self._utm_filters.items()))),
        }

    @kpi_cache_value(cache_period=900, global_cache=True)
    def _get_customers(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        customers = UserProfile.objects.filter(
            Q(
                user_type=UserType.CUSTOMER,
                user__date_joined__gte=segment_beginning,
                user__date_joined__lt=segment_end,
            )
            & self.region_q
        )

        result = customers
        len(result)
        return result

    @kpi_cache_value(cache_period=900, global_cache=True)
    def _get_users(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        segment_end -= timedelta(seconds=1)
        if self._bi_type == KPI_TYPE_GOOGLE_ANALYTICS:
            widget_set = {
                'date_from': segment_beginning.strftime('%Y-%m-%d'),
                'date_to': segment_end.strftime('%Y-%m-%d')
                if time_segment_n < len(self.kpi_group.time_segments.segments) - 1
                else None,
                'steps': [{'metric': {'name': 'Users', 'value': 'ga:users'}}],
            }
            cache_key = 'gecko_analytics_users_{}_{}'.format(
                widget_set['date_from'], widget_set['date_to']
            )
            data = cache.get(cache_key)
            if not data:
                data = google_analytics_funnel_fetch(widget_set, cache_key)
            if (
                data
                and 'items' in data
                and len(data['items']) > 0
                and len(data['items'][0]) > 0
            ):
                return list(range(data['items'][0][0]))
            else:
                return []
        else:
            users = UserProfile.objects.filter(
                Q(
                    user__date_joined__gte=segment_beginning,
                    user__date_joined__lt=segment_end,
                )
                & self.region_q
            )
            result = users
            len(result)
            return result

    def _order_queryset(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        orders = Order.objects.filter(
            Q(paid_at__gte=segment_beginning, paid_at__lt=segment_end) & self.region_q
        )

        # Exclude samplebox from all order-based metrics,
        # as kpis should be product based only
        sample_box = ContentType.objects.get(model='samplebox')
        orders = orders.exclude(items__content_type=sample_box).distinct()

        if len(self._utm_filters) > 0:
            for k, vs in list(self._utm_filters.items()):
                utm_query = Order.objects.none()
                for v in vs:
                    referer_kv = r'{}={}'.format(
                        k,
                        v,
                    )
                    referer_kv_qs = quote_plus(referer_kv)
                    utm_query |= Q(
                        owner__profile__registration_referrer__iregex=referer_kv
                    )
                    utm_query |= Q(
                        owner__profile__registration_referrer__iregex=referer_kv_qs
                    )
                orders = orders.filter(utm_query)
        if self._bi_type == KPI_TYPE_ORGANIC:
            orders = orders.exclude(
                Q(owner__profile__registration_referrer__contains='utm_')
                | Q(owner__profile__registration_referrer__contains='be_')
            )
        orders_ids = orders.values_list('id', flat=True)
        result = Order.objects.filter(
            id__in=list(orders_ids),
            parent_order__isnull=True,
        )
        return result

    @kpi_cache_value(cache_period=900, global_cache=True)
    def _get_orders(self, time_segment_n, filter=None):
        if time_segment_n < 0:
            return tuple()
        result = self._order_queryset(time_segment_n)

        if filter:
            result = result.filter(filter)
        len(result)  # Do the job once.

        return result

    @kpi_cache_value(cache_period=900, global_cache=True)
    def _get_orders_filtered(self, time_segment_n, filter):
        if time_segment_n < 0:
            return tuple()
        result = self._order_queryset(time_segment_n).filter(filter)
        len(result)  # Do the job once.
        return result

    @abc.abstractmethod
    def _is_cachable(self, time_segment):
        raise NotImplementedError

    @abc.abstractmethod
    def _is_savable(self, time_segment):
        raise NotImplementedError

    @property
    def _is_percentage(self):
        return False

    @property
    def chart(self):
        return ','.join([str(v) for v in self.values])

    @property
    def color_positive(self):
        return 'green'

    @property
    def color_negative(self):
        return 'red'

    def get_difference(self, time_segment_1, time_segment_2):
        value_1 = Decimal(self.get_value(time_segment_1).value)
        value_2 = Decimal(self.get_value(time_segment_2).value)
        difference = (value_1 - value_2).quantize(Decimal('.01'))
        if value_2 == 0:
            difference_percentage = 0 if value_1 == 0 else 100
        else:
            difference_percentage = (
                old_div(100 * Decimal(difference), Decimal(value_2))
            ).quantize(Decimal('.01'))
        return difference, difference_percentage

    def get_share(self, time_segment):
        this_value = Decimal(self.get_value(time_segment).value)
        all_kpis_value = sum(
            [Decimal(kpi.get_value(time_segment).value) for kpi in self.kpi_group.kpis]
        )
        if all_kpis_value == 0:
            return 0
        return (old_div(100 * this_value, all_kpis_value)).quantize(Decimal('.01'))

    @abc.abstractmethod
    def get_value(self, time_segment_n):
        raise NotImplementedError

    @property
    def kpi_group(self):
        return self._kpi_group

    def post_processing(self, value):
        return value

    @property
    def prediction_based_on_last_segment(self):
        (
            since_beginning,
            until_end,
        ) = self.kpi_group.time_segments.subintervals_in_the_last_segment
        if since_beginning == 0:
            return 0
        value_object = self.get_value(len(self.kpi_group.time_segments.segments) - 1)
        coefficient = old_div(
            Decimal(value_object.value), (Decimal(since_beginning) + 1)
        )

        value_components = []
        if value_object and value_object.value_components:
            for vc in value_object.value_components:
                kvc = KPIValueComponent(
                    name=vc.name,
                    description=vc.description,
                    value=Decimal(
                        old_div(Decimal(vc.value), (Decimal(since_beginning) + 1))
                        * (since_beginning + 1 + until_end)
                    ).quantize(Decimal('.01')),
                )
                value_components.append(kvc)

        result = coefficient * (since_beginning + 1 + until_end)
        return KPIValueObject(
            value=result.quantize(Decimal('.01')), value_components=value_components
        )

    @property
    def prediction_based_on_trend(self):
        segments_len = len(self.kpi_group.time_segments.segments)
        if segments_len < 3:
            return 0
        coefficients = self._trendline_coefficients
        poly = numpy.poly1d(coefficients)
        segment_n_start, segment_n_end = (
            max(0, old_div(segments_len, 2) - 1),
            max(1, segments_len - 1),
        )
        segments = dict(
            list(self.kpi_group.time_segments.segments.items())[
                segment_n_start:segment_n_end
            ]
        )
        result = Decimal(poly(len(segments))).quantize(Decimal('.01'))
        if not self._is_percentage and result < 0:
            return 0
        else:
            return result

    @property
    def _trendline_coefficients(self):
        segments_len = len(self.kpi_group.time_segments.segments)
        segment_n_start, segment_n_end = (
            max(0, old_div(segments_len, 2) - 1),
            max(1, segments_len - 1),
        )
        segments = dict(
            list(self.kpi_group.time_segments.segments.items())[
                segment_n_start:segment_n_end
            ]
        )
        values = [
            float(self.get_value(time_segment).value) for time_segment in segments
        ]
        x = numpy.arange(0, len(values))
        y = numpy.array(values)
        z = numpy.polyfit(x, y, 1)
        return z[0], z[1]

    @property
    def trendline(self):
        points_y = []
        coefficients = self._trendline_coefficients
        poly = numpy.poly1d(coefficients)
        segments_len = len(self.kpi_group.time_segments.segments)
        segment_n_start, segment_n_end = (
            max(0, old_div(segments_len, 2) - 1),
            max(1, segments_len - 1),
        )
        segments = dict(
            list(self.kpi_group.time_segments.segments.items())[
                segment_n_start:segment_n_end
            ]
        )
        for time_segment_n in segments:
            point_value = int(round(poly(time_segment_n)))
            if point_value < 0:
                point_value = 0
            points_y.append(str(point_value))
        return ','.join(points_y)

    @property
    def values(self):
        result = []
        for n, segment in list(self.kpi_group.time_segments.segments.items()):
            result.append(self.get_value(n).value)
        return result

    @property
    def value_suffix(self):
        return ''


########################################
#                MIXINS                #
########################################


class KPINineMonthsMonthlyMixin(object):
    @property
    def time_segments(self):
        return KPITimeSegments(KPI_INTERVAL_MONTHLY, 15)


class KPINineWeeksWeeklyMixin(object):
    @property
    def time_segments(self):
        return KPITimeSegments(KPI_INTERVAL_WEEKLY, 9)


class KPITwoWeeksDailyMixin(object):
    @property
    def time_segments(self):
        return KPITimeSegments(KPI_INTERVAL_DAILY, 14)


def create_dynamic_segments_mixin(interval, time_span):
    class KPIDynamicSegmentsMixin(object):
        def _is_cachable(self, time_segment):
            return False

        def _is_savable(self, time_segment):
            return False

        @property
        def time_segments(self):
            return KPITimeSegments(interval, time_span)

    return KPIDynamicSegmentsMixin


class KPIStandardCSVGroupMixin(object):
    def as_csv(self, as_row_list=False, with_header=False):
        result = []
        if with_header:
            result.append(('name',))
            for i, segment in list(self.time_segments.segments.items()):
                ts_string = segment[0].strftime('%Y-%m-%d')
                result[0] += (ts_string,)
            result[0] += (
                'prediction',
                'prediction trend',
            )
        for kpi in self._registry:
            if hasattr(kpi, 'as_csv'):
                result.append(kpi.as_csv(as_row_list=True))
        if as_row_list:
            return result
        return write_row_list_to_csv_string(result)


class KPIStandardHTMLGroupMixin(object):
    def as_html(self, request=None):
        c = {'group': self, 'request': request}
        t = loader.get_template(self.template_name)
        return mark_safe(t.render(c))

    @property
    def template_name(self):
        return 'kpis/group.html'


class KPIJSONGroupMixin(object):
    def as_json(self, as_dict=False):
        result = {'name': self.name, 'kpis': []}
        for kpi in self.kpis:
            if hasattr(kpi, 'as_json'):
                result['kpis'].append(kpi.as_json(as_dict=True))
        if as_dict:
            return result
        return json.dumps(result)


class KPIWeekNumberHTMLGroupMixin(KPIStandardHTMLGroupMixin):
    @property
    def template_name(self):
        return 'kpis/group_week_number.html'


class KPIJSONMixin(object):
    def as_json(self, as_dict=False):
        result = {
            'name': self.name,
            'values': [],
        }
        time_segments = self.kpi_group.time_segments
        for n, segment in list(time_segments.segments.items()):
            kpi_value = self.get_value(n)
            result_value = {
                'components': kpi_value.value_components,
                'time_segment': segment[0].strftime(
                    time_segments.get_date_format(for_template=False)
                ),
                'value': kpi_value.value,
            }
            if n > 0:
                (
                    result_value['difference_nominal'],
                    result_value['difference_percentage'],
                ) = self.get_difference(n, n - 1)
            result['values'].append(result_value)
        result['prediction'] = self.prediction_based_on_last_segment
        result['trend'] = self.prediction_based_on_trend
        if as_dict:
            return result
        return json.dumps(result)


class KPIStandardCSVMixin(object):
    def as_csv(self, as_row_list=False, with_header=False):
        result = (self.name,)
        for time_segment_n in self.kpi_group.time_segments.segments:
            current_value = self.get_value(time_segment_n).value
            result += (current_value,)
        result += (
            self.prediction_based_on_last_segment,
            self.prediction_based_on_trend,
        )
        if as_row_list:
            return result
        return write_row_list_to_csv_string((result,))


class KPIStandardHTMLMixin(object):
    def as_html_header(self):
        c = {'kpi': self, 'values': [], 'only_header': True}
        t = loader.get_template(self.template_name)
        return mark_safe(t.render(c))

    def as_html(self, request=None):
        c = {'kpi': self, 'values': [], 'only_header': False, 'request': request}
        detailed_segments = list(self.kpi_group.time_segments.segments.keys())[
            old_div(len(self.kpi_group.time_segments.segments), 2) :  # noqa E203
        ]
        for n, segment in list(self.kpi_group.time_segments.segments.items()):
            kpi_value = self.get_value(n)
            value = {
                'detailed': n in detailed_segments,
                'share': self.get_share(n),
                'time_segment_n': n,
                'value': kpi_value.value,
                'value_components': kpi_value.value_components,
            }
            if n > 0:
                (
                    value['difference_nominal'],
                    value['difference_percentage'],
                ) = self.get_difference(n, n - 1)
            c['values'].append(value)
        t = loader.get_template(self.template_name)
        return mark_safe(t.render(c))

    @property
    def template_name(self):
        return 'kpis/kpi.html'


class KPINoShareHTMLMixin(KPIStandardHTMLMixin):
    def as_html(self, request=None):
        c = {'kpi': self, 'values': [], 'only_header': False, 'request': request}
        detailed_segments = list(self.kpi_group.time_segments.segments.keys())[
            old_div(len(self.kpi_group.time_segments.segments), 2) :  # noqa E203
        ]
        for n, segment in list(self.kpi_group.time_segments.segments.items()):
            kpi_value = self.get_value(n)
            value = {
                'detailed': n in detailed_segments,
                'time_segment_n': n,
                'value': kpi_value.value,
                'value_components': kpi_value.value_components,
            }
            if n > 0:
                (
                    value['difference_nominal'],
                    value['difference_percentage'],
                ) = self.get_difference(n, n - 1)
            c['values'].append(value)
        t = loader.get_template(self.template_name)
        return mark_safe(t.render(c))

    @property
    def template_name(self):
        return 'kpis/kpi_noshare.html'


class KPINoShareComponentStringHtmlMixin(KPINoShareHTMLMixin):
    @property
    def template_name(self):
        return 'kpis/kpi_noshare_component_string.html'


class KPICachableAllButLastMixin(object):
    def _is_cachable(self, time_segment):
        if time_segment >= len(self.kpi_group.time_segments.segments) - 1:
            return False
        return True


class KPICachableAllButTwoLastMixin(object):
    def _is_cachable(self, time_segment):
        if time_segment >= len(self.kpi_group.time_segments.segments) - 2:
            return False
        return True


class KPISavableFromThirdLastDownMixin(object):
    def _is_savable(self, time_segment):
        number_of_segments = len(self.kpi_group.time_segments.segments)
        return time_segment < number_of_segments - 3


class KPIValueSuffixPercentageMixin(object):
    @property
    def _is_percentage(self):
        return True

    @property
    def value_suffix(self):
        return ' %'


class KPIValueSuffixEuroMixin(object):
    @property
    def value_suffix(self):
        return ' €'


class KPINoPredictionMixin(object):
    @property
    def prediction_based_on_last_segment(self):
        return None


class KPIReversePositiveColor(object):
    @property
    def color_positive(self):
        return 'red'

    @property
    def color_negative(self):
        return 'green'


class ThousandSeparatorMixin(object):
    def post_processing(self, value):
        return '{:,.2f}'.format(float(value or 0)).replace(',', ' ')


########################################
#              KPI GROUPS              #
########################################


class KPIMonthlyStandardGroup(
    KPINineMonthsMonthlyMixin,
    KPIStandardHTMLGroupMixin,
    KPIStandardCSVGroupMixin,
    KPIJSONGroupMixin,
    KPIGroup,
):
    pass


class KPIWeeklyStandardGroup(
    KPINineWeeksWeeklyMixin,
    KPIWeekNumberHTMLGroupMixin,
    KPIStandardCSVGroupMixin,
    KPIJSONGroupMixin,
    KPIGroup,
):
    pass


class KPIDailyGroup(
    KPITwoWeeksDailyMixin,
    KPIStandardHTMLGroupMixin,
    KPIStandardCSVGroupMixin,
    KPIJSONGroupMixin,
    KPIGroup,
):
    pass


########################################
#                 KPIS                 #
########################################


class KPIOrderFinished(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        orders = self._get_orders(time_segment)
        value = len(orders)
        order_countries = (
            orders.values('country')
            .annotate(count=Count('country'))
            .order_by('-count', 'country')
        )
        value_components = []
        for order_country in order_countries:
            country = order_country['country']
            if country is not None:
                kvc = KPIValueComponent(
                    name='order_country_%s' % country.lower(),
                    description=country,
                    value=order_country['count'],
                )
                value_components.append(kvc)
        return KPIValueObject(value=value, value_components=value_components)

    @property
    def name(self):
        return 'Orders finished'


class KPIOrderFinishedPerDay(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    ThousandSeparatorMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment
        ]
        now = timezone.now()
        if not timezone.is_aware(now):
            now = timezone.make_aware(now)
        if segment_end > now:
            delta = now - segment_beginning
        else:
            delta = segment_end - segment_beginning
        orders = self._get_orders(time_segment)
        orders_count = len(orders)
        value = orders_count / float(delta.days) if delta.days else orders_count
        order_countries = (
            orders.values('country')
            .annotate(count=Count('country'))
            .order_by('-count', 'country')
        )
        value_components = []
        for order_country in order_countries:
            country = order_country['country']
            if country is not None:
                kvc = KPIValueComponent(
                    name='order_country_%s' % country.lower(),
                    description=country,
                    value=Decimal(order_country['count'] / float(delta.days)).quantize(
                        Decimal('.01')
                    )
                    if delta.days
                    else order_country['count'],
                )
                value_components.append(kvc)
        return KPIValueObject(
            value=Decimal(value).quantize(Decimal('.01')),
            value_components=value_components,
        )

    @property
    def name(self):
        return 'Orders finished per day'


class KPIOrderTotalValue(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    ThousandSeparatorMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @property
    def dependencies(self):
        return KPIMarketingOtherRevenue, KPIFreeAssemblyCost

    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        orders = self._get_orders(time_segment)
        value = sum([(order.get_base_total_value_net() or 0) for order in orders])
        order_sources = (
            orders.values('order_source')
            .annotate(count=Count('order_source'))
            .order_by('order_source')
            .order_by('-count')
        )
        value_components = []
        for order_source in order_sources:
            kvc = KPIValueComponent(
                name='order_source_%s' % order_source['order_source'],
                description=dict(OrderSource.choices)[order_source['order_source']],
                value=order_source['count'],
            )
            value_components.append(kvc)
        kpi_mnr = self.kpi_group.get_kpi_by_class_name('KPIMarketingOtherRevenue')
        mnr = Decimal(kpi_mnr.get_value(time_segment).value)
        value_components.append(
            KPIValueComponent(
                name='revenue_manual', description='Revenue manual', value=mnr
            )
        )
        value += mnr
        return KPIValueObject(value=value, value_components=value_components)

    @property
    def name(self):
        return 'Order total net value'


class KPIFreeAssemblyCost(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    ThousandSeparatorMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        cost = 0
        free_assembly_vouchers = Voucher.objects.filter(value=1).values_list(
            'id', flat=True
        )
        orders_with_free_assembly = [
            x
            for x in self._get_orders(time_segment_n)
            if x.used_promo_id in free_assembly_vouchers
        ]

        for order in orders_with_free_assembly:
            delivery_cost = Decimal(0)
            if order.country == 'france':
                # for 2 from 14 france orders, there were added
                # value for distance, 50 in avg
                delivery_cost = order.get_base_total_value() * Decimal(0.12) + Decimal(
                    (2 / 14.0) * 50
                )
            else:
                delivery_cost = order.get_base_total_value() * Decimal(0.10)
            cost += max(delivery_cost, Decimal(80))

        return KPIValueObject(value=cost)

    @property
    def name(self):
        return 'Free assembly cost'


class KPIOrderAvgValue(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    ThousandSeparatorMixin,
    KPINoPredictionMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        orders_finished = self._get_orders(time_segment)
        orders_finished_count = len(orders_finished)
        if orders_finished_count > 0:
            total_value = sum(
                [
                    (order.get_base_total_value_net() or 0)
                    + (order.get_base_promo_amount_net() or 0)
                    for order in orders_finished
                ]
            )
            value = (old_div(Decimal(total_value), orders_finished_count)).quantize(
                Decimal('.01')
            )
        else:
            value = 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Avg. Order net value + promo'


class KPIOrderAvgItems(
    KPICachableAllButLastMixin,
    KPINoPredictionMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    def _is_savable(self, *args, **kwargs):
        return False

    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        orders = self._get_orders(time_segment)
        items = orders.annotate(num_items=Count('product')).aggregate(
            num_items__sum=Coalesce(Sum('num_items'), Value(0))
        )['num_items__sum']
        items = Decimal(items) if items else 0
        orders_len = Decimal(len(orders))
        value = (
            (old_div(items, orders_len)).quantize(Decimal('.01'))
            if orders_len > 0
            else 0
        )
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Order items avg'


class KPIGASessions(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    ThousandSeparatorMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment
        ]
        segment_end -= timedelta(seconds=1)
        widget_set = {
            'date_from': segment_beginning.strftime('%Y-%m-%d'),
            'date_to': segment_end.strftime('%Y-%m-%d')
            if time_segment < len(self.kpi_group.time_segments.segments) - 1
            else None,
            'steps': [{'metric': {'name': 'Sessions', 'value': 'ga:sessions'}}],
        }
        cache_key = 'gecko_analytics_sessions_{}_{}'.format(
            widget_set['date_from'], widget_set['date_to']
        )
        data = cache.get(cache_key)
        if not data:
            data = google_analytics_funnel_fetch(widget_set, cache_key)
        if (
            data
            and 'items' in data
            and len(data['items']) > 0
            and len(data['items'][0]) > 0
        ):
            value = data['items'][0][0]
        else:
            value = 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Session'


class KPIAdCost(
    KPICachableAllButTwoLastMixin,
    ThousandSeparatorMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIReversePositiveColor,
    KPIType,
):
    def _is_savable(self, *args, **kwargs):
        return False

    @property
    def dependencies(self):
        return KPIMarketingOtherCost, KPIFreeAssemblyCost

    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        value_components = []
        cost = Decimal(0)
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment
        ]
        segment_end -= timedelta(seconds=1)

        orders_finished = self._get_orders(time_segment)
        if self._bi_type in (KPI_TYPE_ALL, KPI_TYPE_ORGANIC, KPI_TYPE_UTM):
            orders_finished_cb = self._get_orders_filtered(
                time_segment,
                filter=Q(used_promo__creator__username='<EMAIL>')
                | Q(used_promo__code__startswith='cbf')
                | Q(used_promo__code__startswith='cbef')
                | Q(used_promo__origin=VoucherOrigin.CORPORATE),
            )
            sold_vouchers = self._order_queryset(time_segment).filter(
                used_promo__origin=VoucherOrigin.SOLD_VOUCHER
            )
            len(sold_vouchers)  # dirty hack
            corporate_benefits = Decimal(
                sum(
                    [
                        (order.get_base_promo_amount_net() or 0)
                        for order in orders_finished_cb
                    ]
                )
            )
            organic_cost = Decimal(
                sum(
                    [
                        (order.get_base_promo_amount_net() or 0)
                        for order in orders_finished
                    ]
                )
            )
            sold_vouchers = Decimal(
                sum(
                    [
                        (order.get_base_promo_amount_net() or 0)
                        for order in sold_vouchers
                    ]
                )
            )
            value_components.append(
                KPIValueComponent(
                    name='cost_organic',
                    description='Organic (wo corporate and sold vouchers)',
                    value=organic_cost - corporate_benefits - sold_vouchers,
                )
            )
            value_components.append(
                KPIValueComponent(
                    name='corporate_benefits',
                    description='Corporate Benefits',
                    value=corporate_benefits,
                )
            )
            value_components.append(
                KPIValueComponent(
                    name='sold_vouchers',
                    description='Sold Vouchers',
                    value=sold_vouchers,
                )
            )
            cost += organic_cost

        if self._bi_type in (KPI_TYPE_ALL, KPI_TYPE_ORGANIC, KPI_TYPE_UTM):
            kpi_mnc = self.kpi_group.get_kpi_by_class_name('KPIFreeAssemblyCost')
            mnc = Decimal(kpi_mnc.get_value(time_segment).value)
            value_components.append(
                KPIValueComponent(
                    name='cost_free_assembly', description='Free Assembly', value=mnc
                )
            )
            cost += mnc

        date_from = segment_beginning.strftime('%Y-%m-%d')
        date_to = segment_end.strftime('%Y-%m-%d')
        country_code = self._region.get_country().code if self._region else None
        if (
            self._bi_type in (KPI_TYPE_ALL, KPI_TYPE_GOOGLE, KPI_TYPE_GOOGLE_ANALYTICS)
            and settings.DEBUG is False
        ):
            if self._region is None:
                widget_set = {
                    'date_from': date_from,
                    'date_to': date_to
                    if time_segment < len(self.kpi_group.time_segments.segments) - 1
                    else None,
                    'steps': [{'metric': {'name': 'Ad Cost', 'value': 'ga:adCost'}}],
                }
                cache_key = 'gecko_analytics_adcost_%s_%s' % (
                    widget_set['date_from'],
                    widget_set['date_to'],
                )
            else:
                widget_set = {
                    'date_from': date_from,
                    'date_to': date_to
                    if time_segment < len(self.kpi_group.time_segments.segments) - 1
                    else None,
                    'steps': [
                        {
                            'filters': [
                                {
                                    'dimensions': 'ga:campaign',
                                    'value': 'ga:medium==cpc,ga:source==google',
                                }
                            ],
                            'metric': {'name': 'Ad Cost', 'value': 'ga:adCost'},
                        }
                    ],
                }
                cache_key = 'gecko_analytics_adcost_{}_{}_campaigns'.format(
                    widget_set['date_from'], widget_set['date_to']
                )
            data = cache.get(cache_key) if self._is_cachable(time_segment) else None
            if not data:
                data = google_analytics_funnel_fetch(
                    widget_set, cache_key, cache_period=self._cache_period
                )
            if data:
                ga_cost = Decimal(0)
                if 'items' in data and len(data['items']) > 0:
                    if self._region is None and len(data['items'][0]) > 0:
                        ga_cost = Decimal(data['items'][0][0].replace('%', '').strip())
                    if self._region is not None:
                        for item in data['items']:
                            if '[%s]' % country_code in item[1]:
                                ga_cost += Decimal(item[0].replace('%', '').strip())
                value_components.append(
                    KPIValueComponent(
                        name='cost_google', description='Adwords', value=ga_cost
                    )
                )
                cost += ga_cost

        if (
            self._bi_type in (KPI_TYPE_ALL, KPI_TYPE_FACEBOOK)
            and settings.DEBUG is False
        ):
            fb_cumulative = 0
            for fb_account_name, fb_account_id in list(settings.FB_ACCOUNT_IDS.items()):
                try:
                    FacebookAdsApi.init(
                        app_id=settings.FB_APP_ID,
                        app_secret=settings.FB_APP_SECRET,
                        access_token=settings.FB_APP_TOKEN,
                        account_id=fb_account_id,
                        api_version=settings.FB_API_VERSION,
                    )
                    fb_account = AdAccount(fbid=fb_account_id)
                    fb_cache_key = 'fb_insights_{}_{}_{}'.format(
                        fb_account_id, date_from, date_to
                    )
                    fb_insights = (
                        cache.get(fb_cache_key)
                        if self._is_cachable(time_segment)
                        else None
                    )
                    if not fb_insights:
                        fb_params = {
                            'breakdowns': ['country'],
                            'time_range': {'since': date_from, 'until': date_to},
                        }
                        fb_insights = fb_account.get_insights(params=fb_params)
                        # as somehow in py3 fb pickling changed - this will allow
                        # to avoid nested dict_values inside fb library,
                        # lets change it to list of dicts
                        fb_insights = [dict(x) for x in fb_insights]
                        cache.set(fb_cache_key, fb_insights, self._cache_period)
                    fb_cost = 0
                    for insight in fb_insights:
                        if self._region is None or (
                            self._region is not None
                            and insight['country'] == country_code
                        ):
                            fb_cost += Decimal(insight['spend'])
                    value_components.append(
                        KPIValueComponent(
                            name='cost_fb_{}'.format(fb_account_id),
                            description='Facebook {}'.format(fb_account_name),
                            value=fb_cost,
                        )
                    )
                    fb_cumulative += fb_cost
                except FacebookRequestError:
                    logger.exception(
                        'Error while calling Facebook Ads API {} {}'.format(
                            fb_account_id, fb_account_name
                        )
                    )
            value_components.append(
                KPIValueComponent(
                    name='cost_fb_cumulative',
                    description='Facebook cumulative costs',
                    value=fb_cumulative,
                )
            )
            cost += fb_cumulative

        if self._bi_type == KPI_TYPE_ALL:
            kpi_mnc = self.kpi_group.get_kpi_by_class_name('KPIMarketingOtherCost')
            mnc = Decimal(kpi_mnc.get_value(time_segment).value)
            value_components.append(
                KPIValueComponent(name='cost_manual', description='Manual', value=mnc)
            )
            cost += mnc

        value = cost.quantize(Decimal('.01'))
        return KPIValueObject(value=value, value_components=value_components)

    @property
    def name(self):
        return 'Cost (%s)' % self._bi_type


class KPIGAAddToCartCR(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPIValueSuffixPercentageMixin,
    KPINoPredictionMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment
        ]
        segment_end -= timedelta(seconds=1)
        widget_set = {
            'date_from': segment_beginning.strftime('%Y-%m-%d'),
            'date_to': segment_end.strftime('%Y-%m-%d')
            if time_segment < len(self.kpi_group.time_segments.segments) - 1
            else None,
            'steps': [
                {
                    'metric': {
                        'name': 'Add to cart CR',
                        'value': 'ga:goal2ConversionRate',
                    }
                }
            ],
        }
        cache_key = 'gecko_analytics_addtocartCR_%s_%s' % (
            widget_set['date_from'],
            widget_set['date_to'],
        )
        data = cache.get(cache_key)
        if not data:
            data = google_analytics_funnel_fetch(widget_set, cache_key)
        if (
            data
            and 'items' in data
            and len(data['items']) > 0
            and len(data['items'][0]) > 0
        ):
            result = data['items'][0][0]
            value = Decimal(result.replace('%', '').strip())
        else:
            value = 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Add to cart CR'


class KPIGAAddToCart(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    ThousandSeparatorMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @property
    def dependencies(self):
        return [KPIGASessions, KPIGAAddToCartCR]

    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        segment_end -= timedelta(seconds=1)
        sessions = (
            self.kpi_group.get_kpi_by_class_name('KPIGASessions')
            .get_value(time_segment_n)
            .value
        )
        add_to_cart = (
            self.kpi_group.get_kpi_by_class_name('KPIGAAddToCartCR')
            .get_value(time_segment_n)
            .value
        )
        value = old_div((sessions * add_to_cart), 100)
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Carts created count (ga based)'


class KPIMarketingOtherCost(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPINoPredictionMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIReversePositiveColor,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        segment_beginning, segment_end = self.kpi_group.time_segments.segments[
            time_segment_n
        ]
        mnc = MarketingNonperformanceCost.objects.filter(
            date_at__gte=segment_beginning, date_at__lt=segment_end
        ).aggregate(cost_sum=Sum(F('cost_amount')))
        value = mnc['cost_sum'] or 0
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Marketing other cost'


class KPIMarketingOtherRevenue(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPINoPredictionMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIReversePositiveColor,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        return KPIValueObject(value=0)

    @property
    def name(self):
        return 'Marketing other revenue'


class KPIOrderTotalValueGross(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        orders = self._get_orders(time_segment_n)
        value = sum([(order.get_base_total_value() or 0) for order in orders])
        order_sources = (
            orders.values('order_source')
            .annotate(count=Count('order_source'))
            .order_by('order_source')
            .order_by('-count')
        )
        value_components = []
        for order_source in order_sources:
            kvc = KPIValueComponent(
                name='order_source_%s' % order_source['order_source'],
                description=dict(OrderSource.choices)[order_source['order_source']],
                value=order_source['count'],
            )
            value_components.append(kvc)
        return KPIValueObject(value=value, value_components=value_components)

    @property
    def name(self):
        return 'Order total value (gross)'


class KPIOrderDiscountValueGross(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        value = sum(
            [
                (order.get_base_promo_amount() or 0)
                for order in self._get_orders(time_segment_n)
            ]
        )
        return KPIValueObject(value=value)

    @property
    def name(self):
        return 'Order discount value (gross)'


class KPIOrderWithDiscountTotalValueGross(
    KPICachableAllButLastMixin,
    KPISavableFromThirdLastDownMixin,
    ThousandSeparatorMixin,
    KPIValueSuffixEuroMixin,
    KPINoShareHTMLMixin,
    KPIStandardCSVMixin,
    KPIJSONMixin,
    KPIType,
):
    @property
    def dependencies(self):
        return KPIOrderTotalValueGross, KPIOrderDiscountValueGross

    @kpi_cache_value
    @kpi_save_value
    def get_value(self, time_segment_n):
        kpi_order_total_value_gross = self.kpi_group.get_kpi_by_class_name(
            'KPIOrderTotalValueGross'
        )
        kpi_order_discount_value_gross = self.kpi_group.get_kpi_by_class_name(
            'KPIOrderDiscountValueGross'
        )
        order_total_value_gross = Decimal(
            kpi_order_total_value_gross.get_value(time_segment_n).value
        )
        order_discount_value_gross = Decimal(
            kpi_order_discount_value_gross.get_value(time_segment_n).value
        )
        sold_vouchers = self._order_queryset(time_segment_n).filter(
            used_promo__origin=VoucherOrigin.SOLD_VOUCHER
        )
        len(sold_vouchers)  # dirty hack
        sold_vouchers_value_gross = Decimal(
            sum([x.used_promo.value for x in sold_vouchers])
        )
        value = (
            order_total_value_gross
            + order_discount_value_gross
            + sold_vouchers_value_gross
        ).quantize(Decimal('.01'))
        value_components = [
            KPIValueComponent(
                name='order_value_gross',
                description='Value',
                value=order_total_value_gross,
            ),
            KPIValueComponent(
                name='order_discount_gross',
                description='Discount',
                value=order_discount_value_gross,
            ),
            KPIValueComponent(
                name='sold_vouchers_value_gross',
                description='Sold Vouchers',
                value=sold_vouchers_value_gross,
            ),
        ]
        return KPIValueObject(value=value, value_components=value_components)

    @property
    def name(self):
        return 'Order total gross + discounts value'
