# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion
import django.utils.timezone

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import kpi.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='ForecastCost',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('description', models.TextField(blank=True, null=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'month',
                    models.IntegerField(
                        choices=[
                            (1, 1),
                            (2, 2),
                            (3, 3),
                            (4, 4),
                            (5, 5),
                            (6, 6),
                            (7, 7),
                            (8, 8),
                            (9, 9),
                            (10, 10),
                            (11, 11),
                            (12, 12),
                        ]
                    ),
                ),
                ('transactions', models.IntegerField()),
                ('revenue_netto_in_k', models.IntegerField()),
                (
                    'user',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Forecast',
                'verbose_name_plural': 'Forecasts',
            },
        ),
        migrations.CreateModel(
            name='KPIValue',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('kpi_name', models.CharField(db_index=True, max_length=128)),
                ('bi_type', models.CharField(db_index=True, max_length=32)),
                (
                    'region',
                    models.CharField(
                        blank=True, db_index=True, max_length=32, null=True
                    ),
                ),
                ('segment_beginning', models.DateTimeField(db_index=True)),
                ('segment_end', models.DateTimeField(db_index=True)),
                ('utms', models.CharField(max_length=512)),
                ('utms_hash', models.CharField(db_index=True, max_length=32)),
                ('value', kpi.models.KPIValueField()),
                ('generated_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
        migrations.CreateModel(
            name='MarketingNonperformanceCost',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=64)),
                ('date_at', models.DateField(default=django.utils.timezone.now)),
                (
                    'description',
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                ('cost_amount', models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    'countries',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('romania', 'romania'),
                            ('portugal', 'portugal'),
                            ('czech', 'czech'),
                            ('austria', 'austria'),
                            ('greece', 'greece'),
                            ('hungary', 'hungary'),
                            ('italy', 'italy'),
                            ('estonia', 'estonia'),
                            ('lithuania', 'lithuania'),
                            ('luxembourg', 'luxembourg'),
                            ('france', 'france'),
                            ('slovakia', 'slovakia'),
                            ('ireland', 'ireland'),
                            ('norway', 'norway'),
                            ('slovenia', 'slovenia'),
                            ('germany', 'germany'),
                            ('belgium', 'belgium'),
                            ('spain', 'spain'),
                            ('netherlands', 'netherlands'),
                            ('denmark', 'denmark'),
                            ('poland', 'poland'),
                            ('finland', 'finland'),
                            ('sweden', 'sweden'),
                            ('latvia', 'latvia'),
                            ('croatia', 'croatia'),
                            ('united_kingdom', 'united_kingdom'),
                            ('switzerland', 'switzerland'),
                            ('bulgaria', 'bulgaria'),
                        ],
                        max_length=70,
                        null=True,
                    ),
                ),
                (
                    'category',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('pinterest', 'PINTEREST'),
                            ('other_channels', 'CHANNELS OTHER'),
                            ('test_channels', 'TEST CHANNELS'),
                            ('brand', 'BRAND OTHER'),
                            ('visual', 'VISUAL CONTENT'),
                            ('outsource', 'OUTSOURCE'),
                            ('tools', 'TOOLS'),
                            ('referal', 'REFERAL'),
                            ('other', 'OTHER'),
                        ],
                        max_length=70,
                        null=True,
                    ),
                ),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='MarketingNonperformanceRevenue',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=64)),
                ('date_at', models.DateField(default=django.utils.timezone.now)),
                (
                    'description',
                    models.CharField(blank=True, max_length=256, null=True),
                ),
                (
                    'revenue_amount',
                    models.DecimalField(decimal_places=2, max_digits=12),
                ),
                (
                    'countries',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('romania', 'romania'),
                            ('portugal', 'portugal'),
                            ('czech', 'czech'),
                            ('austria', 'austria'),
                            ('greece', 'greece'),
                            ('hungary', 'hungary'),
                            ('italy', 'italy'),
                            ('estonia', 'estonia'),
                            ('lithuania', 'lithuania'),
                            ('luxembourg', 'luxembourg'),
                            ('france', 'france'),
                            ('slovakia', 'slovakia'),
                            ('ireland', 'ireland'),
                            ('norway', 'norway'),
                            ('slovenia', 'slovenia'),
                            ('germany', 'germany'),
                            ('belgium', 'belgium'),
                            ('spain', 'spain'),
                            ('netherlands', 'netherlands'),
                            ('denmark', 'denmark'),
                            ('poland', 'poland'),
                            ('finland', 'finland'),
                            ('sweden', 'sweden'),
                            ('latvia', 'latvia'),
                            ('croatia', 'croatia'),
                            ('united_kingdom', 'united_kingdom'),
                            ('switzerland', 'switzerland'),
                            ('bulgaria', 'bulgaria'),
                        ],
                        max_length=70,
                        null=True,
                    ),
                ),
                (
                    'category',
                    models.CharField(
                        blank=True,
                        choices=[
                            ('pinterest', 'PINTEREST'),
                            ('other_channels', 'CHANNELS OTHER'),
                            ('test_channels', 'TEST CHANNELS'),
                            ('brand', 'BRAND OTHER'),
                            ('visual', 'VISUAL CONTENT'),
                            ('outsource', 'OUTSOURCE'),
                            ('tools', 'TOOLS'),
                            ('referal', 'REFERAL'),
                            ('other', 'OTHER'),
                        ],
                        max_length=70,
                        null=True,
                    ),
                ),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='ShelfStatusLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('user_id', models.IntegerField(blank=True, null=True)),
                ('shelf_id', models.IntegerField(blank=True, null=True)),
                ('log_created_at', models.DateTimeField(blank=True, null=True)),
                ('base_preset', models.IntegerField(blank=True, null=True)),
                ('source', models.CharField(blank=True, max_length=250, null=True)),
            ],
            options={
                'verbose_name': 'ShelfStatusLog',
                'verbose_name_plural': 'Shelf status logs',
            },
        ),
        migrations.AlterUniqueTogether(
            name='kpivalue',
            unique_together=set(
                [
                    (
                        'kpi_name',
                        'bi_type',
                        'utms_hash',
                        'segment_beginning',
                        'segment_end',
                        'region',
                    )
                ]
            ),
        ),
    ]
