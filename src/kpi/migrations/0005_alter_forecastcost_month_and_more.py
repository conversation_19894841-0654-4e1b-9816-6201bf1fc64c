# Generated by Django 4.1.8 on 2023-07-14 14:39

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('kpi', '0004_alter_kpivalue_value'),
    ]

    operations = [
        migrations.AlterField(
            model_name='forecastcost',
            name='month',
            field=models.IntegerField(
                choices=[
                    (1, 'Jan'),
                    (2, 'Feb'),
                    (3, 'Mar'),
                    (4, 'Apr'),
                    (5, 'May'),
                    (6, 'June'),
                    (7, 'July'),
                    (8, 'Aug'),
                    (9, 'Sept'),
                    (10, 'Oct'),
                    (11, 'Nov'),
                    (12, 'Dec'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='marketingnonperformancecost',
            name='category',
            field=models.CharField(
                blank=True,
                choices=[
                    ('pinterest', 'Pinterest'),
                    ('other_channels', 'Channels Other'),
                    ('test_channels', 'Test Channels'),
                    ('brand', 'Brand Other'),
                    ('visual', 'Visual Content'),
                    ('outsource', 'Outsource'),
                    ('tools', 'Tools'),
                    ('referal', 'Referral'),
                    ('programmatic', 'Programmatic'),
                    ('other', 'Other'),
                ],
                max_length=70,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='marketingnonperformancecost',
            name='countries',
            field=models.CharField(
                blank=True,
                choices=[
                    ('romania', 'Romania'),
                    ('portugal', 'Portugal'),
                    ('czech', 'Czech'),
                    ('austria', 'Austria'),
                    ('greece', 'Greece'),
                    ('hungary', 'Hungary'),
                    ('italy', 'Italy'),
                    ('estonia', 'Estonia'),
                    ('lithuania', 'Lithuania'),
                    ('luxembourg', 'Luxembourg'),
                    ('france', 'France'),
                    ('slovakia', 'Slovakia'),
                    ('ireland', 'Ireland'),
                    ('norway', 'Norway'),
                    ('slovenia', 'Slovenia'),
                    ('germany', 'Germany'),
                    ('belgium', 'Belgium'),
                    ('spain', 'Spain'),
                    ('netherlands', 'Netherlands'),
                    ('denmark', 'Denmark'),
                    ('poland', 'Poland'),
                    ('finland', 'Finland'),
                    ('sweden', 'Sweden'),
                    ('latvia', 'Latvia'),
                    ('croatia', 'Croatia'),
                    ('united_kingdom', 'United Kingdom'),
                    ('switzerland', 'Switzerland'),
                    ('bulgaria', 'Bulgaria'),
                ],
                max_length=70,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='marketingnonperformancerevenue',
            name='category',
            field=models.CharField(
                blank=True,
                choices=[
                    ('pinterest', 'Pinterest'),
                    ('other_channels', 'Channels Other'),
                    ('test_channels', 'Test Channels'),
                    ('brand', 'Brand Other'),
                    ('visual', 'Visual Content'),
                    ('outsource', 'Outsource'),
                    ('tools', 'Tools'),
                    ('referal', 'Referral'),
                    ('programmatic', 'Programmatic'),
                    ('other', 'Other'),
                ],
                max_length=70,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='marketingnonperformancerevenue',
            name='countries',
            field=models.CharField(
                blank=True,
                choices=[
                    ('romania', 'Romania'),
                    ('portugal', 'Portugal'),
                    ('czech', 'Czech'),
                    ('austria', 'Austria'),
                    ('greece', 'Greece'),
                    ('hungary', 'Hungary'),
                    ('italy', 'Italy'),
                    ('estonia', 'Estonia'),
                    ('lithuania', 'Lithuania'),
                    ('luxembourg', 'Luxembourg'),
                    ('france', 'France'),
                    ('slovakia', 'Slovakia'),
                    ('ireland', 'Ireland'),
                    ('norway', 'Norway'),
                    ('slovenia', 'Slovenia'),
                    ('germany', 'Germany'),
                    ('belgium', 'Belgium'),
                    ('spain', 'Spain'),
                    ('netherlands', 'Netherlands'),
                    ('denmark', 'Denmark'),
                    ('poland', 'Poland'),
                    ('finland', 'Finland'),
                    ('sweden', 'Sweden'),
                    ('latvia', 'Latvia'),
                    ('croatia', 'Croatia'),
                    ('united_kingdom', 'United Kingdom'),
                    ('switzerland', 'Switzerland'),
                    ('bulgaria', 'Bulgaria'),
                ],
                max_length=70,
                null=True,
            ),
        ),
    ]
