from django.db import models


class CountriesMarketing(models.TextChoices):
    ROMANIA = 'romania'
    PORTUGAL = 'portugal'
    CZECH = 'czech'
    AUSTRIA = 'austria'
    GREECE = 'greece'
    HUNGARY = 'hungary'
    ITALY = 'italy'
    ESTONIA = 'estonia'
    LITHUANIA = 'lithuania'
    LUXEMBOURG = 'luxembourg'
    FRANCE = 'france'
    SLOVAKIA = 'slovakia'
    IRELAND = 'ireland'
    NORWAY = 'norway'
    SLOVENIA = 'slovenia'
    GERMANY = 'germany'
    BELGIUM = 'belgium'
    SPAIN = 'spain'
    NETHERLANDS = 'netherlands'
    DENMARK = 'denmark'
    POLAND = 'poland'
    FINLAND = 'finland'
    SWEDEN = 'sweden'
    LATVIA = 'latvia'
    CROATIA = 'croatia'
    UNITED_KINGDOM = 'united_kingdom'
    SWITZERLAND = 'switzerland'
    BULGARIA = 'bulgaria'


class CategoryForCosts(models.TextChoices):
    PINTEREST = 'pinterest'
    CHANNELS_OTHER = 'other_channels'
    TEST_CHANNELS = 'test_channels'
    BRAND_OTHER = 'brand'
    VISUAL_CONTENT = 'visual'
    OUTSOURCE = 'outsource'
    TOOLS = 'tools'
    REFERRAL = 'referal'
    PROGRAMMATIC = 'programmatic'
    OTHER = 'other'
