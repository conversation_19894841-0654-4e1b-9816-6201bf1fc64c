{% load kpi_tags %}
        <td class='kpi_value_td' style="{% if last %}background-color: #F2F2F2; font-weight: bold; {% endif %};min-width: 100px; width: 100px; max-width: 100px; position:relative">
            <span class="kpi_value_mainvalue">{% kpi_post_processing kpi v.value %}{{ kpi.value_suffix }}</span><br/>

            {% if v.difference_nominal %}
                <span style="color:{% if v.difference_nominal > 0 %}{{ kpi.color_positive }}; {% else %}{{ kpi.color_negative }}; {% endif %}font-size: {% if v.detailed %}80%{% else %}70%{% endif %}">

                {% if v.detailed %}
                    {% if v.difference_nominal > 0 %}
                        +
                    {% endif %}
                        {{ v.difference_nominal }} /
                {% endif %}

                {{ v.difference_percentage|floatformat:0 }}%

                </span><br/>

            {% endif %}

            {% if share %}<span style="font-size: 80%; font-style: italic">{{ v.share|floatformat:0 }}%</span>{% endif %}
            <br/>{% if last %}<a class="see-more" href="#">see more</a>{% endif %}<div class="kpi_value_components"><br/>
            {% block value_components %}
                {% for vc in v.value_components %}{{ vc.description }}: {% kpi_post_processing kpi vc.value %} ({% component_share v.value_components vc.value %}%)<br/>{% endfor %}
            {% endblock %}
            </div>
        </td>
