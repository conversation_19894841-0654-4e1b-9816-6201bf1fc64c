{% extends 'kpis/kpi_noshare.html' %}
{% load kpi_tags %}
{% load util_tags %}
{% block kpi_value %}

    {% if 'reverse' in request.GET %}
        <td style="background-color: #D2F9D6;">{% if kpi.prediction_based_on_last_segment != None %}
            {% kpi_post_processing kpi kpi.prediction_based_on_last_segment %}{{ kpi.value_suffix }}{% else %}-{% endif %}</td>
        {% for v in values|reverselist %}{% show_kpi_value_string_components kpi v False forloop.first %}{% endfor %}
    {% else %}
        {% for v in values %}{% show_kpi_value_string_components kpi v False forloop.last %}{% endfor %}
    {% endif %}

{% endblock %}
