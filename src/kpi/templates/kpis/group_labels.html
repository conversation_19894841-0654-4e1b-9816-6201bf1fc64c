{% load kpi_tags %}
{% load util_tags %}
<div class="table-responsive table-bordered">
        <table class="table table-fixed">
            <thead>
            <tr>
                <th style="min-width:150px; width: 150px; max-width: 150px">{{ group.name }}</th>
            </tr>
            </thead>
            <tbody>
            {% for kpi in group.kpis %}{% if not kpi.is_dependency %}
                {{ kpi.as_html_header }}
            {% endif %}{% endfor %}
            </tbody>
        </table>
        <table class="table table-offset">
            <thead>
            <tr>
                {% for n, segment in group.time_segments.segments.items %}
                <th style="min-width: 100px; width: 100px; max-width: 100px">
                    {% time_segment_label group.time_segments n %}<br/>
                    {{ segment.0|date:group.time_segments.get_date_format }}-{{ segment.1|date:group.time_segments.get_date_format }}
                </th>
                {% endfor %}
                <th style="min-width: 100px; width: 100px; max-width: 100px">Prediction</th>
                <th style="min-width: 100px; width: 100px; max-width: 100px">Trend</th>
                <th style="text-align: center; width: 120px; max-width: 120px">Chart</th>
            </tr>
            </thead>
            <tbody>
            {% for kpi in group.kpis %}{% if not kpi.is_dependency %}
                {% kpi_as_html kpi %}
            {% endif %}{% endfor %}
            </tbody>
        </table>
</div>
