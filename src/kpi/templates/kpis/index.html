{% extends "admin/base_site.html" %}
{% load kpi_tags %}

{% block title %}KPIs{% endblock %}

{% block extrahead %}
    {{ block.super }}
    <style>
    .table>tbody>tr:nth-of-type(odd)>td {
        background-color: #f9f9f9;
    }
    .table>tbody>tr:nth-of-type(even)>td {
        background-color: #fff;
    }
    .table-fixed tbody tr {
        border-right: 1px solid #eee;
    }
    .table-fixed {
        position: absolute;
        float:left;
        width:120px;
        height: 50px;
        z-index: 1;
    }
    .table-fixed thead tr, .table-offset thead tr  {
        height: 35px;
    }
    .table-fixed tbody tr, .table-offset tbody tr  {
        height: 90px;
    }
    .table-offset {
        margin-left: 170px;
        float:left;
    }

    .kpi_value_components {
        display: none;
    }

    .kpi_value_mainvalue {
        font-size: 13px;
    }
    .kpi_value_td {
        min-width:150px;
        width: 150px;
        max-width: 150px;
        vertical-align: top;
        padding-top: 10px;
    }
    .panel-kpi-tools .row {
        margin: 15px 0;
    }
    .scroller-wrapper {
        width: 100%;
        height: 18px;
        overflow-x: scroll;
        overflow-y: hidden;
        background-color: #eee;
    }
    .scroller-wrapper:not(:first-child) {
        display: none;
    }

    .scroller-helper {
        width: auto; /* calculation based on tableWidth variable */
        height: 18px;
    }
    </style>
    <script type="application/javascript">
        if (!$) {
            $ = django.jQuery;
        }
        $(document).ready(function(){
            $('a.see-more').click(function(e){
                e.preventDefault();
                var kpi_component = $(e.target).parent().find('.kpi_value_components');
                var kpi_tr = kpi_component.closest('tr');
                kpi_tr.find('.kpi_value_components').toggle();
                var kpi_table = kpi_tr.closest('.table-responsive');
                var kpi_index = kpi_table.find('.table-offset > tbody tr').index(kpi_tr);
                $(kpi_table.find('.table-fixed > tbody tr')[kpi_index]).css('height', kpi_tr.height() + 'px');
                });

            /* Double Scroll Enable Function */
            $( window ).on( "load resize", function() {
                    var tableWidth = $('.table-fixed').width() + $('.table-offset').width();
                    var tableContainer = $(".panel-kpi-tables");
                    var sDiv =$('.scroller-helper');
                    var sWrapper = $('.scroller-wrapper');

                    sDiv.width( tableWidth );

                    if ( sWrapper.width() > tableWidth) {
                        sWrapper.css('visibility', 'hidden');
                    } else {
                         sWrapper.css('visibility', 'visible');
                    }

                    $('.panel-kpi-tables, .scroller-wrapper').scroll( function() {
                        $('.panel-kpi-tables, .scroller-wrapper').scrollLeft( $(this).scrollLeft() );
                    });

            /* END - Double Scroll Enable Function */

            /* Adjusting .table-fixed row height */

            adjustRowHeight();

            });

            function adjustRowHeight() {

                var fixedTableRows = $('.table-fixed tbody tr');
                var offsetTableRows =  $('.table-offset tbody tr');
                var defaultCssRowHeight = fixedTableRows.eq(0).height();

                offsetTableRows.each(function(index){

                    if ( $(this).height() > defaultCssRowHeight ) {

                        var biggerRowHeight =  $(this).height();
                        fixedTableRows.eq(index).height(biggerRowHeight);
                    }
                });
            }

            /* END - Adjusting .table-fixed row height */
        });

    </script>
{% endblock %}

{% block content %}
    {% if wait %}
        <div class="panel panel-default" >
        <div class="row">
            The page is being calculated. Please come back in a few minutes.
        </div>
    </div>
    {% else %}
    {% for group in groups %}

    <div class="scroller-wrapper">
        <div class="scroller-helper"></div>
    </div>

    <div class="panel panel-default panel-kpi-tables" style="overflow: auto">
        <div class="panel-body magic">
            {% kpi_as_html group %}
        </div>
    </div>
    {% endfor %}

    <div class="panel panel-default panel-kpi-tools" >
        <div class="row">
            View created: {% now "SHORT_DATETIME_FORMAT" %}
            <a href="?csv" class="addlink button">Get CSV export</a>
        </div>
        <div class="row">
            <form action="" method="get" style="float: left; margin-right: 5px">
                <select name="region">
                    <option value selected></option>
                    {% for r in regions %}<option value="{{ r|iriencode }}">{{ r }}</option>{% endfor %}
                </select>
                <button type="submit" class="btn-cta">Set region</button>
            </form>
            <form action="" method="get" style="float: left">
                    <label >Data
                    <select name="csv_data">
                        <option value selected></option>
                        <option value="users">All Users</option>
                        <option value="customers">Customers</option>
                        <option value="orders">Orders</option>
                    </select></label>
                    <input name="utm_campaign" type="hidden" value="{{ request.GET.utm_campaign }}"/>
                    <input name="utm_medium" type="hidden" value="{{ request.GET.utm_medium }}"/>
                    <input name="utm_source" type="hidden" value="{{ request.GET.utm_source }}"/>
                    <input name="utm_term" type="hidden" value="{{ request.GET.utm_term }}"/>
                    <button type="submit" class="btn-cta">Get</button>
            </form>
        </div>
    </div>
    {% endif %}
{% endblock %}
