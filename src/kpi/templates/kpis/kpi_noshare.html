{% load kpi_tags %}
{% load util_tags %}
<tr>

{% if only_header %}

    <td class="kpi_value_td">{{ kpi.name }}</td>

{% else %}

    {% block kpi_value %}

    {% if 'reverse' in request.GET %}
        <td style="background-color: #D2F9D6;">{% if kpi.prediction_based_on_last_segment != None %}{% kpi_post_processing kpi kpi.prediction_based_on_last_segment %}{{ kpi.value_suffix }}{% else %}-{% endif %}</td>
        {% for v in values|reverselist %}{% show_kpi_value kpi v False forloop.first %}{% endfor %}
    {% else %}
        {% for v in values %}{% show_kpi_value kpi v False forloop.last %}{% endfor %}
    {% endif %}

    {% endblock %}

    {% if 'reverse' not in request.GET %}<td style="background-color: #D2F9D6;">{% if kpi.prediction_based_on_last_segment != None %}{% kpi_post_processing kpi kpi.prediction_based_on_last_segment.value %}{{ kpi.value_suffix }}<div class="kpi_value_components"><br/>
                {% for vc in kpi.prediction_based_on_last_segment.value_components %}{{ vc.description }}: {% kpi_post_processing kpi vc.value %} ({% component_share v.value_components vc.value %}%)<br/>{% endfor %}
            </div>{% else %}-{% endif %}</td>{% else %}{% endif %}
    <td style="background-color: #D2F9D6;">{% kpi_post_processing kpi kpi.prediction_based_on_trend %}{{ kpi.value_suffix }}</td>
    <td style="background-color: #D2F9D6;"><embed src="//sparksvg.me/line.svg?{{ kpi.chart }}" style="max-height:30px; max-width:120px" type="image/svg+xml"></embed></td>
{% endif %}
</tr>
