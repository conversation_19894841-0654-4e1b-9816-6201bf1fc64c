import hashlib
import json
import logging
import os
import time

from datetime import datetime
from urllib.parse import quote

from django.conf import settings
from django.contrib.auth import get_user_model
from django.contrib.contenttypes.models import ContentType
from django.core.cache import cache
from django.db.models import (
    F,
    Q,
)
from django.test.client import Client
from django.urls import reverse
from django.utils import timezone
from django.utils.encoding import force_bytes

from celery import shared_task
from celery.utils.log import get_task_logger
from google.cloud.exceptions import GoogleCloudError

from custom.metrics import (
    metrics_client,
    task_metrics,
)
from custom.utils.decorators import notify_after
from custom.utils.import_object import import_object
from custom.utils.slack import log_bigquery_error_to_slack
from kpi.constants import KPI_INTERVAL_MONTHLY
from orders.enums import OrderStatus
from orders.models import PaidOrdersGeo
from payments.models import PrimerNotification
from payments.serializers import PrimerNotificationBigQuerySerializer

User = get_user_model()
logger = logging.getLogger('cstm')
task_logger = get_task_logger('celery_task')

MAX_PRICING_ETL_BATCH = 500
LAST_PRODUCT_WE_IGNORE_ID = 12000
MAX_COGS_ETL_BATCH = 3000

try:
    from google.cloud import bigquery
except ImportError:
    logger.exception('Error while importing bigquery from google.cloud')


@shared_task
def periodic_regenerate_kpis_view():
    path = reverse(
        'admin:kpis',
        kwargs={
            'board_name': 'bi',
            'time_interval': KPI_INTERVAL_MONTHLY,
        },
    )
    task_regenerate_kpis_view(path)
    path = reverse(
        'admin:kpis',
        kwargs={
            'board_name': 'all',
            'time_interval': KPI_INTERVAL_MONTHLY,
        },
    )
    task_regenerate_kpis_view(path)


@shared_task
def task_regenerate_kpis_view(path, notify_email=None):
    @notify_after(subject='KPIs have finished regenerating')
    def _regenerate_kpis_view():
        path_hash = hashlib.md5(force_bytes(quote(path))).hexdigest()
        cache_key = 'kpis_regeneration_%s' % path_hash
        if cache.get(cache_key) is None:
            time_start = time.time()
            cache.set(cache_key, True, 900)
            path_regenerating = '%s%sregenerating' % (path, '&' if '?' in path else '?')
            c = Client()
            c.force_login(
                user=User.objects.get_by_natural_key(
                    settings.CELERY_SUPERUSER_USERNAME,
                ),
            )
            c.get(path_regenerating)
            cache.delete(cache_key)
            generation_time = time.time() - time_start
            path_split = path_regenerating.split('/')
            if 'csv' not in path_regenerating:
                metrics_client().timing(
                    'backend.kpi.generation_time',
                    generation_time,
                    tags=['board:%s_%s' % (path_split[-3], path_split[-2])],
                )

    notify_to = (notify_email,) if notify_email else tuple()
    _regenerate_kpis_view(__body='https://tylko.com%s' % path, __to=notify_to)


@shared_task
def task_kpis_generate_value(group, group_name, kpi, bi_type, utm_filters, *args):
    if group_name != '':
        group = import_object('kpi.kpis.%s' % group)('')
        from kpi.utils import get_kpi_class

        kpi = get_kpi_class(kpi)(group, bi_type=bi_type, utm_filters=utm_filters)
        kpi.get_value(*args)


@shared_task
@task_metrics
def regenerate_kpi_bi():
    PaidOrdersGeo.find_geodatas()


@shared_task
@task_metrics
def export_marketing_nonperformance_cost_to_big_query():
    from kpi.models import MarketingNonperformanceCost

    client = bigquery.Client()
    dataset_ref = client.dataset('marketing')
    table_ref = dataset_ref.table('marketing_nonperformance_cost')
    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
    job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE

    data = MarketingNonperformanceCost.objects.order_by('-id')[:5000]

    from django.core.serializers.json import DjangoJSONEncoder

    result = [
        json.dumps(record.biq_query_export(), cls=DjangoJSONEncoder) for record in data
    ]

    file_name = (
        datetime.today()
        .isoformat()
        .replace(':', '_')
        .replace('.', '_')
        .replace('-', '_')
    )
    dir = 'biquery'
    path = '{}/{}'.format(os.path.expanduser('~'), dir)
    try:
        os.makedirs(path)
    except OSError:
        if not os.path.isdir(path):
            raise
    file_with_path = '{}/marketing_nonperformance_cost{}.json'.format(path, file_name)
    with open(file_with_path, 'w') as outfile:
        outfile.write('\n'.join(result))
    with open(file_with_path, 'rb') as source_file:
        job = client.load_table_from_file(
            source_file, table_ref, location='EU', job_config=job_config
        )

    try:
        job.result()
    except GoogleCloudError as err:
        log_bigquery_error_to_slack(err, 'marketing.marketing_nonperformance_cost')
        raise


@shared_task
@task_metrics
def export_geo_to_big_query_cron():
    from kpi.big_query import export_geo_to_big_query

    export_geo_to_big_query()


@shared_task
@task_metrics
def export_batch_history_cron():
    from kpi.big_query import export_batch_history

    export_batch_history()


@shared_task
@task_metrics
def export_loose_ends_to_big_query_cron():
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import KPILooseEndSerializer
    from loose_ends.models import LooseEnd

    queryset = LooseEnd.objects.filter(exported_to_big_query=None)

    export_serializer_to_big_query(
        'logistics_data',
        'raw_loose_ends',
        model=LooseEnd,
        queryset=queryset,
        serializer=KPILooseEndSerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
    )
    queryset.update(exported_to_big_query=timezone.now())


@shared_task
@task_metrics
def export_complaints_to_big_query_cron():
    from complaints.models import Complaint
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import KPIComplaintSerializer

    queryset = Complaint.objects.filter(exported_to_big_query=None).select_related(
        'product',
        'typical_issues',
        'complaint_type',
        'area',
        'responsibility',
    )

    export_serializer_to_big_query(
        'complaints',
        'complaints_new',
        model=Complaint,
        queryset=queryset,
        serializer=KPIComplaintSerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
    )
    queryset.update(exported_to_big_query=timezone.now())


@shared_task
@task_metrics
def export_free_returns_to_big_query_cron():
    from free_returns.models import FreeReturn
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import FreeReturnSerializer

    export_serializer_to_big_query(
        'free_returns',
        'free_return',
        model=FreeReturn,
        serializer=FreeReturnSerializer,
        write=bigquery.WriteDisposition.WRITE_TRUNCATE,
    )


@shared_task
@task_metrics
def export_insta_grid_images_to_big_query_cron():
    from gallery.enums import FurnitureImageType
    from gallery.models import FurnitureImage
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import FurnitureImageBigQuerySerializer

    export_serializer_to_big_query(
        'marketing',
        'instagrid_images',
        model=FurnitureImage,
        queryset=FurnitureImage.objects.filter(
            type=FurnitureImageType.INSTA_GRID_IMAGE,
        ),
        serializer=FurnitureImageBigQuerySerializer,
        write=bigquery.WriteDisposition.WRITE_TRUNCATE,
    )


@shared_task
@task_metrics
def export_estimated_delivery_time_per_day_cron():
    from django.db import connection

    from kpi.big_query import export_list_to_big_query
    from kpi.big_query_sql import min_estimated_delivery_time_per_type

    with connection.cursor() as cursor:
        cursor.execute(min_estimated_delivery_time_per_type)
        row = cursor.fetchall()
    export_list_to_big_query(
        'sold_items',
        'estimated_delivery_time_per_day_RAW',
        row,
        write=bigquery.WriteDisposition.WRITE_TRUNCATE,
    )


@shared_task
@task_metrics
def export_staff_to_big_query_cron_product_status_log_packaging():
    from kpi.big_query import (
        export_list_to_big_query,
        export_serializer_to_big_query,
    )
    from kpi.serializers import ProductStatusHistorySerializer
    from producers.models import (
        Product,
        ProductStatusHistory,
    )

    client = bigquery.Client()
    query = (
        'SELECT max(changed_at) '
        'FROM `tylko-bi-200712.production_info.product_status_history`'
    )

    query_job = client.query(
        query,
        # Location must match that of the dataset(s) referenced in the query.
        location='EU',
    )  # API request - starts the query

    last_changed_at_date = None
    for row in query_job:  # API request - fetches results
        # Row values can be accessed by field name or index
        last_changed_at_date = row[0]

    export_serializer_to_big_query(
        'production_info',
        'product_status_history',
        model=ProductStatusHistory,
        serializer=ProductStatusHistorySerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
        queryset=ProductStatusHistory.objects.filter(
            changed_at__gte=last_changed_at_date
        ),
    )

    query = (
        'SELECT max(paid_at) '
        'FROM `tylko-bi-200712.production_info.packaging` LIMIT 1000'
    )

    query_job = client.query(
        query,
        # Location must match that of the dataset(s) referenced in the query.
        location='EU',
    )  # API request - starts the query

    last_paid_at_date = None
    for row in query_job:  # API request - fetches results
        # Row values can be accessed by field name or index
        last_paid_at_date = row[0]

    # packaging, for not so old only

    packagingList = []
    for x in Product.objects.filter(
        id__gte=14321, order__paid_at__gte=last_paid_at_date
    ):
        try:
            packagingList.append(x.get_packaging_structure_for_export())
        except Exception:
            pass
    packagingList = [item for sublist in packagingList for item in sublist]
    export_list_to_big_query(
        'production_info',
        'packaging',
        packagingList,
        write=bigquery.WriteDisposition.WRITE_APPEND,
    )


@shared_task
@task_metrics
def export_products_to_big_query_cron_product_cogs_and_pricing():
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import ProductChangesBigQuerySerializer
    from producers.models import Product

    client = bigquery.Client()
    query = (
        'SELECT max(updated_at) FROM `tylko-bi-200712.production_info.production_cogs`'
    )

    query_job = client.query(
        query,
        # Location must match that of the dataset(s) referenced in the query.
        location='EU',
    )  # API request - starts the query

    last_updated_at_date = None
    for row in query_job:  # API request - fetches results
        # Row values can be accessed by field name or index
        last_updated_at_date = row[0]

    queryset = Product.all_objects.filter(
        cached_product_type__in=Product.JETTY_WATTY,
        id__gte=LAST_PRODUCT_WE_IGNORE_ID,
    ).order_by('updated_at')
    if last_updated_at_date is not None:
        queryset = queryset.filter(updated_at__gt=last_updated_at_date)

    queryset = queryset[:MAX_COGS_ETL_BATCH]

    export_serializer_to_big_query(
        'production_info',
        'production_cogs',
        model=Product,
        serializer=ProductChangesBigQuerySerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
        queryset=queryset,
    )


@shared_task
@task_metrics
def export_samples_to_big_query():
    """
    Inspired by tools_and_reports/slawek/export_order_with_samples.py
    """

    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import SampleBoxBigQuerySerializer
    from orders.models import OrderItem

    sample_box = ContentType.objects.get(model='samplebox')
    queryset = OrderItem.objects.filter(
        content_type=sample_box, exported_to_big_query__isnull=True
    ).exclude(order__status=OrderStatus.CART)

    export_serializer_to_big_query(
        'free_returns',
        'samples_raw',
        model=OrderItem,
        serializer=SampleBoxBigQuerySerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
        queryset=queryset,
    )
    queryset.update(exported_to_big_query=datetime.now())


@shared_task
def export_cash_flow_to_big_query(elements_order_id):
    from kpi.big_query import export_serializer_to_big_query
    from kpi.serializers import FinancialReportBigQuerySerializer
    from producers.reports.tasks_reports import generate_financial_report

    csv_file = generate_financial_report([elements_order_id], 'report.csv')
    keys = [
        'paid_at',
        'placed_at',
        'production_date',
        'delivery_date',
        'order_id',
        'product_id',
        'elements_order_id',
        'batch_id',
        'manufacturer_name',
        'invoice_no',
        'priority',
        'codename',
        'tylko_quantity',
        'tylko_price',
        'tylko_losses',
        'tylko_material_consumption_with_losses',
        'tylko_cost_with_losses',
        'factory_quantity',
        'factory_price',
        'factory_losses',
        'factory_material_consumption_with_losses',
        'factory_cost_with_losses',
        'factory_cost_of_management',
        'factory_total_cost',
        'summary_quantity',
        'summary_price',
        'summary_losses',
        'summary_material_consumption_with_losses',
        'summary_cost_with_losses',
        'summary_cost_of_management',
        'summary_total_cost',
    ]

    data = []
    for row_idx, row in enumerate(csv_file.content.decode('utf-8').splitlines()):
        row = row.split(';')
        if row_idx == 0:
            continue
        data_dict = {}
        for column_idx, key in enumerate(keys):
            if not row[column_idx]:
                continue
            else:
                data_dict[key] = row[column_idx]
        data.append(data_dict)

    export_serializer_to_big_query(
        'production_info',
        'production_cashflow',
        model=None,
        serializer=FinancialReportBigQuerySerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
        queryset=data,
    )


@shared_task
def update_cash_flow_data(elements_order_id):
    query = (
        f'UPDATE `tylko-bi-200712.production_info.production_cashflow` '
        f'SET `deleted` = true '
        f'WHERE `elements_order_id` = {elements_order_id}'
    )

    client = bigquery.Client()
    client.query(
        query,
        location='EU',
    )


@shared_task
@task_metrics
def export_primer_notification_to_big_query():
    from kpi.big_query import export_serializer_to_big_query

    queryset = PrimerNotification.objects.filter(
        Q(exported_to_big_query__isnull=True)
        | Q(exported_to_big_query__lt=F('updated_at'))
    )

    export_serializer_to_big_query(
        'production_info',
        'primer_notifications',
        model=PrimerNotification,
        serializer=PrimerNotificationBigQuerySerializer,
        write=bigquery.WriteDisposition.WRITE_APPEND,
        queryset=queryset,
    )
    queryset.update(exported_to_big_query=datetime.now())


@shared_task
def kpi_lead_time():
    from kpi.management.commands.kpi_leadtime import Command

    Command().handle()


@shared_task
def delayed_orders():
    from kpi.management.commands.delayed_orders import Command

    Command().handle()
