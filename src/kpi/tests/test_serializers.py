import json

from kpi.serializers import FreeReturnSerializer


def test_free_return_serializer_schema(db, free_return):
    serializer = FreeReturnSerializer(free_return)
    expected_keys = {
        'id',
        'orders',
        'notification_date',
        'is_packed',
        'is_need_packaging',
        'is_send_asap',
        'status',
        'reason',
        'reason_tag',
        'notes',
        'created_at',
        'updated_at',
        'finished_at',
        'aborted_at',
        'tracking_number',
    }

    assert set(serializer.data.keys()) == expected_keys
    assert serializer.data['orders'] == '[]'


def test_free_return_serializer_with_order_items(
    db,
    free_return_factory,
    order_item_factory,
):
    items = order_item_factory.create_batch(4)
    free_return = free_return_factory()
    free_return.orderitem_set.add(*items)
    serializer = FreeReturnSerializer(free_return)

    orders = serializer.data['orders']

    assert isinstance(orders, str)
    assert set(json.loads(orders)) == {item.order.pk for item in items}
