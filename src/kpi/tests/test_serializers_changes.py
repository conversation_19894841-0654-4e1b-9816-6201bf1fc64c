import pytest

from kpi.serializers import (
    FreeReturnSerializer,
    FurnitureImageBigQuerySerializer,
    KPIComplaintSerializer,
    KPILooseEndSerializer,
    ProductChangesBigQuerySerializer,
    ProductStatusHistorySerializer,
    SampleBoxBigQuerySerializer,
)


@pytest.mark.django_db
class BaseTestSerializer:
    serializer_class = None
    expected_fields = None

    def test_if_serializer_was_changed(self):
        assert self.expected_fields == list(self.serializer_class().fields.keys())


class TestKPIComplaintSerializer(BaseTestSerializer):
    serializer_class = KPIComplaintSerializer
    expected_fields = [
        'id',
        'owner_id',
        'product_id',
        'reporter_id',
        'reproduction_product',
        'elements_detailed',
        'item_id',
        'only_additional_elements',
        'previous_status',
        'status',
        'reported_date',
        'cs_forwarded_transport_damage_date',
        'production_ordered_date',
        'production_released_date',
        'cs_request_date',
        'is_repeated',
        'boxes_damaged',
        'notification_type',
        'shipment_date',
        'delivered_date',
        'packages_info_was_provided',
        'elements',
        'manufactor_fault',
        'logistic_dimensions_info',
        'logistic_weight',
        'material',
        'reproduction',
        'refund',
        'refund_reason',
        'free_return_prevention',
        'assembly_team_intervention',
        'additional_info',
        'logistic_additional_info',
        'production_additional_info',
        'conversation_link',
        'tnt_complaint_type',
        'tnt_order_number',
        'tnt_tracking_number',
        'tnt_complaint_date',
        'tnt_notification_channel',
        'tnt_notification_by_customer',
        'tnt_additional_info',
        'tnt_status',
        'tnt_final_result',
        'express_replacement',
        'created_at',
        'updated_at',
        'deleted',
        'complaint_type',
        'typical_issues',
        'area',
        'responsibility',
        'reproduction_element_categories',
        'priority',
        'reproduction_order_id',
        'total_cost',
        'created_by_assembly_team',
        'is_confirmed',
    ]


class TestFreeReturnSerializer(BaseTestSerializer):
    serializer_class = FreeReturnSerializer
    expected_fields = [
        'id',
        'orders',
        'notification_date',
        'is_packed',
        'is_need_packaging',
        'is_send_asap',
        'status',
        'reason',
        'reason_tag',
        'notes',
        'created_at',
        'updated_at',
        'finished_at',
        'aborted_at',
        'tracking_number',
    ]


class TestFurnitureImageBigQuerySerializer(BaseTestSerializer):
    serializer_class = FurnitureImageBigQuerySerializer
    expected_fields = ['id', 'furniture_object_id', 'image', 'color', 'furniture_type']


class TestKPILooseEndSerializer(BaseTestSerializer):
    serializer_class = KPILooseEndSerializer
    expected_fields = [
        'id',
        'category',
        'department',
        'value',
        'currency',
        'accounting_date',
        'description',
        'created_at',
        'updated_at',
        'deleted_at',
        'object_id',
        'contractor',
        'c_invoice',
        'order',
        'owner',
        'content_type',
        'invoice',
    ]


class TestProductStatusHistorySerializer(BaseTestSerializer):
    serializer_class = ProductStatusHistorySerializer
    expected_fields = [
        'changed_at',
        'item_id',
        'product_id',
        'status',
        'previous_status',
    ]


class TestProductChangesBigQuerySerializer(BaseTestSerializer):
    serializer_class = ProductChangesBigQuerySerializer
    expected_fields = [
        'item_id',
        'product_id',
        'order_id',
        'material',
        'shelf_type',
        'weight',
        'status',
        'features',
        'cogs',
        'exported_at',
        'updated_at',
        'created_at',
        'gallery_id',
        'batch',
        'manufactor',
        'region_price',
        'region_price_net',
        'price',
        'price_net',
        'region',
        'export_type',
        'deleted',
    ]


class TestSampleBoxBigQuerySerializer(BaseTestSerializer):
    serializer_class = SampleBoxBigQuerySerializer
    expected_fields = [
        'order_id',
        'paid_at',
        'email',
        'country',
        'total_price',
        'promo_amount',
        'profil_lang',
        'name',
        'sent_to_customer',
        'delivered_date',
        'price',
        'box_variant',
        'box_samples',
        'box_name',
    ]
