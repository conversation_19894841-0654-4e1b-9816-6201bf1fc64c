import json
import logging
import os

from datetime import datetime

from google.cloud.exceptions import GoogleCloudError
from sentry_sdk import capture_exception

from custom.utils.decorators import production_only
from custom.utils.slack import log_bigquery_error_to_slack
from producers.errors import SerializationMissingError

logger = logging.getLogger('cstm')

try:
    from google.cloud import bigquery
except ImportError:
    logger.exception('Error while importing bigquery from google.cloud')


def get_data_for_export_to_big_query(queryset):
    from copy import deepcopy

    to_export = queryset.filter(exported_to_big_query__isnull=True)
    result = []
    for e in to_export:
        tmp = deepcopy(e.__dict__)
        tmp.pop('_state')
        tmp.pop('exported_to_big_query')
        result.append(json.dumps(tmp))
    return result, to_export


def export_geo_to_big_query():
    from regions.models import (
        GeoCity,
        GeoRegion,
    )

    client = bigquery.Client()
    tables = [
        ('geo_city', GeoCity.objects.all()),
        ('geo_region', GeoRegion.objects.all()),
    ]

    for table, queryset in tables:
        dataset_ref = client.dataset('sold_items')
        table_ref = dataset_ref.table(table)
        job_config = bigquery.LoadJobConfig()
        job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
        result, queryset = get_data_for_export_to_big_query(queryset)

        file_name = '{}-{}'.format(
            table,
            datetime.today()
            .isoformat()
            .replace(':', '_')
            .replace('.', '_')
            .replace('-', '_'),
        )
        directory = 'biquery'
        path = '{}/{}'.format(os.path.expanduser('~'), directory)
        try:
            os.makedirs(path)
        except OSError:
            if not os.path.isdir(path):
                raise
        file_with_path = '{}/{}.json'.format(path, file_name)
        with open(file_with_path, 'w') as outfile:
            outfile.write('\n'.join(result))

        with open(file_with_path, 'rb') as source_file:
            job = client.load_table_from_file(
                source_file, table_ref, location='EU', job_config=job_config
            )

        try:
            job.result()
        except GoogleCloudError as err:
            log_bigquery_error_to_slack(err, f'sold_items.{table}')
            raise

        for obj in queryset:
            obj.exported_to_big_query = datetime.today()
            obj.save(update_fields=['exported_to_big_query'])
    return job


def export_batch_history():
    from producers.models import ProductBatch

    client = bigquery.Client()

    dataset_ref = client.dataset('production_info')
    table_ref = dataset_ref.table('batch_history')
    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
    job_config.write_disposition = bigquery.WriteDisposition.WRITE_TRUNCATE

    result = [
        {
            'id': x.id,
            'manufactor': x.manufactor.name if x.manufactor else '-',
            'items': x.get_batch_items_number(),
            'created_at': x.created_at.isoformat(),
            'batch_type': x.get_batch_type_display(),
            'material_description': x.material_description,
            'batch_value': float(x.batch_value() or 0),
        }
        for x in ProductBatch.objects.all().prefetch_related('batch_details_jetty')
    ]
    result = [json.dumps(x) for x in result]

    file_name = '{}-{}'.format(
        'batch_history_info',
        datetime.today()
        .isoformat()
        .replace(':', '_')
        .replace('.', '_')
        .replace('-', '_'),
    )
    directory = 'biquery'
    path = '{}/{}'.format(os.path.expanduser('~'), directory)
    try:
        os.makedirs(path)
    except OSError:
        if not os.path.isdir(path):
            raise
    file_with_path = '{}/{}.json'.format(path, file_name)
    with open(file_with_path, 'w') as outfile:
        outfile.write('\n'.join(result))

    with open(file_with_path, 'rb') as source_file:
        job = client.load_table_from_file(
            source_file, table_ref, location='EU', job_config=job_config
        )

    try:
        job.result()
    except GoogleCloudError as err:
        log_bigquery_error_to_slack(err, 'production_info.batch_history')
        raise

    return job


def save_table_to_file(data, space, table):
    file_name = (
        datetime.today()
        .isoformat()
        .replace(':', '_')
        .replace('.', '_')
        .replace('-', '_')
    )
    directory = 'biquery'
    path = '{}/{}'.format(os.path.expanduser('~'), directory)
    try:
        os.makedirs(path)
    except OSError:
        if not os.path.isdir(path):
            raise
    file_path = '{}/{}_{}_{}.json'.format(path, space, table, file_name)
    with open(file_path, 'w') as outfile:
        outfile.write('\n'.join(data))
    return file_path


@production_only
def export_serializer_to_big_query(
    space,
    table,
    model,
    serializer,
    write=bigquery.WriteDisposition.WRITE_TRUNCATE,
    queryset=None,
    exported_to_big_query_date=False,
    logger_level='info',
    re_raise=False,
):
    client = bigquery.Client()
    dataset_ref = client.dataset(space)
    table_ref = dataset_ref.table(table)
    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
    job_config.write_disposition = write

    if queryset is None:
        data_to_export = model.objects.all()
    else:
        data_to_export = queryset

    rows = []
    for model_to_export in data_to_export:
        try:
            row = serializer(model_to_export).data
            if exported_to_big_query_date:
                row['exported_at'] = datetime.today().isoformat()
        except (ValueError, SerializationMissingError):
            # Fix for PS Value error
            continue
        rows.append(json.dumps(row))

    file_path = save_table_to_file(rows, space, table)

    with open(file_path, 'rb') as source_file:
        job = client.load_table_from_file(
            source_file,
            table_ref,
            location='EU',
            job_config=job_config,
        )
    try:
        return job.result()
    except Exception as err:
        log = getattr(logger, logger_level, 'info')
        log('Problem while exporting to big query', exc_info=True)
        capture_exception(err)
        log_bigquery_error_to_slack(err, f'{space}.{table}')
        if re_raise:
            raise err


def upload_list_to_big_query(
    space, table, my_list, write=bigquery.WriteDisposition.WRITE_TRUNCATE
):
    client = bigquery.Client()
    dataset_ref = client.dataset(space)
    table_ref = dataset_ref.table(table)
    job_config = bigquery.LoadJobConfig()
    job_config.source_format = bigquery.SourceFormat.NEWLINE_DELIMITED_JSON
    job_config.write_disposition = write
    result = [
        json.dumps(record[0] if isinstance(record, tuple) else record)
        for record in my_list
    ]
    file_path = save_table_to_file(result, space, table)
    with open(file_path, 'rb') as source_file:
        job = client.load_table_from_file(
            source_file, table_ref, location='EU', job_config=job_config
        )
    try:
        return job.result()
    except GoogleCloudError as err:
        log_bigquery_error_to_slack(err, f'{space}.{table}')
        raise


def export_list_to_big_query(
    space, table, my_list, write=bigquery.WriteDisposition.WRITE_TRUNCATE
):
    try:
        upload_list_to_big_query(space, table, my_list, write=write)
    except Exception as err:
        errors = getattr(err, 'errors', [err])
        for er in errors:
            logger.exception(
                'Error while importing bigquery from google.cloud %s %s %s',
                space,
                table,
                er,
            )
