from decimal import Decimal

from django import template

from past.utils import old_div

register = template.Library()


@register.simple_tag
def component_share(value_components, value):
    value = Decimal(value)
    values_sum = Decimal(sum([vc.value for vc in value_components]))
    if values_sum == 0:
        return 0
    return Decimal(old_div(100 * value, values_sum)).quantize(Decimal('1'))


@register.simple_tag(takes_context=True)
def kpi_as_html(context, kpi_template_interface):
    return kpi_template_interface.as_html(request=context['request'])


@register.simple_tag
def kpi_post_processing(kpi, kpi_value):
    return kpi.post_processing(kpi_value)


@register.simple_tag
def time_segment_label(time_segments, time_segment_n):
    return time_segments.labels[time_segment_n]


@register.inclusion_tag('kpis/components/tag_value.html')
def show_kpi_value(kpi, value, share=False, last=False):
    return {'kpi': kpi, 'last': last, 'share': share, 'v': value}


@register.inclusion_tag('kpis/components/tag_value_component_string.html')
def show_kpi_value_string_components(kpi, value, share=False, last=False):
    return {'kpi': kpi, 'last': last, 'share': share, 'v': value}


@register.simple_tag
def kpi_cache_key(kpi, time_segment_n):
    return kpi.get_cache_key(time_segment_n)
