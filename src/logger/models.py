from django.conf import settings
from django.db import models

from logger.choices import Source


class Log(models.Model):
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        null=True,
        on_delete=models.CASCADE,
    )
    logger_source = models.IntegerField(choices=Source.choices)
    action = models.CharField(max_length=50)
    model = models.CharField(max_length=80, db_index=True)
    model_id = models.IntegerField(db_index=True, null=True)
    url = models.CharField(max_length=80, null=True)
    additional_info = models.JSONField(null=True)
    data = models.JSONField(null=False)
    date = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return '{} {} {} {} {} {}'.format(
            self.user,
            self.logger_source,
            self.model,
            self.model_id,
            self.url,
            self.date,
        )


class Singleton(object):
    _instance = None

    def __new__(class_, *args, **kwargs):
        if not isinstance(class_._instance, class_):
            class_._instance = super(Singleton, class_).__new__(class_)
        return class_._instance


class IPythonTester(Singleton):
    init = False
    test = False

    def __init__(self, test=False):
        if self.init:
            return
        self.test = test
        self.init = True


def _is_ipython():
    test = IPythonTester()
    return test.test


class LoggerQuerySet(models.QuerySet):
    def update(self, **kwargs):
        if _is_ipython():
            import IPython

            items = [x.pk for x in self]
            profile_hist = IPython.core.history.HistoryAccessor(profile='default')
            z = list(profile_hist.get_tail(n=50))
            log = Log(
                user=None,
                logger_source=Source.LOGGER_IPYTHON,
                action='update',
                model=self.model._meta.label,
                model_id=None,
                data=items,
                additional_info=[kwargs, z],
            )
            log.save()
        return super(LoggerQuerySet, self).update(**kwargs)


class LoggerManager(models.Manager.from_queryset(LoggerQuerySet)):
    """
    Provides access to LoggerQuerySet, which includes special behavior for logging
    update operations when run inside an IPython environment (such as Jupyter).
    """


class LoggerMixin(models.Model):
    class Meta(object):
        abstract = True

    def save(self, *args, **kwargs):
        super(LoggerMixin, self).save(*args, **kwargs)
        if _is_ipython():
            import IPython

            profile_hist = IPython.core.history.HistoryAccessor(profile='default')
            z = list(profile_hist.get_tail(n=50))
            log = Log(
                user=None,
                logger_source=Source.LOGGER_ADMIN,
                action='save',
                model=self._meta.label,
                model_id=self.pk,
                data=z,
            )
            log.save()
