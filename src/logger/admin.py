from django.contrib import admin
from django.contrib.admin import helpers
from django.contrib.postgres import fields

from django_json_widget.widgets import J<PERSON>NEditorWidget

from .choices import Source
from .models import Log


class LoggerMixin(admin.ModelAdmin):
    def response_action(self, request, queryset):
        result = super(LoggerMixin, self).response_action(request, queryset)
        if getattr(self, 'log_actions', False):
            from django.contrib import messages

            msg = messages.get_messages(request)._queued_messages
            extra_info = {}
            for r in list(request.environ.keys()):
                if type(request.environ[r]) in (int, str, str):
                    extra_info[r] = request.environ[r]
            extra_info['MSG'] = [str(s) for s in msg]

            select_across = request.POST.get('select_across')
            selected = request.POST.getlist(helpers.ACTION_CHECKBOX_NAME)
            query = None
            if (select_across is None) or (
                select_across.isdigit() and not int(select_across)
            ):
                queryset = queryset.filter(pk__in=selected)
            else:
                query = repr(queryset.query)
            action = request.POST.get('post')
            extra_info = dict(list(request.POST.items()) + list(extra_info.items()))
            log = Log(
                user=request.user,
                logger_source=Source.LOGGER_ADMIN,
                action=request.POST['action'],
                model=self.model._meta.label,
                model_id=None,
                url=request.path,
                data=[x.pk for x in queryset] if not query else [query],
                additional_info=extra_info,
            )
            if (request.POST['action'] == 'delete_selected' and action) or request.POST[
                'action'
            ] != 'delete_selected':
                log.save()
        return result

    def change_view(self, request, object_id, form_url='', extra_context=None):
        result = super(LoggerMixin, self).change_view(
            request, object_id, form_url, extra_context
        )
        context_data = getattr(result, 'context_data', None)

        if (
            (
                context_data
                and len(context_data['errors']) == 0
                and request.method == 'POST'
            )
            or not context_data
            and request.method == 'POST'
        ):
            extra_info = {}
            for r in list(request.environ.keys()):
                if type(request.environ[r]) in (int, str, str):
                    extra_info[r] = request.environ[r]
            log = Log(
                user=request.user,
                logger_source=Source.LOGGER_ADMIN,
                action='change_view',
                model=self.model._meta.label,
                model_id=object_id,
                url=request.path,
                data=dict(list(request.POST.items())),
                additional_info=extra_info,
            )
            log.save()

        return result

    def add_view(self, request, form_url='', extra_context=None):
        result = super(LoggerMixin, self).add_view(request, form_url, extra_context)
        if request.method == 'POST':
            extra_info = {}
            for r in list(request.environ.keys()):
                if type(request.environ[r]) in (int, str, str):
                    extra_info[r] = request.environ[r]
            log = Log(
                user=request.user,
                logger_source=Source.LOGGER_ADMIN,
                action='add_view',
                model=self.model._meta.label,
                model_id=None,
                url=request.path,
                data=dict(list(request.POST.items())),
                additional_info=extra_info,
            )
            log.save()
        return result


class LogAdmin(admin.ModelAdmin):
    formfield_overrides = {fields.JSONField: {'widget': JSONEditorWidget}}
    list_filter = ('logger_source', 'action', 'model')
    search_fields = ('user__id', 'model', 'model_id')
    list_display = (
        'pk',
        'user',
        'logger_source',
        'action',
        'model',
        'model_id',
        'url',
        'date',
    )
    raw_id_fields = ('user',)


# admin.site.register(Log, LogAdmin)
