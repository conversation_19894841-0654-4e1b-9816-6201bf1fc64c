from urllib.parse import urljoin

from django.conf import settings

import requests

from regions.constants import REGION_LANGAUGE_MAP
from regions.models import Region


def purge_cloudflare_cache():
    url = (
        'https://api.cloudflare.com/client/v4/zones/'
        + f'{settings.CLOUDFLARE_ZONE_ID}/purge_cache'
    )
    requests.post(
        url,
        headers={
            'Authorization': f'Bearer {settings.CLOUDFLARE_API_TOKEN}',
            'Content-Type': 'application/json',
        },
        json={'purge_everything': True},
    )


def purge_cloudflare_cache_by_prefixes():
    site_language_prefixes = []
    for region, languages in REGION_LANGAUGE_MAP.items():
        region_code = Region.objects.get(name=region).country.code.lower()
        for language in languages:
            site_language_prefixes.append(f'/{language}-{region_code}')

    url_to_purge = [
        urljoin(settings.SITE_URL, prefix).replace('https://', '')
        for prefix in site_language_prefixes
    ]
    url = (
        'https://api.cloudflare.com/client/v4/zones/'
        + f'{settings.CLOUDFLARE_ZONE_ID}/purge_cache'
    )
    response = requests.post(
        url,
        headers={
            'Authorization': f'Bearer {settings.CLOUDFLARE_API_TOKEN}',
            'Content-Type': 'application/json',
        },
        json={'prefixes': url_to_purge},
    )
    response.raise_for_status()
