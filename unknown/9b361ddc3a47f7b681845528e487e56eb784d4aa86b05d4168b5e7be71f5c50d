import pytest

from pytest_cases import parametrize_with_cases

from custom.enums import ShelfType
from feeds.models import FeedItem
from feeds.tasks import sync_furniture_with_categories
from gallery.enums import FurnitureCategory


class ShouldSyncFeedsWithCategoriesCases:
    def case_watty(
        self,
        watty_factory,
        feeds_category_factory,
        catalogue_entry_factory,
        feeds_item_factory,
    ):
        # Assign
        chest_category = FurnitureCategory.CHEST
        chest, chest_without_feed = watty_factory.create_batch(
            2, shelf_category=chest_category, shelf_type=ShelfType.TYPE13
        )
        feed_category = feeds_category_factory(
            auto_furniture_sync=True, furniture_category=chest_category
        )
        # Feed item doesn't exist for this catalogue entry
        new = catalogue_entry_factory(
            furniture=chest_without_feed, category=chest_category
        )
        new_feed_furniture_id = FeedItem.get_feed_furniture_id(
            furniture=new.furniture, feed_category=feed_category
        )
        # There is existing catalogue entry
        catalogue_entry_factory(furniture=chest, category=chest_category)
        existing = feeds_item_factory(furniture=chest, category=feed_category)
        # There is no corresponding catalogue entry
        deleted = feeds_item_factory(category=feed_category)

        return deleted, existing, new_feed_furniture_id

    def case_sotty(
        self,
        sotty_factory,
        feeds_category_factory,
        catalogue_entry_factory,
        feeds_item_factory,
    ):
        sotty_category = FurnitureCategory.CHAISE_LONGUE
        sotty, sotty_without_feed = sotty_factory.create_batch(
            2, shelf_category=sotty_category
        )
        feed_category = feeds_category_factory(
            auto_furniture_sync=True, furniture_category=sotty_category
        )
        new = catalogue_entry_factory(
            furniture=sotty_without_feed, category=sotty_category
        )
        new_feed_furniture_id = FeedItem.get_feed_furniture_id(
            furniture=new.furniture, feed_category=feed_category
        )
        catalogue_entry_factory(furniture=sotty, category=sotty_category)
        existing = feeds_item_factory(furniture=sotty, category=feed_category)
        deleted = feeds_item_factory(category=feed_category)
        return deleted, existing, new_feed_furniture_id


@pytest.mark.django_db
class TestSyncFurnitureWithCategories:
    @parametrize_with_cases(
        'deleted, existing, new_feed_furniture_id',
        cases=ShouldSyncFeedsWithCategoriesCases,
    )
    def test_should_sync_feeds_with_categories(
        self, deleted, existing, new_feed_furniture_id
    ):
        sync_furniture_with_categories()

        assert not FeedItem.objects.filter(id=deleted.id).exists()
        assert FeedItem.objects.filter(id=existing.id).exists()
        assert FeedItem.objects.filter(
            furniture_category_id=new_feed_furniture_id
        ).exists()

    @pytest.mark.parametrize(
        'furniture_factory_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory'],
    )
    def test_should_sync_feeds_with_attributes(
        self,
        furniture_factory_name,
        feeds_category_factory,
        catalogue_entry_factory,
        feeds_item_factory,
        request,
    ):
        furniture = request.getfixturevalue(furniture_factory_name)()

        tylko_tag = 'tylko'
        feed_category = feeds_category_factory(
            auto_furniture_sync=True, furniture_category='', furniture_tag=(tylko_tag,)
        )
        # Feed item doesn't exist for this catalogue entry
        new = catalogue_entry_factory(attributes=(tylko_tag,))
        new_feed_furniture_id = FeedItem.get_feed_furniture_id(
            furniture=new.furniture, feed_category=feed_category
        )
        # There is existing catalogue entry
        catalogue_entry_factory(furniture=furniture, attributes=(tylko_tag,))
        existing = feeds_item_factory(furniture=furniture, category=feed_category)
        # There is no corresponding catalogue entry
        deleted = feeds_item_factory(category=feed_category)

        # Act
        sync_furniture_with_categories()

        # Assert
        assert not FeedItem.objects.filter(id=deleted.id).exists()
        assert FeedItem.objects.filter(id=existing.id).exists()
        assert FeedItem.objects.filter(
            furniture_category_id=new_feed_furniture_id
        ).exists()
